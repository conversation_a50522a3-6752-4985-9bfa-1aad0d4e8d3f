<template>
  <el-form class="base-width" ref="form" :model="form" :rules="rules" label-width="104px" size="small">
    <el-form-item label="所属应用" prop="appKey">
      <span v-if="isView">{{ form.appName }}</span>
      <el-select v-model="form.appKey" filterable placeholder="请选择" :disabled="isEdit" :appLoading="appLoading" style="width: 100%" @change="handleAppChange" v-else>
        <el-option v-for="item in appOptions" :key="item.appKey" :label="item.appLabel" :value="item.appKey"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="配置编码" prop="interfaceCode">
      <span v-if="isView">{{ form.interfaceCode }}</span>
      <el-input v-model="form.interfaceCode" placeholder="请输入" clearable :disabled="isEdit" v-else></el-input>
    </el-form-item>
    <el-form-item label="配置名称" prop="interfaceName">
      <span v-if="isView">{{ form.interfaceName }}</span>
      <el-input v-model="form.interfaceName" placeholder="请输入" clearable :disabled="isEdit" v-else></el-input>
    </el-form-item>
    <el-form-item label="描述">
      <span v-if="isView">{{ form.interfaceDesc }}</span>
      <el-input v-model="form.interfaceDesc" placeholder="请输入" clearable type="textarea" :rows="4" v-else></el-input>
    </el-form-item>
    <el-form-item label="负责/维护人" prop="owners">
      <span v-if="isView">{{ form.owners.map(item => item.staffName).join('；') }}</span>
      <sdc-staff-selector v-model="staffList" multiple clearable size="small" ref="staffSelector" modalClass="bids-sdc-modal--fix" placeholder="请选择" @change="handleOwnersChange" v-else />
    </el-form-item>
  </el-form>
</template>

<script>
import { queryApps } from 'services/common.service'

export default {
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        appKey: '',
        appName: '',
        interfaceCode: '',
        interfaceName: '',
        interfaceDesc: '',
        owners: [],
        jobType: 'FLINK',
        jobExecMode: 'BATCH', // BATCH 批 STREAMING 流式
        integrationMode: 'SIMPLE' // SIMPLE 简单 EXPERT 专家
      },
      rules: {
        appKey: [
          { required: true, message: '请选择所属应用', trigger: 'change' }
        ],
        interfaceCode: [
          { required: true, message: '请输入配置编码', trigger: 'change' }
        ],
        interfaceName: [
          { required: true, message: '请输入配置名称', trigger: 'change' }
        ],
        owners: [
          { required: true, message: '请选择负责/维护人', trigger: 'change' }
        ]
      },
      staffList: [],
      appOptions: [],
      appLoading: false
    }
  },
  methods: {
    fetchData() {
      this.appLoading = true
      queryApps().then(res => {
        this.appOptions = res.map(item => ({
          appKey: item.name,
          appName: item.label,
          appLabel: item.name + '（' + item.label + '）'
        }))
      }).catch(res => {
        this.appOptions = []
        this.$message.error('获取应用下拉列表失败！原因：' + res.message || '暂无')
      }).finally(() => {
        this.appLoading = false
      })
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    handleAppChange(val) {
      this.form.appName = this.appOptions.find(item => item.appKey === val).appName
    },
    handleOwnersChange(value) {
      this.form.owners = value.map(item => ({
        staffId: item.StaffId,
        staffName: item.StaffName
      }))
      if (value.length) {
        this.$refs.form.clearValidate()
      }
    },
    setData(data) {
      this.form = {
        appKey: data.appKey,
        appName: data.appName,
        interfaceCode: data.interfaceCode,
        interfaceName: data.interfaceName,
        interfaceDesc: data.interfaceDesc,
        owners: data.owners,
        jobType: 'FLINK',
        jobExecMode: 'BATCH', // BATCH 批 STREAMING 流式
        integrationMode: 'SIMPLE' // SIMPLE 简单 EXPERT 专家
      }
    },
    getSubData() {
      return {
        appKey: this.form.appKey,
        appName: this.form.appName,
        interfaceCode: this.form.interfaceCode,
        interfaceName: this.form.interfaceName,
        interfaceDesc: this.form.interfaceDesc,
        owners: this.form.owners,
        jobType: 'FLINK',
        jobExecMode: 'BATCH', // BATCH 批 STREAMING 流式
        integrationMode: 'SIMPLE' // SIMPLE 简单 EXPERT 专家
      }
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

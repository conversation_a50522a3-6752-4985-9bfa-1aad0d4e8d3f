import { http } from 'utils/http'
import api from 'config/api.conf'

export const querySimpleConfigList = (data) => http.post(api.bidsplus_config.simple.querySimpleConfigList, { params: data })
export const querySimpleConfigVersions = (params) => http.get(api.bidsplus_config.simple.querySimpleConfigVersions, { params, loading: true })
export const releaseSimpleConfig = ({ id, releaseState }) => http.post(api.bidsplus_config.simple.releaseSimpleConfig.replace('{id}', id).replace('{releaseState}', releaseState), { loading: true })
export const saveSimpleConfig = (params) => http.post(api.bidsplus_config.simple.saveSimpleConfig, { params, loading: true })
export const rollbackSimpleConfig = ({ id, version }) => http.post(api.bidsplus_config.simple.rollbackSimpleConfig.replace('{id}', id).replace('{version}', version), { loading: true })
// 该接口主要用来修改线上版本的运行配置如运行参数，调度周期，超时配置，重试配置，该修改不会创建新的版本；
// 运行配置弹窗2部分
export const updateOnlineSimpleConfig = (params) => http.post(api.bidsplus_config.simple.updateOnlineSimpleConfig, { params, loading: true })
export const querySimpleConfigInfo = (params) => http.get(api.bidsplus_config.simple.querySimpleConfigInfo, { params, loading: true })
export const deleteSimpleConfig = ({ id }) => http.del(api.bidsplus_config.simple.deleteSimpleConfig.replace('{id}', id), { loading: true })

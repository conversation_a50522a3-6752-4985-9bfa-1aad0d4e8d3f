<template>
  <div class="sync-deatail-dialog">
    <el-dialog title="同步详情" :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" width="857px" top="6vh">
      <section>
        <bids-block-header title="数据源信息" style="margin-bottom: 16px;"></bids-block-header>
        <el-table :data="tableData" style="width: 100%" :header-cell-style="{ background: '#f5f7f9', height: '48px' }">
          <el-table-column prop="prop" width="138"></el-table-column>
          <el-table-column prop="col1" label="来源环境TEST" min-width="338"></el-table-column>
          <el-table-column prop="col2" label="目标环境UAT" min-width="338"></el-table-column>
        </el-table>
      </section>
      <section>
        <bids-block-header title="字段信息" style="margin-bottom: 16px;"></bids-block-header>
        <el-table :data="tableData1" style="width: 100%" :header-cell-style="{ background: '#f5f7f9', height: '48px' }">
          <el-table-column prop="prop" width="138"></el-table-column>
          <el-table-column prop="col1" label="来源环境TEST" min-width="338"></el-table-column>
          <el-table-column prop="col2" label="目标环境UAT" min-width="338"></el-table-column>
        </el-table>
      </section>
      <section>
        <bids-block-header title="前置事件信息" style="margin-bottom: 16px;"></bids-block-header>
        <el-table :data="tableData2" style="width: 100%" :header-cell-style="{ background: '#f5f7f9', height: '48px' }">
          <el-table-column prop="prop" width="138"></el-table-column>
          <el-table-column prop="col1" label="来源环境TEST" min-width="338"></el-table-column>
          <el-table-column prop="col2" label="目标环境UAT" min-width="338"></el-table-column>
        </el-table>
      </section>
      <section>
        <bids-block-header title="后置事件信息" style="margin-bottom: 16px;"></bids-block-header>
        <el-table :data="tableData3" style="width: 100%" :header-cell-style="{ background: '#f5f7f9', height: '48px' }">
          <el-table-column prop="prop" width="138"></el-table-column>
          <el-table-column prop="col1" label="来源环境TEST" min-width="338"></el-table-column>
          <el-table-column prop="col2" label="目标环境UAT" min-width="338"></el-table-column>
        </el-table>
      </section>
    </el-dialog>
  </div>
</template>

<script>
import { BidsBlockHeader } from 'components'

export default {
  name: 'sync-deatail-dialog',
  components: {
    BidsBlockHeader
  },
  data() {
    return {
      type: 'publish',
      visible: false,
      tableData: [
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' },
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' },
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' }
      ],
      tableData1: [
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' },
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' },
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' }
      ],
      tableData2: [
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' },
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' },
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' }
      ],
      tableData3: [
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' },
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' },
        { prop: '字段1', col1: '来源环境TEST', col2: '目标环境UAT' }
      ]
    }
  },
  methods: {
    show() {
      this.fetchData()
      this.visible = true
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    fetchData() {}
  }
}
</script>

<style lang="less" scoped>
.sync-deatail-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }
  section {
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-02 15:59:55
 */
const domainKey = window.location.host.indexOf('woa') !== -1 ? 'woa' : 'oa'
export default {
  loc: {
    api: {
      main: `http://dev.ntsgw.${domainKey}.com/api/sso/hr-bidsplus-config`,
      flink: `http://demo.ntsgw.${domainKey}.com/api/pub/bidsplus-executor/bidsplus`,
      scheduler: `http://dev.ntsgw.${domainKey}.com/dolphinscheduler/bids`,
      esb: `http://dev.ntsgw.${domainKey}.com/api/esb/hr-bidsplus-config`,
      frontRepo: `http://dev.ntsgw.${domainKey}.com/api/sso/bidsplus-frontrepo`
    },
    esb: {
      main: `http://dev.ntsgw.woa.com/api/esb/hr-bidsplus-config`
    }
  },
  dev: {
    api: {
      main: `http://dev.ntsgw.${domainKey}.com/api/sso/hr-bidsplus-config`,
      flink: `http://demo.ntsgw.${domainKey}.com/api/pub/bidsplus-executor/bidsplus`,
      scheduler: `http://dev.ntsgw.${domainKey}.com/dolphinscheduler/bids`,
      esb: `http://dev.ntsgw.${domainKey}.com/api/esb/hr-bidsplus-config`,
      frontRepo: `http://dev.ntsgw.${domainKey}.com/api/sso/bidsplus-frontrepo`
    },
    esb: {
      main: `http://dev.ntsgw.woa.com/api/esb/hr-bidsplus-config`
    }
  },
  uat: {
    api: {
      main: `http://uat-ntsgw.${domainKey}.com/api/sso/hr-bidsplus-config`,
      flink: `http://uat-ntsgw.${domainKey}.com/api/pub/bidsplus-executor/bidsplus`,
      scheduler: `http://uat-ntsgw.${domainKey}.com/dolphinscheduler/bids`,
      esb: `http://uat-ntsgw.${domainKey}.com/api/esb/hr-bidsplus-config`,
      frontRepo: `http://uat-ntsgw.${domainKey}.com/api/sso/bidsplus-frontrepo`
    },
    esb: {
      main: `http://uat-ntsgw.woa.com/api/esb/hr-bidsplus-config`
    }
  },
  prd: {
    api: {
      main: `http://ntsgw.${domainKey}.com/api/sso/hr-bidsplus-config`,
      flink: `http://ntsgw.${domainKey}.com/api/pub/bidsplus-executor/bidsplus`,
      scheduler: `http://ntsgw.${domainKey}.com/dolphinscheduler/bids`,
      esb: `http://ntsgw.${domainKey}.com/api/esb/hr-bidsplus-config`,
      frontRepo: `http://ntsgw.${domainKey}.com/api/sso/bidsplus-frontrepo`
    },
    esb: {
      main: `http://ntsgw.woa.com/api/esb/hr-bidsplus-config`
    }
  }
}

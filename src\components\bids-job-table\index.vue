<!--
 * @Description: 发布/订阅实例表格组件，用于展示和操作实例
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-24 16:18:34
-->
<template>
  <div class="bids-job-table">
    <el-table
      v-if="visible"
      :data="tableData"
      ref="jobTable"
      v-loading="loading"
      height="600"
      :empty-text="emptyText"
      :header-cell-style="{'border-top': '1px solid #EBEEF5' , height: '60px'}"
      style="width: 100%">
      <template v-if="formatterCollectType(initData) === '全量'">
        <el-table-column
          v-for="item in jobTableColumn.schedulePart" :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.minWidth || item.width"
          :show-overflow-tooltip="true"
          :formatter="item.formatter"
          align="center">
          <template v-if="item.prop === 'scheduleStatus'" v-slot="{ row }">
            <el-link type="primary" :underline="false" @click="handleGoTab(row)" v-if="row.scheduleStatus === '运行中'">{{ row.scheduleStatus }}</el-link>
            <span v-else>{{ row.scheduleStatus }}</span>
          </template>
        </el-table-column>
      </template>
      <template v-else>
        <el-table-column
          v-for="item in jobTableColumn.taskPart" :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.minWidth || item.width"
          :show-overflow-tooltip="true"
          :formatter="item.formatter"
          align="center">
          <template v-if="item.prop === 'executorStatus'" v-slot="{ row }">
            <el-link type="primary" :underline="false" @click="handleGoTab(row)" v-if="row.executorStatus === 'RUNNING'">{{ dt_executorState[row.executorStatus] }}</el-link>
            <span v-else>{{ dt_executorState[row.executorStatus] }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        fixed="right"
        label="查看"
        align="center"
        width="150">
        <template slot-scope="scope">
          <el-button type="text" @click="handleShowTaskLog(scope.row)">运行日志</el-button>
          <el-button type="text" @click="handleShowCollectData(scope.row)" :disabled="initData.jobCategory === 'SUB' || initData.collectType === 2" :loading="scope.row['btnLoading']">数据行数</el-button>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        align="center"
        :width="formatterCollectType(initData) === '增量' ? 230 : 150">
        <template v-slot="{ row }">
          <template v-if="formatterCollectType(initData) === '增量'">
            <el-button type="text" @click="handleClick('run', handleRunJob, row)" :loading="row['btnLoading']" :disabled="!editableAuthority || !['NOJOB', 'KILLED', 'FAILED', 'SUCCEEDED'].includes(row.executorStatus)">启动</el-button>
            <el-button type="text" @click="handleClick('pause', handlePause, row)" :loading="row['btnLoading']" :disabled="!editableAuthority || row.executorStatus !== 'RUNNING'">暂停</el-button>
            <el-button type="text" @click="handleClick('resume', handleResume, row)" :loading="row['btnLoading']" :disabled="!editableAuthority || !['KILLED', 'FAILED', 'SUCCEEDED'].includes(row.executorStatus)">恢复</el-button>
            <el-button type="text" @click="handleClick('kill', handleKill, row)" :loading="row['btnLoading']" :disabled="!editableAuthority || ['NOJOB'].includes(row.executorStatus)">停止</el-button>
          </template>
          <template v-if="formatterCollectType(initData) === '全量'">
            <el-button type="text" @click="handleOperate('停止', handleKill, row)" :loading="row['btnLoading']" :disabled="!editableAuthority || row.scheduleStatus !== '运行中'">停止</el-button>
            <el-button type="text" :disabled="!editableAuthority || ['运行中', '已停止'].includes(row.scheduleStatus)" @click="handleOperate('重跑', handleRestart, row)" :loading="row['btnLoading']">重跑</el-button>
          </template>
          <!-- <el-button type="text" @click="handleOperate('暂停', handlePause, scope.row)" :loading="scope.row['btnLoading']">暂停</el-button>
          <el-button type="text" @click="handleOperate('恢复', handleResume, scope.row)" :loading="scope.row['btnLoading']">恢复</el-button> -->
          <!-- <el-button type="text" @click="handleOperate('停止', handleKill, scope.row)" :loading="scope.row['btnLoading']" 
            v-if="initData.collectType === 1" :disabled="scope.row.scheduleStatus !== '运行中'">停止</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="recordCount">
      </el-pagination>
    </div>
    <bids-log-dialog ref="logDialog" :label="dialogLabel"></bids-log-dialog>
  </div>
</template>
<script>
import { pagination, formatter } from 'mixins'
import BidsLogDialog from 'components/bids-log-dialog'
import { runJob, pauseJob, resumeJob, restartJob, killJob, queryJobState, queryJobTableDataCount, queryInstances } from 'services/common.service'
import { queryJob } from '@/services/subscribe.service.js'
import { mapState } from 'vuex'

export default {
  name: 'bids-job-table',
  mixins: [pagination, formatter],
  components: {
    BidsLogDialog
  },
  props: {
    initData: {
      type: Object,
      default: () => {} // { configId, version, collectType, type, storageStrategy(仅发布) }
    },
    editableAuthority: { // 有无按钮的权限
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapState(['dt_scheduleState', 'dt_executorState']),
    emptyText() {
      return this.initData.storageStrategy === 'NOT_SAVE' ? '落地策略为直连源库，没有任务实例' : '暂无数据'
    }
  },
  data() {
    return {
      tableData: [],
      loading: false,
      recordCount: 0,
      dialogLabel: '',
      paginationType: 'remote', // remote/local
      jobTableColumn: {
        schedulePart: [
          // { prop: 'version', label: '数据版本', minWidth: '80' },
          { prop: 'scheduleInstanceId', label: '调度实例ID', minWidth: '100' },
          { prop: 'scheduleStatus', label: '运行状态', minWidth: '100' },
          { prop: 'schedulePlanedTime', label: '调度周期', minWidth: '170' }, // all 调度计划时间 increase 调度周期
          { prop: 'scheduleBeginTime', label: '调度开始时间', minWidth: '170' },
          { prop: 'scheduleEndTime', label: '调度结束时间', minWidth: '170' }
        ],
        taskPart: [
          { prop: 'executorJobName', label: '运行任务ID' },
          { prop: 'executorStatus', label: '任务状态' },
          { prop: 'executorBeginTime', label: '任务开始时间' },
          { prop: 'executorEndTime', label: '任务结束时间', formatter: ({ executorEndTime }) => executorEndTime && (new Date(executorEndTime).getTime() > 0 ? executorEndTime : '') }
        ]
      },
      visible: true
    }
  },
  watch: {
    'initData.version'(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        this.pageIndex = 1
        this.initData.storageStrategy === 'NOT_SAVE' ? this.tableData = [] : this.fetchData()
      }
    }
  },
  methods: {
    handleShowTaskLog(row) {
      this.dialogLabel = '运行任务ID'
      this.$refs.logDialog.show({ jobName: row.executorJobName, jobInstId: row.scheduleInstanceId })
    },
    handleShowCollectData(row) {
      const params = { jobName: row.executorJobName }
      row.btnLoading = true
      queryJobTableDataCount(params).then(res => {
        const num = res || 0
        row.btnLoading = false
        this.$alert(`本次运行任务共得到${num}行数据。`, '数据行数', {
          confirmButtonText: '确定'
        })
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        row.btnLoading = false
      })
    },
    handleOperate(optType, optFunc, row) {
      this.$confirm('此操作将' + optType + '该任务实例，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        optFunc(row)
      }).catch(() => {})
    },
    handleKill(row) {
      // 停止任务
      const params = { jobName: row.executorJobName }
      row.btnLoading = true
      killJob(params).then(res => {
        this.$message.success('操作成功！')
        row.scheduleStatus = this.dt_jobState[res.state] || res.state
        row.executorStatus = res.state
        row.btnLoading = false
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        row.btnLoading = false
      })
    },
    handlePause(row) {
      const params = { jobName: row.executorJobName }
      row.btnLoading = true
      pauseJob(params).then(res => {
        this.$message.success('操作成功！')
        row.btnLoading = false
        // row.executorStatus = this.dt_jobState[res.state] || res.state
        row.executorStatus = 'SUCCEEDED'
      }).catch(res => {
        const text = res.code === 'ECONNABORTED' ? '暂停返回超时，请稍后查看任务状态' : '暂停失败，请稍后再试或停止任务'
        this.$message.error(text)
        row.btnLoading = false
      })
    },
    handleResume(row) {
      const params = { jobName: row.executorJobName }
      row.btnLoading = true
      resumeJob(params).then(res => {
        this.$message.success('操作成功！')
        row.btnLoading = false
        // row.scheduleStatus = this.dt_jobState[res.state] || res.state
        row.executorStatus = res.state
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        row.btnLoading = false
      })
    },
    handleRestart(row) {
      const params = { jobInstId: row.scheduleInstanceId }
      const postData = { type: 'restart' }
      row.btnLoading = true
      restartJob(params, postData).then(res => {
        this.$message.success('操作成功！')
        row.btnLoading = false
        row.scheduleStatus = this.dt_scheduleState[1]
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        row.btnLoading = false
      })
    },
    handleRefresh(row) {
      const data = [{ jobName: row.executorJobName }]
      row.btnLoading = true
      queryJobState(data).then(res => {
        row.btnLoading = false
        const item = res[0]
        row.executorStatus = item.state
      }).catch(res => {
        row.btnLoading = false
        this.$message.error('操作失败！原因：' + res.message || '暂无')
      })
    },
    handleRunJob(row) {
      const params = { jobName: row.executorJobName }
      row.btnLoading = true
      runJob(params).then(res => {
        this.$message.success('操作成功！')
        row.btnLoading = false
        row.executorStatus = res.state
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        row.btnLoading = false
      })
    },
    fetchData() {
      const params = { configId: this.initData.configId, version: this.initData.version }
      const postData = { criteria: [], paginator: { pageSize: this.pageSize, pageIndex: this.pageIndex } }
      this.loading = true
      queryInstances(params, postData).then(res => {
        this.tableData = res.content ? res.content.map(item => ({ btnLoading: false, ...item })) : []
        this.recordCount = res.paginator?.recordCount || 0
        this.loading = false
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('获取数据（' + this.initData.version + '版本）的任务情况失败！原因：' + res.message || '暂无')
        this.loading = false
      }).finally(() => {
        this.visible = false
        this.$nextTick(() => {
          this.visible = true
        })
      })
    },
    handleGoTab(row) {
      const { executorJobName } = row
      const params = { executorJobName }
      queryJob(params).then(res => {
        if (res) {
          const { trackingUrl } = res
          const url = `${trackingUrl}#/overview`
          window.open(url, '_blank')
        } else {
          this.$message.error('操作失败！原因：任务不存在')
        }
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
      })
    },
    handleClick(optType, optFunc, row) {
      const textMap = {
        run: '是否运行任务(不从savepoint恢复)',
        pause: '是否暂停任务并保存savepoint',
        resume: '是否从上一次savepoint恢复任务',
        kill: '是否停止任务(不保存savepoint)'
      }
      this.$confirm(textMap[optType], '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        optFunc(row)
      }).catch(() => {})
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/bids-job-table.less";
</style>

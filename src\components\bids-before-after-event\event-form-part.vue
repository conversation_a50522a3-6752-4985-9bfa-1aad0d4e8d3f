<!--
 * @Description: 前/后置事件组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-03 16:41:52
-->
<template>
  <div class="event-form-part">
    <el-form :model="form" size="small" :rules="rules" ref="form" :disabled="!moderateEditable" label-width="100px">
      <el-form-item label="插件类型">
        <el-select v-model="form.eventPlugin" placeholder="请选择" @change="handlePluginTypeChange" clearable v-if="mildEditable">
          <el-option v-for="item in pluginTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <span v-else>{{pluginTypeOptions.find(item => item.value === form.eventPlugin)?.label}}</span>
      </el-form-item>
      <el-form-item label="对应数据源" prop="jdbc_dsid" v-if="form.eventPlugin == 'Sql'">
        <el-select v-model="form.jdbc_dsid" @change="handleDsIdChange" :disabled="!moderateEditable" :loading="dsLoading" v-if="mildEditable">
          <el-option v-for="item in dataSourceOptions" :key="item.dsId" :label="getDsLabel(item)" :value="item.dsId"></el-option>
        </el-select>
        <span v-else>{{getDsLabel(this.sqlDsOptions[0])}}</span>
        <span v-if="moderateEditable">
          <el-button style="margin-left:20px;" @click="handleAddDataSource">新增数据源</el-button>
          <el-button style="margin-left:10px;" @click="handleTestConnect" :loading="btnConnLoading">测试连接</el-button>
        </span>
      </el-form-item>
      <el-form-item :label="instructionLabel" :required="form.eventPlugin !== ''" prop="instruction" :show-message="false">
        <!-- <codemirror ref="cmEditor" :value="form.instruction" :options="{readOnly: !moderateEditable || !form.eventPlugin, hintOptions: hintOptions}"
          @input="handleInstructionChange" @ready="handleCmReady" @blur="handleBlur"/> -->
        <codemirror ref="cmEditor" :value="form.instruction" :options="{readOnly: !moderateEditable || !form.eventPlugin, hintOptions: hintOptions}"
          @input="handleInstructionChange" @ready="handleCmReady"/>
      </el-form-item>
    </el-form>
    <bids-datasource-dialog ref="addDatasouceDialog" type="add" :options="addDsOptions" @submit="handleSubmitDs"/>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import { editable, sqlExpressions } from 'mixins'
import sqlFormatter from 'utils/sql-formatter/sqlFormatter'
import { testDataSourceById } from 'services/common.service'
import BidsDatasourceDialog from 'components/bids-datasource-dialog'

export default {
  name: 'event-form-part',
  components: {
    BidsDatasourceDialog
  },
  mixins: [editable, sqlExpressions],
  props: {
    type: String, // beforeEvent | afterEvent
    pageStatus: String
  },
  data() {
    const validateInstruction = (rule, value, callback) => {
      if (this.form.eventPlugin && !value) {
        callback(new Error('请填写' + this.instructionLabel))
      } else {
        callback()
      } 
    }
    return {
      configType: 'subscribe',
      form: {},
      pluginTypeOptions: [
        { value: 'Sql', label: 'SQL' }
      ],
      instructionKeys: { Sql: 'jdbc_sql' },
      expressionColumn: [{ prop: 'expression', label: '支持的表达式', width: '300' }, { prop: 'explain', label: '说明', width: '150' }],
      dsLoading: false,
      btnConnLoading: false,
      sqlDsOptions: [],
      rules: {
        jdbc_dsid: [{ required: true, message: '请选择对应数据源' }],
        instruction: [{ validator: validateInstruction }]
      },
      addDsOptions: [
        { value: 'ESjdbc', label: 'ESjdbc' }, { value: 'HDFS', label: 'HDFS' }, { value: 'Hive', label: 'Hive' },
        { value: 'MySQL', label: 'MySQL' }, { value: 'Oracle', label: 'Oracle' }, { value: 'PostgreSQL', label: 'PostgreSQL' },
        { value: 'FrontRepo', label: 'FrontRepo(前置仓)' }, { value: 'SQLServer', label: 'SQLServer' }, { value: 'MySqlTenant', label: 'MySqlTenant' },
        { value: 'clickhouse', label: 'clickhouse' }, { value: 'ApacheIgnite', label: 'ApacheIgnite' }, { value: 'starrocks', label: 'starrocks' }
      ]
    }
  },
  computed: {
    ...mapState({
      configData: 'currentSubscribeConfig',
      dt_dsConnector: 'dt_dsConnector'
    }),
    instructionLabel() {
      if (!this.form.eventPlugin) return '待定属性'
      const map = { Sql: 'SQL语句' }
      return map[this.form.eventPlugin]
    },
    dataSourceOptions() {
      return this.configData.cache.dataSourceOptions ? this.configData.cache.dataSourceOptions.filter(item => ['jdbc', 'starrocks'].includes(this.dt_dsConnector[item.dsType])) : this.sqlDsOptions
    }
  },
  created() {
    this.updatePageData()
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    updatePageData() {  
      if (!this.configData.job[this.type]) {
        this.UPDATE_CONFIG_ITEM({ type: 'subscribe', keyPath: ['job', this.type], value: { eventPlugin: '', eventDesc: '', eventProperties: {} } })
      }
      const eventPlugin = this.configData.job[this.type].eventPlugin || ''
      const eventProperties = this.configData.job[this.type].eventProperties
      const instruction = eventProperties[this.instructionKeys[eventPlugin]] || ''
      if (eventPlugin === 'Sql') {
        const { jdbc_dsid, dsType, dsName, dsUserName } = eventProperties
        if (jdbc_dsid && dsName && dsType) {
          this.sqlDsOptions = [{ dsId: jdbc_dsid, dsName, dsType, dsProperties: { dsUserName } }]
        }
      }
      this.form = { eventPlugin, instruction, ...eventProperties }
    },
    doLayout() {
      this.$refs['cmEditor'] && this.$refs['cmEditor'].refresh()
    },
    getDsLabel(item) {
      if (!item) return ''
      let label = '(' + item.dsType + ')' + item.dsName
      if (item.dsProperties && item.dsProperties.dsUserName && this.mildEditable) {
        label = label + ' [' + item.dsProperties.dsUserName + ']'
      }
      return label
    },
    handlePluginTypeChange(val) {
      if (!val) {
        this.UPDATE_CONFIG_ITEM({ type: 'subscribe', keyPath: ['job', this.type], value: { eventPlugin: '', eventDesc: '', eventProperties: {} } })
        this.form = { instruction: '', eventPlugin: '' }
        return
      }
      const payload = { type: 'subscribe', keyPath: ['job', this.type, 'eventPlugin'], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
      if (val === 'Sql') {
        const dsId = this.configData.job.sink.properties.dsId
        if (!this.form.jdbc_dsid && dsId && this.dataSourceOptions.some(item => item.dsId === dsId)) {
          this.$set(this.form, 'jdbc_dsid', dsId)
          this.handleDsIdChange(this.form.jdbc_dsid)
        }
      } 
    },
    handleDsIdChange(val) {
      const ds = this.dataSourceOptions.find(item => item.dsId === val)
      const properties = { jdbc_dsid: val, dsType: ds.dsType, dsName: ds.dsName, dsUserName: ds.dsProperties.dsUserName }
      const keys = ['jdbc_dsid', 'dsType', 'dsName', 'dsUserName']
      keys.forEach(item => {
        let payload = { type: 'subscribe', keyPath: ['job', this.type, 'eventProperties', item], value: properties[item] }
        this.UPDATE_CONFIG_ITEM(payload)
      })
    },
    // updateDataValue(value, keyPath) {
    //   const payload = { type: 'subscribe', keyPath, value }
    //   this.UPDATE_CONFIG_ITEM(payload)
    // },
    handleInstructionChange(value) {
      this.form.instruction = value
      const insKey = this.instructionKeys[this.form.eventPlugin]
      if (!insKey) return
      const payload = { type: 'subscribe', keyPath: ['job', this.type, 'eventProperties', insKey], value }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleBlur() {
      const sqlText = sqlFormatter.format(this.form.instruction)
      if (this.form.instruction !== sqlText) {
        this.handleInstructionChange(sqlText)
      }
    },
    handleCmReady(cm) {
      cm.on('keypress', () => {
        if (this.moderateEditable && this.form.eventPlugin) cm.showHint()
      })
    },
    handleAddDataSource() {
      if (!this.configData.appKey) {
        this.$message.warning('请先选择一个应用！')
        return
      }
      const data = { dsAppKey: this.configData.appKey, dsAppName: this.configData.appName, dsNameList: this.dataSourceOptions.map(item => item.dsName) }
      this.$refs.addDatasouceDialog.show(data)
    },
    handleSubmitDs(option) {
      // 新增数据源成功后, 加入下拉列表
      if (option) {
        const options = [...this.dataSourceOptions, option]
        const payload = { type: 'subscribe', keyPath: ['cache', 'dataSourceOptions'], value: options }
        this.UPDATE_CONFIG_ITEM(payload)
        this.form.dsId = option.dsId
        this.handleDsIdChange(option.dsId)
      }
    },
    handleTestConnect() {
      if (!this.form.jdbc_dsid) {
        this.$message.warning('请先选择一个数据源！')
        return
      }
      const params = { dsId: this.form.jdbc_dsid }
      this.btnConnLoading = true
      testDataSourceById(params).then(res => {
        this.$message.success('连接成功！')
        this.btnConnLoading = false
      }).catch(res => {
        this.$message.error('连接失败！原因：' + res.message || '暂无')
        this.btnConnLoading = false
      }) 
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    }
  }
}
</script>

import { Router<PERSON><PERSON><PERSON>, Container, Layout, Link, Loading, StaffSelector, Modal, Toast } from 'sdc-webui'
import locale from 'sdc-webui/lib/locale'
import zh from 'sdc-webui/lib/locale/lang/zh-CN'
import en from 'sdc-webui/lib/locale/lang/en-US'
import EventBus from 'utils/bus'

export default {
  components: [
    RouterView,
    Container,
    Layout,
    Link,
    StaffSelector,
    Modal
  ],
  install(Vue) {
    Vue.prototype.$sdc = {}
    Vue.prototype.$sdc.loading = Loading
    Vue.prototype.$sdc.loading.hide = Loading.hide
    Vue.prototype.$sdc.toast = Toast
    Vue.prototype.$bus = new EventBus()
  },
  locale,
  langs: {
    zh,
    en
  }
}

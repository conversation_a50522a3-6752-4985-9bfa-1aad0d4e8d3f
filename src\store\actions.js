import { Message } from 'element-ui'
import { APP_TYPES } from './types'
import { getWhiteList } from 'services/common.service'

export default {
  saveData (context, payload) {
    context.commit(APP_TYPES.SAVE_DATA, payload)
  },
  setSubWhiteList (context) {
    getWhiteList().then(res => {
      context.commit(APP_TYPES.SET_SUBWHITELIST, res)
    }).catch(err => {
      Message.error('提交失败！原因：' + err.message || '暂无')
    })
  }
}

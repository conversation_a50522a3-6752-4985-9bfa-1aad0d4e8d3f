/*
 * @Description: 基于组件树结构的事件流通信
 * @Autor: kenoxia
 * @LastEditTime: 2020-12-31 19:27:39
 */
function broadcast(functionName, root) {
  this.$children.forEach(child => {
    const func = child[functionName]
    if (func || child.$options['_componentTag'] === 'el-form') {
      if (func && !func()) {
        root.valid = false
      }
      broadcast.apply(child, [functionName, root])
    }
  })
}

export default {
  methods: {
    broadcast(functionName, root) {
      broadcast.call(this, functionName, root)
    }
  }
}

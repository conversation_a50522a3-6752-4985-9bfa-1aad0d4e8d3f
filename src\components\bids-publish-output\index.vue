<!--
 * @Description: 数据发布的输出
 * @Autor: kenoxia
 * @LastEditTime: 2021-07-29 18:45:03
-->
<template>
  <div class="bids-publish-output">
    <el-form :model="form" ref="form" size="small" :label-width="labelWidth" :rules="rules" :disabled="!mildEditable">
      <el-form-item label="落地策略" prop="storageStrategy">
        <el-select v-model="form.storageStrategy" :disabled="!strictEditable" @change="handleStorageStrategyChange" v-if="mildEditable">
          <el-option v-for="(item, index) in storageOptions" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <span v-else>{{form.storageStrategy ? storageOptions.find(item => item.value === form.storageStrategy).label : ''}}</span>
      </el-form-item>
      <el-form-item label="输出数据的处理规则" prop="script">
        <div class="codemirror-wrap">
          <el-tooltip content="格式化">
            <i class="el-icon-brush" @click="handleFormatter"></i>
          </el-tooltip>
          <codemirror ref="cmEditor" :value="form.script" :options="{readOnly: !moderateEditable, hintOptions: hintOptions}" @beforeChange="handleBeforeChange" @input="handleScriptChange" @ready="handleCmReady"/>
        </div>
      </el-form-item>
      <template v-if="kafkaSink">
        <el-form-item label="主题名称" prop="topic" required>
          <span>{{`${form.topic}（平台定义）`}}</span>
        </el-form-item>
        <el-form-item label="分区数" prop="partition">
          <el-input-number style="width:100px;" v-model="form.partition" size="mini" :min="1" :disabled="!strictEditable"
            :max="dt_system.maxPartitionNum" @change="(val) => { updateSinkDsProperties(val, 'partition') }" v-if="mildEditable"/>
          <span v-else>{{form.partition}}</span>
        </el-form-item>
        <el-form-item label="分区器" prop="partitioner">
          <el-select v-model="form.partitioner" :disabled="!strictEditable" @change="(val) => { updateSinkDsProperties(val, 'partitioner') }" v-if="mildEditable">
            <el-option v-for="item in partitionerOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <span v-else>{{form.partitioner ? partitionerOptions.find(item => item.value === form.partitioner).label : ''}}</span>
        </el-form-item>
      </template>
      <bids-datastruct-form-part ref="datastruct" type="sink" :page-status="pageStatus" :configType="configType" :init-data="currentDataSource" :label-width="labelWidth"></bids-datastruct-form-part>
    </el-form>
    <div class="description-panel" v-if="mildEditable">
      <div class="question">
        <i class="el-icon-question"></i>
        <span>落地策略</span>
      </div>
      <div class="answer">
        <div>暂存平台：平台会定时/持续采集数据并储存在本平台上，用于他人订阅；</div>
        <div>直连源库：仅当他人发起订阅任务时，平台才从源库采集数据，并直接输出到目标库中</div>
      </div>
      <div class="question">
        <i class="el-icon-question"></i>
        <span>输出数据的处理规则</span>
      </div>
      <div class="answer">
        <!-- <span>SQL SELECT语句，适用于需要将输入数据进行一定加工后再发布，表名请使用<strong>{{`PUB_${configData.appKey || '应用name'}_${configData.interfaceCode || '接口编码'}`}}</strong></span> -->
        <span>SQL SELECT语句，适用于需要将输入数据进行一定加工后再发布，表名请使用<strong>{{ tableName }}</strong></span>
      </div>
      <div class="question">
        <i class="el-icon-question"></i>
        <span>输出数据的结构</span>
      </div>
      <div class="answer">
        <span>需要和经过处理规则加工后的数据结构保持一致，平台提供一定的方式辅助定义数据的结构</span>
      </div>
      <div class="question">
        <i class="el-icon-question"></i>
        <span>脱敏规则</span>
      </div>
      <div class="answer">
        <span>对于勾选为加密的数据列，只会对原值进行加密，不会再进行脱敏</span>
      </div>
    </div>
  </div>
</template>
<script>
import { mapMutations, mapState } from 'vuex'
import { editable, sqlExpressions } from 'mixins'
import BidsDatastructFormPart from 'components/bids-datastruct-form-part'
import sqlFormatter from 'utils/sql-formatter/sqlFormatter'

export default {
  name: 'bids-publish-output',
  mixins: [editable, sqlExpressions], // 表单项是否可编辑
  components: {
    BidsDatastructFormPart
    // BidsCrontab
  },
  props: {
    pageStatus: String // 该组件所在的页面（编辑｜构建｜查看）
  },
  computed: {
    ...mapState({
      configData: state => state.currentPublishConfig, // 当前配置
      dt_system: 'dt_system',
      dt_dsConnector: 'dt_dsConnector',
      defaultConfig: state => state.defaultPublishConfig
    }),
    kafkaSink() {
      return this.configData.job.sources[0].properties.connector === 'kafka' && this.form.storageStrategy === 'SAVE'
    },
    currentDataSource() {
      const { dsId, dsType } = this.configData.job.sources[0].properties
      return { dsAppName: this.configData.appName, dsId, dsType }
    },
    tableName() {
      if (this.form.storageStrategy === 'SAVE') {
        if (this.pageStatus === 'build') {
          return `${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}_v1`
        } else if (this.pageStatus === 'clone&build') {
          const latest_version = this.configData.latestVersion?.replace(/(\d+)/, ($1) => Number($1) + 1)
          const table_name = `${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}_${latest_version}`
          return table_name
        } else if (this.pageStatus === 'edit') {
          const version = this.configData.version
          const table_name = `${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}_${version}`
          return table_name
        }
      }
      return `PUB_${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}`
    }
    // hintOptions() {
    //   // 自定义sql编辑器的代码提示
    //   const tableName = `PUB_${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}`
    //   const tables = {}
    //   tables[tableName] = []
    //   this.configData.job.sources[0].columns && this.configData.job.sources[0].columns.forEach(col => {
    //     col.name && (tables[col.name] = [])
    //   })
    //   this.expressionList.map(item => item.expression.slice(1, -1)).forEach(item => tables[item] = [])
    //   return {
    //     completeSingle: false,
    //     tables
    //   }
    // }
  },
  data() {
    const validateScript = (rule, value, callback) => {
      if (value) {
        // const tableName = `PUB_${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}`
        const tableName = this.tableName
        if (value.indexOf(tableName) === -1) {
          callback(new Error('未按要求填写表名，请使用右侧第二条提示中的表名'))
        }
      } else {
        callback()
      }
    }
    return {
      configType: 'publish', // 该组件用于数据发布
      pageName: 'bids-publish-output',
      form: {},
      scriptErrorMsg: '',
      scriptValid: true,
      dsLoading: false,
      btnConnLoading: false,
      connectorOptions: [],
      storageOptions: [{ label: '暂存平台', value: 'SAVE' }, { label: '不暂存，直连源库', value: 'NOT_SAVE' }],
      partitionerOptions: [
        { label: 'default', value: 'default' }, { label: 'fixed', value: 'fixed' }, 
        { label: 'round-robin', value: 'round-robin' }
      ],
      rules: {
        storageStrategy: [{ required: true, message: '请选择落地策略', trigger: 'change' }],
        partition: [{ required: true, message: '请设置分区数', trigger: 'change' }],
        partitioner: [{ required: true, message: '请选择分区器', trigger: 'change' }],
        script: [{ validator: validateScript, trigger: 'change' }]
      },
      labelWidth: '140px'
    }
  },
  watch: {
    kafkaSink(val) {
      if (val) {
        // 设置kafka默认字段
        if (!this.form.partition) {
          this.form.partition = 1
          this.updateSinkDsProperties(1, 'partition')
        }
      } else {
        const { partitioner, partition, ...others } = this.configData.job.sink.properties
        // 删除kafka相关字段
        if (partitioner || partition) {
          const payload = { type: 'publish', keyPath: ['job', 'sink', 'properties'], value: others }
          this.UPDATE_CONFIG_ITEM(payload)
          this.form.partitioner = undefined
          this.form.partition = undefined
        }
      }
    }
  },
  created() {
    this.updatePageData()
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    updatePageData() {
      // 从配置读取数据，更新页面
      const script = this.configData.job.transform.script
      const { storageStrategy } = this.configData.job.sink
      const defaultTopic = `${this.configData.configId || '配置ID'}_${this.configData.version || '版本'}`
      const { topic = defaultTopic, partitioner, partition } = this.configData.job.sink.properties
      this.form = { script, storageStrategy, partitioner, partition, topic: this.strictEditable ? '配置ID_版本' : topic }
      this.$refs['cmEditor'] && this.$refs['cmEditor'].refresh()
      this.$refs['datastruct'] && this.$refs['datastruct'].updatePageData()
    },
    handleStorageStrategyChange(val) {
      let payload = { type: 'publish', keyPath: ['job', 'sink', 'storageStrategy'], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
      if (val === 'NOT_SAVE') {
        // 清空调度策略
        payload = { type: 'publish', keyPath: ['setting', 'scheduleStrategy'], value: this.defaultConfig.setting.scheduleStrategy }
        this.UPDATE_CONFIG_ITEM(payload)
      }
    },
    handleScriptChange(val) {
      this.form.script = val
      const payload = { type: 'publish', keyPath: ['job', 'transform', 'script'], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
      // 如果之前未通过校验，再次校验，清除校验提示
      // const tableName = `PUB_${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}`
      const tableName = this.tableName
      if (this.form.script && this.form.script.indexOf(tableName) !== -1) {
        this.$refs.form.clearValidate('script')
      }
    },
    updateSinkDsProperties(val, name) {
      // 更新sink.properties里的字段
      const payload = { type: 'publish', keyPath: ['job', 'sink', 'properties', name], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleCmReady(cm) {
      cm.on('keypress', () => {
        if (this.moderateEditable) cm.showHint()
      })
    },
    handleBlur() {
      // const sqlText = sqlFormatter.format(this.form.script)
      // if (this.form.instruction !== sqlText) {
      //   this.handleScriptChange(sqlText)
      // }
    },
    handleFormatter() {
      const sqlText = sqlFormatter.format(this.form.script)
      if (this.form.instruction !== sqlText) {
        this.handleScriptChange(sqlText)
      }
    },
    formValidate() {
      // 校验本组件内的表单项，点击提交按钮时通过broadcast被触发
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      // const tableName = `PUB_${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}`
      // if (this.form.script && this.form.script.indexOf(tableName) === -1) {
      //   this.scriptValid = false
      //   this.scriptErrorMsg = '未按要求填写表名'
      //   validRes = false
      // }
      return validRes
    }
  }
  
}
</script>
<style lang="less">
 @import "~assets/css/bids-publish-output.less";
</style>

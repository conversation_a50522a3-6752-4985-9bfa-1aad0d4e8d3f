<!--
 * @Description: 上传表结构弹窗组件，解析建表语句得到数据的字段
 * @Autor: kenoxia
 * @LastEditTime: 2020-12-31 19:16:49
-->
<template>
  <div class="upload-table-dialog">
    <el-dialog title="上传表结构" :visible.sync="visible" :before-close="handleClose" width="600px">
      <el-form :model="form" ref="form" :rules="rules" label-position="left" label-width="140px">
        <el-form-item label="所属应用">
          <span>{{initData.dsAppName || initData.dsAppKey}}</span>
        </el-form-item>
        <el-form-item label="来源库类型">
          <span>{{initData.dsType}}</span>
        </el-form-item>
        <el-form-item label="单个表的建表语句" prop="createTableSql">
          <el-input v-model="form.createTableSql" size="medium" rows="10" type="textarea" resize="none" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="medium">取消</el-button>
        <el-button size="medium" type="primary" @click="handleUpload" :loading="loading">确认上传</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { uploadTableSql } from 'services/common.service'

export default {
  name: 'upload-table-dialog',
  props: {
    initData: {
      /* { dsAppName, dsAppKey, dsId, dsName, dsType, dsProperties: { dsUrl, dsUserName }, ... } */
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      form: {},
      rules: {
        createTableSql: [{ required: true, message: '请输入建表语句', trigger: 'blur' }]
      }
    }
  },
  methods: {
    show() {
      this.visible = true
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    handleUpload() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false
        } else {
          this.loading = true
          const queryData = { createTableSql: this.form.createTableSql.replace(/[\r\n]/g, ''), dsType: this.initData.dsType }
          uploadTableSql(queryData).then(res => {
            this.$message.success('解析成功！')
            this.$emit('upload', res.columns)
            this.visible = false
            this.loading = false
          }).catch(res => {
            this.$message.error('解析失败！原因：' + res.message || '暂无')
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

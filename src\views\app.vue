<template>
  <div id="app" :key="layoutKey">
    <sdc-layout scope="oa"
                :sidebar-menus="sidebarMenus"
                :headerLayout="headerLayout">
      <div slot="header-logo" class="header-logo">
        <div class="logo-icon"></div>
      </div>
      <div slot="header-links" class="header-links">
        <a class="help" target="_blank" :href="url">帮助</a>
        <img :src="userInfo.avatar || avatarUrl" v-on:error="notFound"/>
        <span class="name">{{userInfo.engName ? userInfo.engName : ''}}{{userInfo.admin ? '(管理员)' : ''}}</span>
        <span class="exit" @click="handleExit">退出</span>
      </div>
    </sdc-layout>
  </div>
</template>

<script>
import { refresh, resize } from 'sdc-vue'
import { SIDEBAR_MENUS } from 'config/app.conf'
import { queryUserInfo } from 'services/common.service'
import { mapMutations, mapState, mapActions } from 'vuex'
import { getBuildEnv } from 'sdc-core'
export default {
  name: 'app',
  mixins: [refresh, resize],
  data() {
    return {
      sidebarMenus: { active: this.$route.meta.sidebarKey || 'home', ...SIDEBAR_MENUS },
      headerLayout: ['logo', 'links'],
      loading: false,
      url: 'https://iwiki.woa.com/pages/viewpage.action?pageId=4007175172'
    }
  },
  watch: {
    '$route.meta.sidebarKey': {
      handler(val) {
        this.sidebarMenus.active = val
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['userInfo']),
    avatarUrl() {
      return require('assets/img/avatar.gif')
    }
  },
  created() {
    const loading = this.$loading()
    queryUserInfo().then(res => {
      const userInfo = { avatar: `http://r.hrc.woa.com/photo/48/${res.engName}.png`, ...res }
      this.SET_USERINFO(userInfo)
      this.setSubWhiteList()
      loading.close()
    }).catch(() => {
      loading.close()
      this.$message.error('获取用户信息失败！')
    })
  },
  methods: {
    ...mapActions(['setSubWhiteList']),
    ...mapMutations(['SET_USERINFO']),
    notFound(event) {
      const ele = event.srcElement
      ele.src = this.avatarUrl
      ele.onerror = null
    },
    handleExit() {
      this.$loading()
      setTimeout(() => {
        const { origin } = window.location
        const prdUrl = `http://passport.woa.com/modules/passport/signout.ashx?url=${origin}/bidsplus/home`
        const devUrl = `http://passtest.woa.com/modules/passport/signout.ashx?url=${origin}/_hrtest_oa_login_/?url=/bidsplus/home&app=`
        window.location.href = getBuildEnv() === 'prd' ? prdUrl : devUrl
      }, 100)
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/app.less";
</style>

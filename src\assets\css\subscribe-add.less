.subscribe-add {
  padding: 0 @page-padding-horizontal;
  max-width: @page-max-width;
  .page-header {
    display: flex;
    justify-content: space-between;
    height: @page-header;
    align-items: center;
    .header-left {
      .tips {
        font-size: @font-14;
        color: @color-text-gray;
      }
    }
  }
  .page-main {
    height: calc(100vh - 80px - @page-header);
    padding-bottom: @page-main-padding-vertical;
    .selected-wrap {
      height: 40%;
      .header {
        display: flex;
        justify-content: space-between;
        height: 50px;
        align-items: center;
        .title {
          font-size: 16px;
          color: @color-text-dark;
          font-weight: 500;
        }
      }
      .table {
        margin-top: 10px;
        height: calc(100% - 60px)
      }
    }
    .search-wrap {
      margin-top: 30px;
      height: calc(60% - 30px);
      .header {
        display: flex;
        justify-content: space-between;
        height: 50px;
        align-items: center;
        .title {
          font-size: 16px;
          color: @color-text-dark;
          font-weight: 500;
        }
        .filter {
          width: 360px;
        }
      }
      .table {
        margin-top: 10px;
        height: calc(100% - 120px)
      }
      .pagination {
        margin-top: 24px;
      }
    }
  }
  .el-input .el-select{
    width: 110px;
  }
}

.commit-confirm-dialog {
  .el-dialog__body {
    padding: 20px 40px;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
  label {
    color: #99a9bf;
  }
}

.el-tag {
  .el-loading-spinner {
    margin-top: -10px !important;
  }
}

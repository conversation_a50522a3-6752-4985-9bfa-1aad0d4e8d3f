/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-12-15 11:40:00
 */
import { http, httpFlink } from 'utils/http'
import api from 'config/api.conf'

export const queryAllData = (data) => http.post(api.bidsplus_config.subscribe.queryAllData, { params: data })
export const queryData = (params) => http.get(
  api.bidsplus_config.subscribe.queryData.replace('{configId}', params.configId).replace('{version}', params.version || 'null'), { loading: true }
)
export const updateData = (params, data) => http.post(
  api.bidsplus_config.subscribe.updateData.replace('{configId}', params.configId).replace('{version}', params.version), { params: data, loading: true }
)
export const deleteData = (params) => http.del(api.bidsplus_config.subscribe.deleteData.replace('{configId}', params.configId), { loading: true })
export const queryMineData = (data) => http.post(api.bidsplus_config.subscribe.queryMineData, { params: data })
export const queryInstances = (params, data) => http.post(
  api.bidsplus_config.subscribe.queryInstances.replace('{configId}', params.configId).replace('{version}', params.version), { params: data }
)
export const queryDrafts = (data) => http.post(api.bidsplus_config.subscribe.queryDrafts, { params: data, loading: true })
export const saveDrafts = (params, data) => http.post(
  api.bidsplus_config.subscribe.saveDrafts.replace('{configId}', params.configId || 'null'), { params: data, loading: true }
)
export const updateDrafts = (params, data) => http.post(
  api.bidsplus_config.subscribe.updateDrafts.replace('{configId}', params.configId).replace('{version}', params.version || 'null'), { params: data, loading: true }
)
export const removeDrafts = (data) => http.post(api.bidsplus_config.subscribe.removeDrafts, { params: data, loading: true })
export const submitDrafts = (data) => http.post(api.bidsplus_config.subscribe.submitDrafts, { params: data, loading: true })
export const setOnline = (data) => http.post(api.bidsplus_config.subscribe.setOnline, { params: data, loading: true })
export const setOffline = (data) => http.post(api.bidsplus_config.subscribe.setOffline, { params: data, loading: true })
export const queryJob = (params) => httpFlink.post(api.bidsplus_executor.queryJob.replace('{executorJobName}', params.executorJobName))
export const subJobsbatchStart = (data) => http.post(api.bidsplus_config.subscribe.subJobsbatchStart, { params: data, loading: true })

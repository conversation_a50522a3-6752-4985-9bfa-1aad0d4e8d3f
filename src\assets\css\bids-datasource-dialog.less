.bids-datasource-dialog {
  .el-form-item {
    .el-input:not(.custom-input){
      width: 370px;
    }
    .el-select {
      width: @base-select-width;
    }
    .params-wrapper, .header-wrapper {
      display: flex;
      flex-wrap: wrap;
      .wrapper {
        display: flex;
        .input-wrapper {
          width: 370px;
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          &:last-child {
            margin-bottom: 0;
          }
        }
        .btn-wrapper {
          margin-bottom: 10px;
          &:last-child {
            margin-bottom: 0;
          }
          .remove-btn {
            color: @color-danger;
          }
        }
      }
    }
    .codemirror-wrapper {
      width: 370px;
      border: 1px solid #ebeef5;
      ::v-deep .CodeMirror {
        line-height: 16px;
        height: 200px !important;
      }
    }
  }
  .el-dialog__body {
    padding: 20px 20px;
  }
  .custom-input {
    width: @base-select-width;
  }
}
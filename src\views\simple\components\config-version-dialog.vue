<template>
  <div class="config-version-dialog">
    <el-dialog title="版本记录" :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" width="1228px" top="10vh">
      <el-table :data="tableData" style="width: 100%" height="500" :header-cell-style="{ background: '#f5f7f9', height: '60px' }">
        <el-table-column prop="index" label="序号" width="61" align="center">
          <template slot-scope="scope">
            {{ (pageIndex - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column prop="jobVersion" label="版本" width="66" :formatter="formatjobVersion"></el-table-column>

        <el-table-column prop="appName" label="所属应用" width="109">
        </el-table-column>

        <el-table-column prop="interfaceName" label="配置名称" min-width="158" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="interfaceCode" label="配置编码" min-width="158" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="creatorName" label="创建人" width="170">
          <template v-slot="{ row }">
            <BidsUserName :fullName="row.creatorName"></BidsUserName>
            </template>
        </el-table-column>

        <el-table-column prop="updator" label="修改人" width="170">
          <template v-slot="{ row }">
            <BidsUserName :user="row.updator"></BidsUserName>
          </template>
        </el-table-column>

        <el-table-column prop="modifyTime" label="更新时间" width="180" :formatter="formatTime"></el-table-column>

        <el-table-column label="操作" width="100">
          <template v-slot="{ row }">
            <el-button type="text" size="small" @click="handleRollback(row)">回滚到此版本</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pagination } from 'mixins'
import { BidsUserName } from 'components'
import { querySimpleConfigVersions, rollbackSimpleConfig } from 'services/simple.service'

export default {
  name: 'config-version-dialog',
  mixins: [pagination], // 分页
  components: {
    BidsUserName
  },
  data() {
    return {
      row: {},
      visible: false,
      paginationType: 'remote', // 由服务器实现分页查询
      tableData: []
    }
  },
  methods: {
    show(row) {
      this.row = row
      this.fetchData()
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    fetchData() {
      const params = {
        id: this.row.configId,
        pageSize: this.pageSize,
        pageIndex: this.pageIndex
      }
      if (this.recordCount) {
        params.recordCount = this.recordCount
      }
      querySimpleConfigVersions(params).then(res => {
        this.tableData = res.content
        this.recordCount = res.paginator.recordCount
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('操作失败！原因：' + res.message || '暂无')
      })
    },
    formatjobVersion(row) {
      return `V${row.jobVersion}`
    },
    formatTime(row) {
      return row.modifyTime && (new Date(row.modifyTime).toLocaleString())
    },
    handleRollback(row) {
      this.$confirm(`确定回滚「${row.interfaceName}」到「V${row.jobVersion}版本」吗？`, '提示', { type: 'warning' }).then(() => {
        rollbackSimpleConfig({ id: row.configId, version: row.jobVersion }).then(res => {
          this.$message.success('操作成功！')
          this.handleClose()
          this.$emit('update')
        }).catch(res => {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="less" scoped>
.config-version-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }

  .pagination {
    margin-top: 20px;
  }
}
</style>

<template>
  <div class="config-version-dialog">
    <el-dialog title="版本记录" :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" width="1408px" top="10vh">
      <el-table :data="tableData" style="width: 100%" height="500" :header-cell-style="{ background: '#f5f7f9', height: '60px' }">
        <el-table-column prop="index" label="序号" width="61" align="center">
          <template slot-scope="scope">
            {{ (pageIndex - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column prop="jobVersion" label="版本" width="66">
          <template v-slot="{ row }">
            <el-button type="text" @click="handleView(row)">{{ formatjobVersion(row) }}</el-button>
          </template>
        </el-table-column>

        <el-table-column prop="appName" label="所属应用" width="109">
        </el-table-column>

        <el-table-column prop="interfaceName" label="配置名称" min-width="158" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="interfaceCode" label="配置编码" min-width="158" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="releaseUser" label="创建人" width="170">
          <template v-slot="{ row }">
            <BidsUserName :fullName="row.releaseUser"></BidsUserName>
          </template>
        </el-table-column>

        <el-table-column prop="releaseTime" label="创建时间" width="180" :formatter="(row) => formatTime(row.releaseTime)"></el-table-column>

        <el-table-column prop="modifierName" label="修改人" width="170">
          <template v-slot="{ row }">
            <BidsUserName :user="row.modifierName"></BidsUserName>
          </template>
        </el-table-column>

        <el-table-column prop="modifyTime" label="更新时间" width="180" :formatter="(row) => formatTime(row.modifyTime)"></el-table-column>

        <el-table-column label="操作" width="100">
          <template v-slot="{ row }">
            <el-button type="text" size="small" @click="handleRollback(row)" :disabled="row.jobVersion === currentRow.jobVersion">回滚到此版本</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pagination } from 'mixins'
import { BidsUserName } from 'components'
import { querySimpleConfigVersions, rollbackSimpleConfig } from 'services/simple.service'

export default {
  name: 'config-version-dialog',
  mixins: [pagination], // 分页
  components: {
    BidsUserName
  },
  data() {
    return {
      currentRow: {},
      visible: false,
      paginationType: 'remote', // 由服务器实现分页查询
      tableData: []
    }
  },
  methods: {
    show(row) {
      this.currentRow = row
      this.fetchData()
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    fetchData() {
      const params = {
        id: this.currentRow.configId,
        pageSize: this.pageSize,
        pageIndex: this.pageIndex
      }
      if (this.recordCount) {
        params.recordCount = this.recordCount
      }
      querySimpleConfigVersions(params).then(res => {
        this.tableData = res.content
        this.recordCount = res.paginator.recordCount
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('操作失败！原因：' + res.message || '暂无')
      })
    },
    formatjobVersion(row) {
      return `V${row.jobVersion}`
    },
    formatTime(time) {
      return time ? new Date(time).toLocaleString().replaceAll('/', '-') : '-'
    },
    handleRollback(row) {
      this.$confirm(`确定回滚「${row.interfaceName}」到「V${row.jobVersion}版本」吗？`, '提示', { type: 'warning' }).then(() => {
        rollbackSimpleConfig({ id: row.configId, version: row.jobVersion }).then(res => {
          this.$message.success('操作成功！')
          this.handleClose()
          this.$emit('update')
        }).catch(res => {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
      }).catch(() => {})
    },
    handleView(row) {
      const { href } = this.$router.resolve({ name: 'Config', query: { configId: row.configId, mode: 'view', version: row.jobVersion } })
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang="less" scoped>
.config-version-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }

  .pagination {
    margin-top: 20px;
  }
}
</style>

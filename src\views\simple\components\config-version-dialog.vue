<template>
  <div class="config-version-dialog">
    <el-dialog title="版本记录" :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" width="1188px" top="10vh">
      <el-table :data="tableData" style="width: 100%" height="500" :header-cell-style="{ background: '#f5f7f9', height: '60px' }">
        <el-table-column prop="index" label="序号" width="61" align="center">
          <template slot-scope="scope">
            {{ (pageIndex - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column prop="version" label="版本" width="66">
          <template slot-scope="scope">
            <span>{{ scope.row.version }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="appName" label="所属应用" width="109">
        </el-table-column>

        <el-table-column prop="configName" label="配置名称" min-width="158" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="configCode" label="配置编码" min-width="158" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="creator" label="创建人" width="170">
          <template>
              <BidsUserName user="yiwuhe"></BidsUserName>
            </template>
        </el-table-column>

        <el-table-column prop="modifier" label="修改人" width="170">
          <template>
              <BidsUserName user="yiwuhe"></BidsUserName>
            </template>
        </el-table-column>

        <el-table-column prop="updateTime" label="更新时间" width="180">
        </el-table-column>

        <el-table-column label="操作" width="60">
          <template v-slot="{ row }">
            <el-button type="text" size="small">回滚{{ row.a }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pagination } from 'mixins'
import { BidsUserName } from 'components'
import { querySimpleConfigVersions } from 'services/simple.service'

export default {
  name: 'config-version-dialog',
  mixins: [pagination], // 分页
  components: {
    BidsUserName
  },
  data() {
    return {
      row: {},
      visible: false,
      paginationType: 'remote', // 由服务器实现分页查询
      tableData: [
        {
          version: 'V4',
          appName: 'hrmd',
          configName: 'cmdb所有服务',
          configCode: 'cmdb-all-services',
          creatorName: 'sanzhang(张三)',
          modifierName: 'sanzhang(张三)',
          updateTime: '2025-01-06 09:20:56'
        }
      ]
    }
  },
  methods: {
    show(row) {
      this.row = row
      this.fetchData()
      this.visible = true
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    fetchData() {
      const params = {
        id: this.row.configId,
        pageSize: this.pageSize,
        pageIndex: this.pageIndex
      }
      if (this.recordCount) {
        params.recordCount = this.recordCount
      }
      querySimpleConfigVersions(params).then(res => {
        this.tableData = res.content
        this.recordCount = res.paginator.recordCount
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('获取版本记录失败！原因：' + res.message || '暂无')
      })
    }
  }
}
</script>

<style lang="less" scoped>
.config-version-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }

  .pagination {
    margin-top: 20px;
  }
}
</style>

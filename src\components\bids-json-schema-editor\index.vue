<!--
 * @Description: json-schema编辑器，这里用于定义json结构数据的字段
 * @Autor: kenoxia
 * @LastEditTime: 2020-12-31 19:18:59
-->
<template>
  <div class="bids-json-schema-editor">
    <div :style="{marginLeft:`${20*deep}px`}" class="json-item">
      <span class="col-caret">
        <i :class="hidden ? 'el-icon-caret-right' : 'el-icon-caret-bottom'" @click="hidden = !hidden" v-if="pickValue.type==='object'"></i>
      </span>
      <span class="col-name">
        <el-input size="mini" :disabled="allDisabled || disabled || root" v-model="inputName" @blur="onInputName"/>
      </span>
      <span class="col-required">
        <el-tooltip content="全选" placement="top" v-if="root">
          <el-checkbox :disabled="allDisabled || !isObject && !isArray"  @change="onRootCheck"></el-checkbox>
        </el-tooltip>
        <el-tooltip content="是否必须" placement="top" v-else>
          <el-checkbox :disabled="allDisabled || isItem" v-model="checked"  @change="onCheck"></el-checkbox>
        </el-tooltip>
      </span>
      <span class="col-type">
        <el-select size="mini" v-model="pickValue.type" :disabled="allDisabled || disabledType" @change="onChangeType">
          <el-option v-for="(item, index) in TYPE_NAME" :key="index" :value="item"></el-option>
        </el-select>
      </span>
      <span class="col-title">
        <el-input :disabled="allDisabled" v-model="pickValue.title" size="mini" placeholder="标题"/>
      </span>
      <span class="col-description">
        <el-input :disabled="allDisabled" size="mini" placeholder="描述" v-model="pickValue.description"/>
      </span>
      <span class="col-toolbar">
        <el-tooltip content="高级设置">
          <i class="el-icon-setting" @click="onSetting"/>
        </el-tooltip>
        <el-tooltip content="删除节点" v-if="!allDisabled && !root && !isItem">
          <i class="el-icon-delete"  @click="removeNode"></i>
        </el-tooltip>
        <el-tooltip content="添加子节点" v-if="!allDisabled && isObject">
          <i class="el-icon-plus" style="color:#3464e0;" @click="addChild"></i>
        </el-tooltip>
      </span>
    </div>
    <template v-if="!hidden&&pickValue.properties && !isArray">
      <bids-json-schema-editor v-for="(item,key,index) in pickValue.properties" :value="{[key]:item}" :parent="pickValue" :key="index" :deep="deep+1" :root="false" class="children"/>
    </template>
    <template v-if="isArray">
      <bids-json-schema-editor :value="{items:pickValue.items}" :deep="deep+1" disabled isItem :root="false" class="children"/>
    </template>
    <el-dialog
      title="高级设置"
      :visible.sync="modalVisible">
      <h4>基础设置</h4>
      <el-form inline :model="advancedValue" :disabled="allDisabled">
        <el-form-item v-for="(item,key) in advancedValue" :key="key">
          <el-tooltip :content="advancedAttr[key].name" slot="label">
            <span style="font-weight:500;">{{ key }}</span>
          </el-tooltip>
          <el-input-number size="small" controls-position="right" v-model="advancedValue[key]" v-if="['integer', 'number'].includes(advancedAttr[key].type)" :step-strictly="advancedAttr[key].type !== 'number'"/>
          <el-switch v-else-if="advancedAttr[key].type === 'boolean'" v-model="advancedValue[key]"/>
          <el-select size="small" v-else-if="advancedAttr[key].type === 'array'" v-model="advancedValue[key]"> 
            <el-option :key="index" v-for="(item, index) in advancedAttr[key].enums" :value="item"></el-option>
          </el-select>
          <el-radio-group v-model="advancedValue[key]" v-else-if="advancedAttr[key].type === 'radio'">
            <el-radio :key="index" v-for="(item, index) in advancedAttr[key].enums" :label="item">{{item}}</el-radio>
          </el-radio-group>
          <el-input size="small" v-model="advancedValue[key]" style="width:150px;" v-else/>
        </el-form-item>
      </el-form>
      <h4>预览</h4>
      <div class="preview">
        <pre>{{completeNodeValue}}</pre>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" @click="modalVisible = false">取消</el-button>
        <el-button size="medium" type="primary" @click="handleOk">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { TYPE_NAME, TYPE, isNull } from './type'

export default {
  name: 'bids-json-schema-editor',
  props: {
    value: {
      type: Object,
      required: true
    },
    disabled: { // name不可编辑，根节点name不可编辑,数组元素name不可编辑
      type: Boolean,
      default: false
    },
    disabledType: { // 禁用类型选择
      type: Boolean,
      default: false
    },
    isItem: { // 是否数组元素
      type: Boolean,
      default: false
    },
    deep: { // 节点深度，根节点deep=0
      type: Number,
      default: 0
    },
    root: { // 是否root节点
      type: Boolean,
      default: true
    },
    parent: { // 父节点
      type: Object,
      default: null
    },
    allDisabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    pickValue() {
      return Object.values(this.value)[0] 
    },
    pickKey: {
      get: function() {
        return Object.keys(this.value)[0]
      },
      set: function(val) {
        if (this.inputName !== val) {
          this.inputName = val
        }
      }
    },
    isObject() {
      return this.pickValue.type === 'object'
    },
    isArray() {
      return this.pickValue.type === 'array'
    },
    checked: {
      get: function() {
        return this.parent && this.parent.required && this.parent.required.indexOf(this.pickKey) >= 0
      },
      set: function() {
      }
    },
    advancedAttr() {
      return TYPE[this.pickValue.type].attr
    },
    advancedNotEmptyValue() {
      const jsonNode = Object.assign({}, this.advancedValue)
      for (let key in jsonNode) {
        isNull(jsonNode[key]) && delete jsonNode[key]
      }
      return jsonNode
    },
    completeNodeValue() {
      const t = {}
      for (const item of this.customProps) {
        t[item.key] = item.value
      }
      return Object.assign({}, this.pickValue, this.advancedNotEmptyValue, t)
    }
  },
  watch: {
    'pickValue.type'(val) {
      this.advancedValue = TYPE[this.pickValue.type].value
    }
  },
  data() {
    return {
      TYPE_NAME,
      hidden: false,
      countAdd: 1,
      modalVisible: false,
      advancedValue: {},
      addProp: {}, // 自定义属性
      customProps: [],
      customing: false,
      inputName: Object.keys(this.value)[0]
    }
  },
  mounted() {
    this.advancedValue = TYPE[this.pickValue.type].value
  },
  methods: {
    onInputName(e) {
      const val = e.target.value
      const p = {}
      for (let key in this.parent.properties) {
        if (key !== this.pickKey) {
          p[key] = this.parent.properties[key]
        } else {
          p[val] = this.parent.properties[key]
          delete this.parent.properties[key]
        }
      }
      this.$set(this.parent, 'properties', p)
    },
    onChangeType() {
      Object.keys(this.pickValue).forEach(key => {
        if (!['type', 'title', 'description'].includes(key)) {
          this.$delete(this.pickValue, key)
        }
      })
      if (this.isArray) {
        this.$set(this.pickValue, 'items', { type: 'string' })
      }
    },
    onCheck(val) {
      this._checked(val, this.parent)
    },
    onRootCheck(val) {
      this._deepCheck(val, this.pickValue)
    },
    _deepCheck(checked, node) {
      if (node.type === 'object' && node.properties) {
        checked ? this.$set(node, 'required', Object.keys(node.properties)) : this.$delete(node, 'required')
        Object.keys(node.properties).forEach(key => this._deepCheck(checked, node.properties[key]))
      } else if (node.type === 'array' && node.items.type === 'object') {
        checked ? this.$set(node.items, 'required', Object.keys(node.items.properties)) : this.$delete(node.items, 'required')
        Object.keys(node.items.properties).forEach(key => this._deepCheck(checked, node.items.properties[key]))
      }
    },
    _checked(checked, parent) {
      let required = parent.required
      if (checked) {
        required || this.$set(this.parent, 'required', [])
        required = this.parent.required
        required.indexOf(this.pickKey) === -1 && required.push(this.pickKey)
      } else {
        const pos = required.indexOf(this.pickKey)
        pos >= 0 && required.splice(pos, 1)
      }
      required.length === 0 && this.$delete(parent, 'required')
    },
    addChild() {
      const name = this._joinName()
      const type = 'string'
      const node = this.pickValue
      node.properties || this.$set(node, 'properties', {})
      const props = node.properties
      this.$set(props, name, { type: type })
    },
    addCustomNode() {
      this.$set(this.addProp, 'key', this._joinName())
      this.$set(this.addProp, 'value', '')
      this.customing = true
    },
    confirmAddCustomNode() {
      this.customProps.push(this.addProp)
      this.addProp = {}
      this.customing = false
    },
    removeNode() {
      const { properties, required } = this.parent 
      this.$delete(properties, this.pickKey)
      if (required) {
        const pos = required.indexOf(this.pickKey)
        pos >= 0 && required.splice(pos, 1)
        required.length === 0 && this.$delete(this.parent, 'required')
      }
    },
    _joinName() {
      return `feild_${this.deep}_${this.countAdd++}`
    },
    onSetting() {
      this.modalVisible = true
      for (const k in this.advancedValue) {
        this.advancedValue[k] = this.pickValue[k]
      }
    },
    handleOk() {
      this.modalVisible = false
      for (const key in this.advancedValue) {
        if (isNull(this.advancedValue[key])) {
          this.$delete(this.pickValue, key)
        } else {
          this.$set(this.pickValue, key, this.advancedValue[key])
        }
      }
      for (const item of this.customProps) {
        this.$set(this.pickValue, item.key, item.value)
      }
    }
  }
}
</script>
<style lang="less">
  @import "~assets/css/bids-json-schema-editor.less";
</style>

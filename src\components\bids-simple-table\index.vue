<!--
 * @Description: 仿ElementUI样式的简单表格，解决el-table行内popover内嵌el-table的样式bug
 * @Autor: kenoxia
 * @LastEditTime: 2020-12-31 19:22:03
-->
<template>
  <table v-loading="loading" class="bids-simple-table">
    <thead>
      <tr>
        <th rowspan="1" colspan="1" v-for="item in tableColumn" :key="item.prop" 
          :style="{ width: item.width ? item.width + 'px' : '' }">
          {{item.label}}
        </th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(row, index) in data" :key="index">
        <td rowspan="1" colspan="1" v-for="col in tableColumn" :key="col.prop"
          :style="{ width: col.width ? col.width + 'px' : '' }">
          {{col.formatter ? col.formatter(row) : row[col.prop]}}
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script>
export default {
  name: 'bids-simple-table',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    tableColumn: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/bids-simple-table.less";
</style>

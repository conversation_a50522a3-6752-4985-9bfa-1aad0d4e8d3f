<template>
  <div class="config-add-page" ref="configPage">
    <div class="config-page-header">
      <el-page-header @back="goBack" :content="title">{{ isAdd }}</el-page-header>
    </div>
    <div class="config-page-main">
      <!-- 基础配置 -->
      <section ref="baseConfig" :class="{ hide: !expand }">
        <block-header-tool title="基础配置" v-model="expand"></block-header-tool>
        <div class="base-config-wrap" v-show="expand">
          <!-- 基本信息 -->
          <section>
            <div class="base-config-title">基本信息</div>
            <base-info-form ref="baseInfoForm" :isView="isView" :isEdit="isEdit" :isOnlineEdit="isOnlineEdit"></base-info-form>
          </section>
          <!-- 数据采集 -->
          <section style="margin-bottom: 16px;">
            <div class="base-config-title">数据采集</div>
            <div class="base-config-source">
              <!-- 数据来源表单 -->
              <source-form ref="sourceForm" :dataSourceOptions="dataSourceOptions" :dsTypeOptions="dsTypeOptions" :isView="isView" :isAdd="isAdd" :isEdit="isEdit" :isOnlineEdit="isOnlineEdit" @updateSourceFields="updateSourceFields"></source-form>
              <!-- 图标 -->
              <div class="icon-wrap">
                <i class="el-icon-arrow-right"></i>
                <i class="el-icon-arrow-right"></i>
              </div>
              <!-- 数据目的地表单 -->
              <sink-form ref="sinkForm" :dataSourceOptions="dataSourceOptions" :dsTypeOptions="dsTypeOptions" :isView="isView" :isEdit="isEdit" @updateSinkFields="updateSinkFields"></sink-form>
            </div>
          </section>
          <!-- 配置字段映射 -->
          <section>
            <div class="base-config-title">配置字段映射</div>
            <!-- 有显示逻辑 -->
            <div class="field-mapping-wrap" v-if="showMappingTable">
              <div class="field-mapping-tool">
                <span>映射策略</span>
                <el-radio-group v-model="mappingValue" size="small" :disabled="isView || isOnlineEdit" @change="fieldMappingChange">
                  <el-radio label="name">同名映射</el-radio>
                  <el-radio label="row">同行映射</el-radio>
                </el-radio-group>
                <el-button type="text" size="medium" :disabled="isView || isOnlineEdit" @click="cancelMapping">重置映射</el-button>
              </div>
              <div class="field-mapping-table">
                <el-table ref="tableRef" :data="fields" max-height="400px" :row-key="row => row.rowKey || row.sourceFieldKey">
                  <el-table-column prop="sourceFieldKey" label="字段" min-width="15%" show-overflow-tooltip>
                    <template slot-scope="{row}">
                      <div v-if="row.sourceFieldType && row.sourceFieldType.startsWith('custom')">
                        <el-input v-model="row.sourceFieldKey" placeholder="请输入自定义字段" @change="sourceFieldChange(row)" size="small"></el-input>
                      </div>
                      <div v-else>
                        <span v-if="sourcePartitionField && row.sourceFieldKey === sourcePartitionField" class="partition-wrapper">分区</span>
                        {{ row.sourceFieldKey }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="sourceFieldType" label="字段类型" min-width="15%">
                    <template slot-scope="{ row }">
                      <div v-if="row.sourceFieldType && row.sourceFieldType.startsWith('custom')">
                        <el-select v-model="row.sourceFieldType" placeholder="请选择自定义字段类型" @change="sourceFieldChange(row)" size="small">
                          <el-option label="customString" value="customString"></el-option>
                          <el-option label="customNumber" value="customNumber"></el-option>
                          <el-option label="customFunction" value="customFunction"></el-option>
                        </el-select>
                      </div>
                      <div v-else>
                        {{ row.sourceFieldType }}
                      </div>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column min-width="5%">
                    <template #header>
                      <el-checkbox v-model="isEncryptionAll"></el-checkbox> 加密
                    </template>
                    <template slot-scope="{ row }">
                      <el-checkbox v-model="row.enableEncryption"></el-checkbox>
                    </template>
                  </el-table-column> -->
                  <el-table-column label="映射目的表字段" min-width="10%">
                    <template slot-scope="scope">
                      <dh-virtual-select
                        v-if="scope.row.sourceFieldKey !== undefined"
                        v-model="scope.row.mappingFieldKey"
                        class="field-selector"
                        filterable
                        clearable
                        :disabled="isView || isOnlineEdit"
                        :list="getFieldOptions(scope.row)"
                        :option="option"
                        @clear="deleteConnect(scope.row, scope.$index)"
                        @change="mappingChange($event, scope.$index)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column class-name="connect-line-col" min-width="10%">
                    <template slot-scope="{ row }">
                      <field-connect-line :field="row" :disabled="isView || isOnlineEdit" @delete-connect="deleteConnect" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="sinkFieldKey" label="字段" min-width="25%" :show-overflow-tooltip="true">
                    <template slot-scope="{ row }">
                      <span v-if="row.sinkFieldKey === sinkPartitionField" class="partition-wrapper">分区</span>
                      {{ row.sinkFieldKey }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="sinkFieldType" label="字段类型" min-width="20%" :show-overflow-tooltip="true"/>
                </el-table>
              </div>
              <!-- <el-button type="text" icon="el-icon-plus" @click="addCustomField" v-if="isAdd">添加自定义字段</el-button> -->
            </div>
            <div v-else>
              <dh-empty-content-new
                class="empty-content"
                msg="请先在基础配置中选择“数据来源”或“数据目的地”表"
              />
            </div>
          </section>
        </div>
      </section>

      <!-- 同步附加配置 -->
      <section ref="additionalConfig" :class="{ hide: !expand2 }">
        <block-header-tool title="同步附加配置" v-model="expand2"></block-header-tool>
        <div class="base-config-wrap" v-show="expand2">
          <additional-config-form ref="additionalConfigForm" :configData="configData" :isView="isView" :isAdd="isAdd" :isEdit="isEdit" :isOnlineEdit="isOnlineEdit"></additional-config-form>
        </div>
      </section>

      <!-- 运行配置 -->
      <section ref="runConfig" :class="{ hide: !expand3 }">
        <block-header-tool title="运行配置" v-model="expand3"></block-header-tool>
        <div class="base-config-wrap" v-show="expand3">
          <run-config-form ref="runConfigForm" :isView="isView" :isEdit="isEdit"></run-config-form>
        </div>
      </section>

      <!-- 运维配置 -->
      <section ref="operateConfig" :class="{ hide: !expand4 }">
        <block-header-tool title="运维配置" v-model="expand4"></block-header-tool>
        <div class="base-config-wrap" v-show="expand4">
          <operate-config-form ref="operateConfigForm" :isView="isView" :isEdit="isEdit" :isOnlineEdit="isOnlineEdit"></operate-config-form>
        </div>
      </section>
    </div>
    <footer class="config-page-footer" v-if="!isView">
      <div>
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button @click="handleOnline" :disabled="isOnlineEdit" v-if="isEdit">上线</el-button>
      </div>
    </footer>
    <nav-bar v-model="showNav" ref="navBar" @goNav="handleGoNav"></nav-bar>
    <nav-bar-fold v-model="showNav" ref="navBarFold"></nav-bar-fold>
  </div>
</template>

<script>
import blockHeaderTool from '../components/block-header-tool.vue'
import SourceForm from '../components/source-form.vue'
import BaseInfoForm from '../components/base-info-form.vue'
import SinkForm from '../components/sink-form.vue'
import AdditionalConfigForm from '../components/additional-config-form.vue'
import RunConfigForm from '../components/run-config-form.vue'
import OperateConfigForm from '../components/operate-config-form.vue'
import FieldConnectLine from '../components/field-connect-line.vue'
import DhVirtualSelect from '../components/dh-virtual-select.vue'
import DhEmptyContentNew from '../components/dh-empty-content-new.vue'
import NavBar from '../components/nav-bar.vue'
import NavBarFold from '../components/nav-bar-fold.vue'
import { difference, cloneDeep, uniqueId, get, pick } from 'lodash'
import { queryDataSources } from 'services/common.service'
import { saveSimpleConfig, querySimpleConfigInfo, updateOnlineSimpleConfig, releaseSimpleConfig, queryVersionInfo } from 'services/simple.service'

export default {
  name: 'ConfigAdd',
  components: {
    blockHeaderTool,
    SourceForm,
    BaseInfoForm,
    SinkForm,
    AdditionalConfigForm,
    RunConfigForm,
    OperateConfigForm,
    FieldConnectLine,
    DhVirtualSelect,
    DhEmptyContentNew,
    NavBar,
    NavBarFold
  },
  data() {
    return {
      configData: {},
      option: {
        label: 'fieldKey',
        value: 'fieldKey'
      },
      expand: true,
      expand2: true,
      expand3: true,
      expand4: true,
      mappingValue: null,
      showNav: false,
      dsTypeOptions: [
        { label: 'MySQL', value: 'MySQL' },
        { label: 'SQLServer', value: 'SQLServer' },
        { label: 'PostgreSQL', value: 'PostgreSQL' },
        { label: 'Derby', value: 'Derby' },
        { label: 'Hive', value: 'Hive' },
        { label: 'ESjdbc', value: 'ESjdbc' },
        { label: 'Oracle', value: 'Oracle' },
        { label: 'FrontRepo', value: 'FrontRepo' },
        { label: 'MySqlTenant', value: 'MySqlTenant' },
        { label: 'clickhouse', value: 'clickhouse' },
        { label: 'ApacheIgnite', value: 'ApacheIgnite' }
      ],
      dataSourceOptions: [],
      sourceDetailObject: {}, // 数据源详情
      sinkDetailObject: {}, // 数据目的详情

      sourceFields: [], // 数据源字段
      sinkFields: [], // 数据目的字段

      fields: []
    }
  },
  computed: {
    title() {
      const text = {
        view: '查看集成任务',
        edit: '编辑集成任务'
      }[this.$route.query.mode]
      return text || '新建集成任务'
    },
    isVersion() {
      return Boolean(this.$route.query.version)
    },
    isAdd() {
      return this.$route.query.mode === undefined
    },
    isView() {
      return this.$route.query.mode === 'view'
    },
    isEdit() {
      return this.$route.query.mode === 'edit'
    },
    isOnline() {
      return String(this.$route.query.jobStatus) === '1'
    },
    isOnlineEdit() {
      return this.isOnline && this.isEdit
    },
    // 数据源分区字段
    sourcePartitionField() {
      return this.getPartitionField(this.sourceDetailObject)
    },
    // 数据源目的字段
    sinkPartitionField() {
      return this.getPartitionField(this.sinkDetailObject)
    },
    // 是否展示自动映射
    isShowFiledMappingDetail() {
      const source = get(this.sourceDetailObject, 'name', '') || get(this.sourceDetailObject, 'id', '')
      const sink = get(this.sinkDetailObject, 'name', '') || get(this.sinkDetailObject, 'id', '')
      return source && sink
    },
    // 字段映射
    mapping() {
      // return get(this.transform, 'detail.mapping', [])
      return []
    },
    showMappingTable() {
      return this.sourceFields.length > 0 || this.sinkFields.length > 0
    },
    // 是否加密全部字段
    isEncryptionAll: {
      get() {
        const totalCount = this.fields.filter(field => field.sourceFieldKey).length
        const encryptionCount = this.fields.filter(field => field.enableEncryption).length
        return totalCount === encryptionCount
      },
      set(val) {
        this.fields.forEach(field => {
          field.enableEncryption = val
        })
      }
    }
  },
  methods: {
    createField(sourceField, sinkField) {
      const field = {
        sourceFieldKey: sourceField?.sourceFieldKey,
        sourceFieldType: sourceField?.sourceFieldType,
        enableEncryption: false,
        sinkFieldKey: sinkField?.sinkFieldKey,
        sinkFieldType: sinkField?.sinkFieldType,
        mappingFieldKey: undefined,
        rowKey: uniqueId('row_')
      }
      if (sourceField) {
        field.sourceFieldKey = sourceField.sourceFieldKey
        field.sourceFieldType = sourceField.sourceFieldType
        field.mappingFieldKey = sourceField.mappingFieldKey
      }
      if (sinkField) {
        field.sinkFieldKey = sinkField.sinkFieldKey
        field.sinkFieldType = sinkField.sinkFieldType
      }
      return field
    },
    goBack() {
      this.$router.push({ name: 'SimpleList' })
    },
    handleGoNav(value) {
      if (value === 'baseConfig') {
        const pageContainer = document.querySelector('.page-container')
        pageContainer.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
        return
      }
      this.$refs[value].scrollIntoView({ behavior: 'smooth' })
    },
    // 获取分区字段
    getPartitionField(detail) {
      const { id } = detail
      // 存在 id，则说明已经在 dh 平台注册
      if (id) {
        return get(detail, 'extraSpec.priPartKey', '') || get(detail, 'extraSpec.partitionField', '')
      }
      // tdw 类型且未在平台注册
      return get(detail, 'priPartKey', '')
    },
    // 修改自定义字段名称/类型
    sourceFieldChange(field) {
      const { mappingFieldKey } = field
      if (mappingFieldKey) {
        const mappedField = this.mapping.find(mappedField => mappedField.sinkFieldKey === mappingFieldKey)
        mappedField.sourceFieldKey = field.fieldKey
        mappedField.sourceFieldType = field.fieldType
      }
    },
    // 获取可选的映射目的表字段
    getFieldOptions() {
      const sinkFieldKeyList = this.fields.filter(field => field.sinkFieldKey).map(field => field.sinkFieldKey)
      const selectFieldKeyList = this.fields.filter(field => field.mappingFieldKey !== undefined).map(field => field.sinkFieldKey)
      return difference(sinkFieldKeyList, selectFieldKeyList).map(fieldKey => ({
        fieldKey
      }))
    },
    // 删除连线
    deleteConnect(field) {
      field.mappingFieldKey = undefined
      this.updateMappingArray()
    },
    // 更新任务模型中的 mapping 配置 (此函数暂无实际用途)
    updateMappingArray() {
      const mapping = []
      this.fields.filter(field => field.sinkFieldKey).forEach(field => {
        if (field.mappingFieldKey) {
          mapping.push({
            sourceFieldKey: field.sourceFieldKey,
            sourceFieldType: field.sourceFieldType,
            enableEncryption: field.enableEncryption,
            sinkFieldKey: field.sinkFieldKey,
            sinkFieldType: field.sinkFieldType
          })
        } else {
          mapping.push({
            sinkFieldKey: field.sinkFieldKey,
            sinkFieldType: field.sinkFieldType
          })
        }
      })
      // set(this.chain, 'chainConf.transform[0].detail.mapping', mapping)
    },
    // 修改对应的mapping字段
    mappingChange(mappingKey, index) {
      const sourceField = this.fields[index]
      const sinkField = this.fields.find(field => field.sinkFieldKey === mappingKey)
      // sourceField 和 sinkField 可能不在同一行
      this.swapField(sourceField, sinkField)
      this.updateMappingArray()
    },
    // 交换 source 和 sink 的字段信息
    swapField(sourceField, sinkField) {
      // 只交换来源表的信息，sink 的信息不变保持位置固定
      const tempField = { ...sourceField }
      sourceField.sourceFieldKey = sinkField.sourceFieldKey
      sourceField.sourceFieldType = sinkField.sourceFieldType
      sourceField.enableEncryption = sinkField.enableEncryption
      sourceField.mappingFieldKey = sinkField.mappingFieldKey
      sourceField.rowKey = sinkField.rowKey
      sinkField.sourceFieldKey = tempField.sourceFieldKey
      sinkField.sourceFieldType = tempField.sourceFieldType
      sinkField.enableEncryption = tempField.enableEncryption
      sinkField.mappingFieldKey = tempField.mappingFieldKey
      sinkField.rowKey = tempField.rowKey
    },
    fieldMappingChange(value) {
      if (value === 'name') {
        this.sameNameMapping()
        return
      }
      this.sameRowMapping()
    },
    // 同名映射
    sameNameMapping() {
      // 清除原有的映射关系
      this.fields.forEach(field => {
        field.mappingFieldKey = undefined
      })
      // 注意：swapField 会交换字段的顺序，所以先克隆一个副本，避免顺序变化后，没有迭代到对应的字段
      const fieldsCopy = cloneDeep(this.fields)
      fieldsCopy.forEach((sourceFieldCopy) => {
        const sinkFieldCopy = fieldsCopy.find(sinkField => sourceFieldCopy.sourceFieldKey?.toLowerCase() === sinkField.sinkFieldKey?.toLowerCase())
        if (!sinkFieldCopy) {
          return
        }
        // 存在同名映射
        // 在原始数组中找到对应的sourceField和sinkField
        const sourceField = this.fields.find(field => field.sourceFieldKey === sourceFieldCopy.sourceFieldKey)
        const sinkField = this.fields.find(field => field.sinkFieldKey === sinkFieldCopy.sinkFieldKey)
        sourceField.mappingFieldKey = sinkFieldCopy.sinkFieldKey
        this.swapField(sourceField, sinkField)
      })
      this.updateMappingArray()
      this.$message.success('已完成同名映射')
    },
    // 同行映射
    sameRowMapping() {
      this.fields.filter(field => field.sourceFieldKey && field.sinkFieldKey)
        .forEach(field => {
          field.mappingFieldKey = field.sinkFieldKey
        })
      this.updateMappingArray()
      this.$message.success('已完成同行映射')
    },
    // 取消映射
    cancelMapping() {
      this.mappingValue = ''
      this.fields.forEach(field => {
        field.mappingFieldKey = undefined
      })
      this.updateMappingArray()
    },
    // 添加自定义字段
    addCustomField() {
      const field = this.fields.find(field => field.sourceFieldKey === undefined)
      if (field) {
        field.sourceFieldKey = ''
        field.sourceFieldType = 'customString'
        field.mappingFieldKey = undefined
        field.enableEncryption = false
        field.rowKey = uniqueId('row_')
      } else {
        this.fields.push({
          sourceFieldKey: '',
          sourceFieldType: 'customString',
          mappingFieldKey: undefined,
          enableEncryption: false,
          sinkFieldKey: undefined,
          sinkFieldType: undefined,
          rowKey: uniqueId('row_')
        })
        this.$nextTick(() => {
          const tableBody = this.$refs.tableRef.$el.querySelector('.el-table__body-wrapper')
          tableBody.scrollTop = tableBody.scrollHeight
        })
      }
    },
    getDataSourceOptions() {
      // 获取数据源
      const params = {}
      queryDataSources(params).then(res => {
        this.dataSourceOptions = res.map(item => {
          const { dsProperties, ...others } = item
          return { ...dsProperties, ...others, label: `${item.dsName} [${dsProperties.dsUserName}]` }
        })
      }).catch(res => {
        this.dataSourceOptions = []
        this.$message.error('获取数据源列表失败！原因：' + res.message || '暂无')
      })
    },
    async handleSubmit() {
      const formNames = ['baseInfoForm', 'sourceForm', 'sinkForm', 'additionalConfigForm', 'runConfigForm', 'operateConfigForm']

      const promiseArray = formNames.reduce((prev, names) => {
        prev.push(this.$refs[names].formValidate())
        return prev
      }, [])

      const result = await Promise.all(promiseArray)
      if (result.every(v => v)) {
        this.fetchSave()
        return 
      }
      this.$message.error('请按要求填写完整表单项！')
    },
    fetchSave() {
      const subData = this.getSubData()
      // 上线状态 只能编辑jobSetting 调用updateOnline
      if (this.isOnlineEdit) {
        updateOnlineSimpleConfig({ configId: this.configData.configId, jobSetting: subData.jobSetting }).then(res => {
          this.$message.success('操作成功！')
          this.$router.push({ name: 'SimpleList' })
        }).catch(res => {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
        return
      }
      if (this.isEdit) {
        Object.assign(subData, { configId: this.$route.query.configId })
      }
      saveSimpleConfig(subData).then(res => {
        this.$message.success('操作成功！')
        this.isAdd && this.$router.push({ name: 'SimpleList' })
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
      })
    },
    getSubData() {
      const baseInfo = this.$refs.baseInfoForm.getSubData()
      const sourceInfo = this.$refs.sourceForm.getSubData()
      const sinkInfo = this.$refs.sinkForm.getSubData()
      const additionInfo = this.$refs.additionalConfigForm.getSubData()
      const runConfigInfo = this.$refs.runConfigForm.getSubData()
      const operateConfigInfo = this.$refs.operateConfigForm.getSubData()
      const sourceColumns = []
      const sinkColumns = []
      this.fields.filter(field => field.mappingFieldKey).forEach((field, index) => {
        sourceColumns.push({
          name: field.sourceFieldKey,
          type: field.sourceFieldType,
          enableEncryption: field.enableEncryption,
          seq: index + 1
        })
        sinkColumns.push({
          name: field.sinkFieldKey,
          type: field.sinkFieldType,
          seq: index + 1
        })
      })
      const data = {
        ...baseInfo,
        jobVersion: this.configData.jobVersion || 1, // 新增时传1
        jobDesc: {
          sources: [
            {
              properties: { ...sourceInfo, fetchSize: additionInfo.fetchSize, splitSize: additionInfo.splitSize },
              columns: sourceColumns
            }
          ],
          sink: {
            overrideStrategy: sinkInfo.overrideStrategy,
            pkCollisionStrategy: sinkInfo.pkCollisionStrategy,
            enableEncryption: additionInfo.enableEncryption,
            encryptionWay: additionInfo.encryptionWay,
            encryptionPublicKey: additionInfo.encryptionPublicKey,
            encryptionPrivateKey: additionInfo.encryptionPrivateKey,
            properties: { ...sinkInfo.properties },
            columns: sinkColumns
          }
        },
        jobSetting: {
          ...runConfigInfo,
          ...operateConfigInfo
        }
      }
      return data
    },
    updateSourceFields(fields) {
      this.sourceFields = fields
      this.getFields()
      if (this.isAdd) return
      this.setMapping()
    },
    updateSinkFields(fields) {
      this.sinkFields = fields
      this.getFields()
      if (this.isAdd) return
      this.setMapping()
    },
    getFields() {
      const array = []
      const maxLength = Math.max(this.sourceFields.length, this.sinkFields.length)
      for (let i = 0; i < maxLength; i++) {
        const sourceField = this.sourceFields[i]
        const sinkField = this.sinkFields[i]
        array.push(this.createField(sourceField, sinkField))
      }
      this.fields = array
    },
    // 设置映射关系
    setMapping() {
      if (this.sourceFields.length === 0 || this.sinkFields.length === 0) {
        return
      }
      const sinkColumns = this.configData.jobDesc.sink.columns
      const sourceColumns = this.configData.jobDesc.sources[0].columns
      const mapping = []
      sourceColumns.forEach(column => {
        const sinkCol = sinkColumns.find(item => item.seq === column.seq)
        if (sinkCol) {
          mapping.push({ sourceFieldKey: column.name, enableEncryption: column.enableEncryption, sinkFieldKey: sinkCol.name, mappingFieldKey: sinkCol.name })
        }
      })
      this.fields.forEach((field, index) => {
        const mappingField = mapping.find(item => item.sourceFieldKey === field.sourceFieldKey)
        if (mappingField) {
          field.enableEncryption = mappingField.enableEncryption
          field.mappingFieldKey = mappingField.mappingFieldKey
          // 设置横线
          this.mappingChange(field.mappingFieldKey, index)
        }
      })
    },
    getConfigData() {
      const { configId } = this.$route.query
      if (!configId) return
      if (this.isVersion) {
        queryVersionInfo({ id: configId, version: this.$route.query.version }).then(res => {
          this.configData = res
          this.setComponentData()
        }).catch(res => {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
        return
      }
      querySimpleConfigInfo({ id: configId }).then(res => {
        this.configData = res
        this.setComponentData()
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
      })
    },
    // 设置组件的数据
    setComponentData() {
      const { appKey, appName, interfaceCode, interfaceName, interfaceDesc, owners, jobType, jobExecMode, integrationMode } = this.configData
      this.$refs.baseInfoForm.setData({ appKey, appName, interfaceCode, interfaceName, interfaceDesc, owners, jobType, jobExecMode, integrationMode })

      const [sourceData] = this.configData.jobDesc.sources
      const partData = pick(sourceData.properties, ['dsType', 'dsId', 'dsName', 'table-name', 'filter_sql'])
      this.$refs.sourceForm.setData(partData)

      const { overrideStrategy, pkCollisionStrategy, properties } = this.configData.jobDesc.sink
      this.$refs.sinkForm.setData({ overrideStrategy, pkCollisionStrategy, properties })

      const { enableEncryption, encryptionWay, encryptionPublicKey, encryptionPrivateKey } = this.configData.jobDesc.sink
      this.$refs.additionalConfigForm.setData({ fetchSize: sourceData.properties.fetchSize, splitSize: sourceData.properties.splitSize, enableEncryption, encryptionWay, encryptionPublicKey, encryptionPrivateKey })

      const { scheduleStrategy, timeoutStrategy, failureStrategy, resources, alertStrategy } = this.configData.jobSetting
      this.$refs.runConfigForm.setData({ scheduleStrategy, timeoutStrategy, failureStrategy, resources })
      this.$refs.operateConfigForm.setData({ alertStrategy })
    },
    handleOnline() {
      const { configId } = this.$route.query
      if (!configId) return
      releaseSimpleConfig({ id: configId, releaseState: 'ONLINE' }).then(res => {
        this.$message.success('操作成功！')
        this.$router.push({ name: 'SimpleList' })
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
      })
    }
  },
  mounted() {
    this.getDataSourceOptions()
    this.getConfigData()

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const dom = this.$refs.navBar
          dom.$el.style.left = (entry.target.offsetWidth + 16) + 'px'
          const foldDom = this.$refs.navBarFold
          foldDom.$el.style.left = (entry.target.offsetWidth + 187) + 'px'
          // 当元素进入视口后，取消监听
          observer.unobserve(entry.target)
        }
      })
    })
    const target = this.$refs.configPage
    // 开启监听
    if (target) observer.observe(target)
  }
}
</script>

<style lang="less">
.config-add-page {
  padding: 10px 20px 20px 20px;
  max-width: @page-config-max-width;
  min-width: @page-config-min-width;
  .config-page-header {
    width: 100%;
    height: @page-header;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 1001;
    background: #fff;
    border-bottom: 1px solid #eee;
  }
  .config-page-main {
    .hide {
      margin-bottom: 20px;
    }
    .base-config-wrap {
      padding: 10px 0 10px 10px;
      .base-config-title {
        color: #333333;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        margin-bottom: 10px;
      }
      .base-config-source {
        display: flex;
        justify-content: space-between;
        .icon-wrap {
          height: 40px;
          display: flex;
          align-items: center;
          font-size: 16px;
        }
      }
      .field-mapping-tool {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
        margin-bottom: 10px;
        .el-radio {
          margin-right: 10px;
        }
      }
      .field-mapping-table {
        margin-bottom: 10px;
      }
    }
    .base-width {
      width: 600px;
    }
  }
  .config-page-footer {
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 72px;
    background: #fff;
    z-index: 1001;
    border: 1px solid #eee;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    bottom: 0;
  }
}

.field-mapping-table {
  .el-table {
    color: #4A5970;
    font-weight: normal;
    .field-selector {
      min-width: 120px;
    }
    ::v-deep {
      .cell {
        height: 31px;
        line-height: 31px;
      }

      .el-input__inner {
        height: 30px;
        line-height: 30px;
        color: #4A5970;
      }

      .partition-wrapper {
        background: rgb(255, 231, 232);
        color: rgb(241, 104, 105);
        padding: 0 5px;
        text-align: center;
        border: 1px solid rgb(241, 104, 105);
        border-radius: 4px;
      }
    }
    .connect-line-col {
      background: none;
      border-left: 1px solid #D8D8D8 !important;
      border-right: 1px solid #D8D8D8 !important;
      border-top: 0;
      border-bottom: 0;
      .cell {
        padding: 0;
        display: flex;
        align-items: center;
      }
    }
  }
}

.empty-content {
  padding: 130px 0;
}

// 额外的表头背景色保险方案
.field-mapping-table .el-table__header-wrapper {
  border-left: 1px solid #D8D8D8;
  border-right: 1px solid #D8D8D8;
  .el-table__header {
    th.el-table__cell {
      background: #f5f7f9 !important;
      border-top: 1px solid #D8D8D8;
    }

    // 连线列特殊处理
    th.connect-line-col {
      background: none !important;
      border-top: 0;
    }
  }
}

// 表格主体连线列样式
.field-mapping-table .el-table__body-wrapper {
  border-left: 1px solid #D8D8D8;
  border-right: 1px solid #D8D8D8;
  .el-table__body {
    .connect-line-col {
      background: none;
    }
  }
}
</style>

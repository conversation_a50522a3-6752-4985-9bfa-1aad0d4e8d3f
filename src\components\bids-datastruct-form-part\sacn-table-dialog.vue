<!--
 * @Description: 扫描表结构弹窗组件，扫描数据库表得到数据的字段
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-09 10:15:33
-->
<template>
  <div class="scan-table-dialog">
    <el-dialog title="扫描表结构" :visible.sync="visible" :before-close="handleClose" width="600px">
      <el-form :model="form" :rules="rules" ref="form" label-position="left" label-width="140px">
        <el-form-item label="所属应用">
          <span>{{initData.dsAppName || initData.dsAppKey}}</span>
        </el-form-item>
        <el-form-item label="来源库类型">
          <span>{{initData.dsType}}</span>
        </el-form-item>
        <el-form-item label="数据源地址">
          <span>{{initData.dsProperties ? initData.dsProperties.dsUrl : ''}}</span>
        </el-form-item>
        <el-form-item label="扫描账号">
          <span>{{initData.dsProperties ? initData.dsProperties.dsUserName : ''}}</span>
        </el-form-item>
        <el-form-item label="待扫描的表名" prop="tableName" v-if="initData.dsType !== 'HTTP'">
          <el-input v-model="form.tableName" size="medium"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="medium">取消</el-button>
        <!-- <el-button size="medium" @click="handleTestScan">测试扫描</el-button> -->
        <el-button size="medium" type="primary" @click="handleConfirmScan" :loading="loading">确认扫描</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { scanDsTable } from 'services/common.service'

export default {
  name: 'scan-table-dialog',
  props: {
    initData: {
      /* { dsAppName, dsAppKey, dsId, dsName, dsType, dsProperties: { dsUrl, dsUserName }, ... } */
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {},
      rules: {
        tableName: [{ required: true, message: '请输入待扫描的表名', trigger: 'blur' }]
      },
      visible: false,
      loading: false
    }
  },
  methods: {
    show() {
      this.visible = true
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    handleTestScan() {
    },
    handleConfirmScan() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false
        } else {
          this.loading = true
          const params = { dsId: this.initData.dsId }
          const postData = { tableName: this.form.tableName }
          scanDsTable(params, postData).then(res => {
            if (res.columns.length === 0) {
              this.$message.info('未扫描到数据字段！')
            } else {
              if (res.columns.some(item => !item.type)) {
                this.$message.warning('扫描成功，但存在未能识别类型的字段，请手工定义！')
              } else {
                this.$message.success('扫描成功！')
              }
            }
            this.$emit('scan', res.columns)
            this.loading = false
            this.visible = false
          }).catch(res => {
            this.loading = false
            this.$message.error('扫描失败！原因：' + res.message || '暂无')
          })
        }
      })
    }
  }
}
</script>

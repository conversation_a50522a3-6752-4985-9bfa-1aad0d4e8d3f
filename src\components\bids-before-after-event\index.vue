<!--
 * @Description: 前置后置配置页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-30 21:03:38
-->
<template>
  <div class="bids-before-after-event">
    <el-tabs v-model="activeEvent" class="button-tabs" v-if="configData.collectType === 1">
      <el-tab-pane label="前置事件" name="beforeEvent" :lazy="true">
        <event-form-part ref="beforeEvent" type="beforeEvent" :pageStatus="pageStatus"></event-form-part>
      </el-tab-pane>
      <el-tab-pane label="后置事件" name="afterEvent" :lazy="true">
        <event-form-part ref="afterEvent" type="afterEvent" :pageStatus="pageStatus"></event-form-part>
      </el-tab-pane>
    </el-tabs>
    <div class="description-panel" v-if="configData.collectType === 2" style="right:30%;top:30px;" >
      <div class="question">
        <i class="el-icon-question"></i>
        <span>无需配置</span>
      </div>
      <div class="answer">
        <div>增量输出的数据订阅无需定义前置、后置事件</div>
      </div>
    </div>
    <div class="description-panel" v-else>
      <div class="question">
        <i class="el-icon-question"></i>
        <span>前置后置事件</span>
      </div>
      <div class="answer">
        <span>非必填，可按实际需求配置</span>
      </div>
    </div>
  </div>
</template>

<script>
import EventFormPart from './event-form-part'
import { mapState } from 'vuex'
export default {
  components: {
    EventFormPart
  },
  props: {
    pageStatus: String
  },
  data() {
    return {
      activeEvent: 'beforeEvent'
    }
  },
  computed: {
    ...mapState({
      configData: 'currentSubscribeConfig'
    })
  },
  created() {
    this.updatePageData()
  },
  methods: {
    updatePageData() {
      this.$refs['beforeEvent'] && this.$refs['beforeEvent'].updatePageData()
      this.$refs['afterEvent'] && this.$refs['afterEvent'].updatePageData()
      this.$refs[this.activeEvent] && this.$refs[this.activeEvent].doLayout()
    },
    formValidate() {
      let validRes = true
      if (this.$refs['beforeEvent']) {
        validRes = validRes && this.$refs['beforeEvent'].formValidate()
      }
      if (this.$refs['afterEvent']) {
        validRes = validRes && this.$refs['afterEvent'].formValidate()
      }
      return validRes
    }
  }
}
</script>
<style lang="less">
  @import "~assets/css/bids-before-after-event.less";
</style>

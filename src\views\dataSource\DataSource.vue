<!--
 * @Description: 我的管理-数据源页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-07-27 18:25:01
-->
<template>
  <div class="data-source">
    <div class="page-header">
      <div>管理数据源</div>
      <el-button type="primary" size="medium" @click="handleAdd">新增数据源</el-button>
    </div>
    <el-divider></el-divider>
    <div>
      <el-form :inline="true" :model="form">
        <el-form-item label="所属应用">
          <el-select size="small" v-model="form.dsAppName" clearable @change="filterMethod">
            <el-option v-for="item in appOptions" :key="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据库类型">
          <el-select size="small" v-model="form.dsType" clearable @change="filterMethod">
            <el-option v-for="item in dsTypeOptions" :key="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据源名称">
          <el-input size="small" v-model="form.dsName" @input="debounceFilter"></el-input>
        </el-form-item>
        <el-form-item label="账号">
          <el-input size="small" v-model="form.dsUserName" @input="debounceFilter"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-main">
      <el-table 
        v-loading="loading"
        :data="tableData"
        border
        height="100%"
        :header-cell-style="{background:'#f5f7f9', height: '60px'}">
        <el-table-column
          v-for="(item, index) in tableColumn" :key="index"
          :prop="item.prop"
          :formatter="item.formatter"
          :label="item.label"
          :min-width="item.minWidth || item.width"
          :show-overflow-tooltip="true"
          align="center">
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          align="center"
          width="100">
          <template slot-scope="scope">
            <el-button @click="handleEdit(scope.$index, scope.row)" type="text" size="small">编辑</el-button>
            <!-- <el-button @click="handleAuth(scope.$index, scope.row)" type="text" size="small">授权</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <bids-datasource-dialog ref="updateDatasouceDialog" type="update" @submit="handleSubmitDs" @delete="handleDeleteDs"/>
    <bids-datasource-add-dialog ref="addDatasouceDialog" type="add" :options="addDsOptions" @submit="handleSubmitDs"></bids-datasource-add-dialog>
    <authed-apps-dialog ref="authedAppsDialog"></authed-apps-dialog>
  </div>
</template>

<script>
import BidsDatasourceDialog from 'components/bids-datasource-dialog'
import BidsDatasourceAddDialog from 'components/bids-datasource-add-dialog'
import { queryDataSources } from 'services/common.service'
import { DataEvent } from 'sdc-core'
import AuthedAppsDialog from './authed-apps-dialog.vue'

export default {
  components: {
    BidsDatasourceDialog,
    AuthedAppsDialog,
    BidsDatasourceAddDialog
  },
  data() {
    return {
      form: { dsAppName: '', dsType: '', dsName: '', dsUserName: '' },
      allData: [],
      tableData: [], // 数据源表格数据
      loading: false,
      tableColumn: [
        // { prop: 'dsId', label: '数据源ID', minWidth: '120' },
        { prop: 'dsAppName', label: '所属应用', minWidth: '120', formatter: (row) => row.dsAppName || row.dsAppKey },
        { prop: 'dsType', label: '数据库类型', minWidth: '80' },
        { prop: 'dsName', label: '数据源名称', minWidth: '120' },
        { prop: 'dsUserName', label: '账号', minWidth: '80' },
        { prop: 'creator', label: '创建人', minWidth: '100' },
        { prop: 'createTime', label: '创建时间', minWidth: '150' }
      ],
      debounceFilter: DataEvent.debounce(this.filterMethod, 500),
      addDsOptions: [
        { value: 'ESjdbc', label: 'ESjdbc' }, { value: 'ES6', label: 'Elasticsearch 6' }, { value: 'ES7', label: 'Elasticsearch 7' }, 
        { value: 'HDFS', label: 'HDFS' }, { value: 'Hive', label: 'Hive' }, 
        { value: 'http_write_json', label: 'http_write_json' }, { value: 'http_write_file', label: 'http_write_file' }, 
        { value: 'Kafka', label: 'Kafka_0.11及以后版本' },
        { value: 'MongoDB', label: 'MongoDB' }, { value: 'MySQL', label: 'MySQL' }, { value: 'Oracle', label: 'Oracle' }, { value: 'PostgreSQL', label: 'PostgreSQL' },
        { value: 'SQLServer', label: 'SQLServer' }, { value: 'MySqlTenant', label: 'MySqlTenant' }, { value: 'clickhouse', label: 'clickhouse' }, { value: 'COS', label: 'COS' },
        { value: 'ApacheIgnite', label: 'ApacheIgnite' }, { value: 'HTTP', label: 'HTTP' }, { value: 'starrocks', label: 'starrocks' }
      ]
    }
  },
  computed: {
    appOptions() {
      return [...new Set(this.tableData.map(item => item.dsAppName || item.dsAppKey))].sort() // 兼容老数据没有dsAppName
    },
    dsTypeOptions() {
      return [...new Set(this.tableData.map(item => item.dsType))].sort()
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    handleAdd() {
      this.$refs.addDatasouceDialog.show({ dsNameList: this.tableData.map(item => item.dsName) })
    },    
    handleEdit(index, row) {
      const data = { index, ...row, dsNameList: this.tableData.map(item => item.dsName).filter(item => item !== row.dsName) }
      !data.agentType && ['MySQL', 'Oracle', 'SQLServer', 'PostgreSQL', 'clickhouse', 'ApacheIgnite', 'MongoDB'].includes(row.dsType) && (data.agentType = 'NO_AGENT')
      if (data.dsType === 'COS') {
        const { dsUserName, dsUrl } = data
        const [bucket, region] = dsUrl.split('|')
        const obj = {
          secretKey: '',
          secretId: dsUserName,
          bucket,
          region
        }
        Object.assign(data, obj)
      }
      if (data.dsType === 'HTTP') {
        const { requestParams, requestHeader } = data
        data.requestParams = this.getConvertData(requestParams)
        data.requestHeader = this.getConvertData(requestHeader)
      }
      this.$refs.updateDatasouceDialog.show(data)
    },
    handleAuth(index, row) {
      this.$refs.authedAppsDialog.show(row)
    },
    fetchData() {
      // 获取数据源
      const params = {}
      this.loading = true
      queryDataSources(params).then(res => {
        this.allData = res.map(item => {
          const { dsProperties, ...others } = item
          return { ...dsProperties, ...others }
        })
        this.filterMethod()
        this.loading = false
      }).catch(res => {
        this.allData = []
        this.tableData = []
        this.loading = false
        this.$message.error('获取数据源列表失败！原因：' + res.message || '暂无')
      })
    },
    filterMethod() {
      this.tableData = this.allData.filter(item => {
        const appMatch = !this.form.dsAppName || item.dsAppName === this.form.dsAppName || (!item.dsAppName && item.dsAppKey === this.form.dsAppName) // 兼容老数据没有dsAppName
        const dsTypeMatch = !this.form.dsType || item.dsType === this.form.dsType
        const dsNameMatch = !this.form.dsName || item.dsName.toLowerCase().includes(this.form.dsName.toLowerCase())
        const dsUserNameMatch = !this.form.dsUserName || (item.dsUserName && item.dsUserName.toLowerCase().includes(this.form.dsUserName.toLowerCase()))
        return appMatch && dsTypeMatch && dsNameMatch && dsUserNameMatch
      })
    },
    handleSubmitDs() {
      // const { index, ...row } = option
      // this.tableData.splice(index, 1, row) // 修改提交成功后更新表格数据
      this.fetchData()
    },
    handleDeleteDs() {
      // const { index } = option
      // this.tableData.splice(index, 1) // 删除成功后更新表格数据
      this.fetchData()
    },
    getConvertData(params) {
      let data = [{ key: '', value: '' }]
      if (params) {
        const obj = JSON.parse(params)
        data = Object.entries(obj).reduce((prev, item) => {
          const [key, value] = item
          return prev.push({ key, value }), prev
        }, [])
      }
      return data
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/data-source.less";
</style>

import { http } from 'utils/http'
import api from 'config/api.conf'

const getUrlByEnv = (url, env) => {
  // 相对路径
  if (env === process.env.VUE_APP_BUILD_ENV) {
    return '/hr-bidsplus-config/api' + url
  }
  const prefix = {
    dev: 'https://dev-ntsapps.woa.com',
    uat: 'https://uat-ntsapps.woa.com',
    prd: 'https://ntsapps.woa.com'
  }[env]

  return prefix + url
}

export const querySyncPubPageList = (data, env) => {
  const url = getUrlByEnv(api.special.queryPubPageList, env)
  return http.post(url, { params: data, loading: true })
}
export const querySyncSubPageList = (data, env) => {
  const url = getUrlByEnv(api.special.querySubPageList, env)
  return http.post(url, { params: data, loading: true })
}

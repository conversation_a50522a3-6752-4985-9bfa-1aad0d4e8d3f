<template>
  <div class="nav-bar-fold" ref="navBarFold" v-show="!value">
    <div>页</div>
    <div>面</div>
    <div>导</div>
    <div>航</div>
    <el-button type="text" icon="el-icon-arrow-left" style="color: #606266; padding: 0;" @click="$emit('input', !value)"></el-button>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {

    }
  },
  methods: {

  }
}
</script>

<style lang="less" scoped>
.nav-bar-fold {
  position: fixed;
  top: 300px;
  right: 20px;
  width: 23px;
  height: 108px;
  flex-shrink: 0;
  border-radius: 4px 0 0 4px;
  border: 1px solid #CDCDCD;
  background: #FFF;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 3px;
  gap: 4px;
  z-index: 1001;
}
</style>

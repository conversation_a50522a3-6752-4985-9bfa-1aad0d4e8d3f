/*
 * @Description: 搜索框属性和方法
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-31 20:39:15
 */
import { DataEvent } from 'sdc-core'
export default {
  data() {
    return {
      filterForm: { appName: '', interfaceName: '', creatorName: '' },
      criteriaValue: '',
      criteriaKey: 'interfaceName',
      debounceSearch: DataEvent.debounce(this.fetchData, 500),
      pubFilterOptions: [
        { label: '应用名', value: 'appName' },
        { label: '发布名称', value: 'interfaceName' },
        { label: '发布编码', value: 'interfaceCode' },
        { label: '描述信息', value: 'interfaceDesc' }
      ],
      subFilterOptions: [
        { label: '应用名', value: 'appName' },
        { label: '订阅名称', value: 'interfaceName' },
        { label: '订阅编码', value: 'interfaceCode' },
        { label: '描述信息', value: 'interfaceDesc' }
      ]
    }
  },
  methods: {
    handleInput() {
      this.pageIndex !== 1 && (this.pageIndex = 1)
      this.debounceSearch()
    },
    handleAppChange() {
      this.pageIndex !== 1 && (this.pageIndex = 1)
      this.fetchData()
    },
    handleChange() {
      if (this.criteriaValue === '') return
      this.pageIndex !== 1 && (this.pageIndex = 1)
      this.fetchData()
    }
  }
}

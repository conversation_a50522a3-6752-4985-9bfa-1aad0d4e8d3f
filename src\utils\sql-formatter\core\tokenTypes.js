/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-01-22 15:33:27
 */
/**
 * Constants for token types
 */
export default {
  WHITESPACE: 'whitespace',
  WORD: 'word',
  STRING: 'string',
  RESERVED: 'reserved',
  RESERVED_TOPLEVEL: 'reserved-toplevel',
  RESERVED_NEWLINE: 'reserved-newline',
  OPERATOR: 'operator',
  OPEN_PAREN: 'open-paren',
  CLOSE_PAREN: 'close-paren',
  LINE_COMMENT: 'line-comment',
  BLOCK_COMMENT: 'block-comment',
  NUMBER: 'number',
  PLACEHOLDER: 'placeholder'
}

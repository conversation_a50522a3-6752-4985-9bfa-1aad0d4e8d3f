<!--
 * @Description: 数据订阅-数据的订阅规则组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-07-29 20:04:21
-->
<template>
  <div class="bids-subscribe-rule">
    <div class="subscribed-table">
      <el-button size="mini" style="float:right;" type="primary" icon="el-icon-plus" @click="handleAdd" v-if="moderateEditable">新增</el-button>
      <div class="label">需订阅的数据发布：{{selectedData.length}}</div>
      <el-table
        :data="selectedData"
        height="370px"
        highlight-current-row
        ref="dataTable"
        @current-change="handleCurrentChange"
        :header-cell-style="{background:'#f5f7f9'}"
        style="width: 100%">
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left" class="table-expand" label-width="110px">
              <el-form-item v-for="(item, index) in dataTableColumn.expandPart" :key="index" :label="item.label">
                <span>{{ item.formatter ? item.formatter(props.row) : props.row[item.prop]}}</span>
              </el-form-item>]
            </el-form>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in dataTableColumn.colPart" :key="index"
          :prop="item.prop"
          :formatter="item.formatter"
          :label="item.label"
          :min-width="item.minWidth || item.width"
          align="center">
          <div slot-scope="scope">
            <el-dropdown trigger="click" v-if="item.prop === 'version' && moderateEditable"
            @command="(val) => handleCommand(val, scope.$index, scope.row)" 
            @visible-change="(val) => handleDropdown(val, scope.row)">
              <span class="el-dropdown-link">
                {{scope.row[item.prop]}}<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu size="small" slot="dropdown">
                <el-dropdown-item v-for="item in dropdownItems" :command="item.version" :key="item.version" :disabled="item.jobStatus !== 1">{{item.version}}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span v-else-if="item.prop === 'interfaceName'">
              <a class="anchor" @click="handleGoPub(scope.row)" @contextmenu.prevent.stop="rightClick($event, publishUrl(scope.row))">{{ scope.row[item.prop] }}</a>
            </span>
            <span v-else-if="item.prop === 'sinkTableName'">{{formatterSinkTableName(scope.row)}}</span>
            <span v-else>{{scope.row[item.prop]}}</span>
          </div>
        </el-table-column>
        <el-table-column
          v-if="moderateEditable"
          align="center"
          label="操作"
          width="50">
          <template slot-scope="scope">
            <el-button @click="handleRemove(scope.$index, scope.row)" type="text" size="small" class="button-text-danger">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="field-table">
      <div class="label">数据表的字段：{{structData.length}}</div>
      <el-table
        :data="structData"
        height="370px"
        border
        style="width: 100%">
        <el-table-column
          v-for="(item, index) in dataStructColumn" :key="index"
          :prop="item.prop"
          :formatter="item.formatter"
          :label="item.label"
          :min-width="item.minWidth || item.width"
          :show-overflow-tooltip="true"
          align="center">
        </el-table-column>
      </el-table>
      <div class="arrow"></div>
    </div>
    <div class="config-item" :class="[!subScriptValid ? 'config-item__error' : '']">
      <div class="label label-required">数据订阅规则</div>
      <div class="panel panel-border">
        <div class="codemirror-wrap">
          <el-tooltip content="格式化">
            <i class="el-icon-brush" @click="handleFormatter"></i>
          </el-tooltip>
          <codemirror ref="cmEditor" :value="subscribeScript" @beforeChange="handleBeforeChange" :options="{readOnly: !moderateEditable, hintOptions: hintOptions}"
            @input="handleSubscribeScriptChange" @ready="handleCmReady"/>
        </div>
      </div>
      <div class="error-message" v-if="!subScriptValid && scriptErrorMsg">{{scriptErrorMsg}}</div>
    </div>
    <div class="description-panel" v-if="mildEditable">
      <div class="question">
        <i class="el-icon-question"></i>
        <span>需订阅的数据发布</span>
      </div>
      <div class="answer">
        <span>请先选择需订阅的数据发布，可以多选，但不能重复选择某一个数据发布下的多个版本</span>
      </div>
      <div class="question">
        <i class="el-icon-question"></i>
        <span>数据订阅规则</span>
      </div>
      <div class="answer">
        <span>使用FlinkSQL，支持</span>
        <el-link type="primary" href="https://ci.apache.org/projects/flink/flink-docs-release-1.11/dev/table/functions/systemFunctions.html" target="_blank">UDF自定义函数</el-link>
        <div>SQL语句中需包含订阅的数据发布列表里的<b>数据表名</b>，选取的列名也必须来自对应数据表内的已有列名</div>
        <div>例子：SELECT colA FROM 数据表名1</div>
      </div>
    </div>
    <el-drawer
      :visible.sync="drawer"
      size="60%"
      direction="rtl">
      <div class="drawer-title" slot="title">选择需订阅的数据发布</div>
      <choose-pub-data @added="handleAdded"></choose-pub-data>
    </el-drawer>
    <bids-popover :rightClickFlag.sync="rightClickFlag" :contextMenu="contextMenu" :clientX="clientX" :clientY="clientY" @handleGoNewTab="handleGoNewTab"></bids-popover>
  </div>
</template>
<script>
import { formatter, editable, sqlExpressions, popoverStatus } from 'mixins'
import { mapState, mapMutations } from 'vuex'
import { queryBatch, queryData } from 'services/publish.service'
import sqlFormatter from 'utils/sql-formatter/sqlFormatter'
import ChoosePubData from './choose-pub-data'
import { DataUtil } from 'sdc-core'
import BidsPopover from '../bids-popover'

export default {
  name: 'bids-subscribe-rule',
  components: {
    ChoosePubData,
    BidsPopover
  },
  mixins: [formatter, editable, sqlExpressions, popoverStatus],
  props: {
    pageStatus: String
  },
  computed: {
    ...mapState({
      configData: state => state.currentSubscribeConfig,
      dataToBeSub: state => state.dataToBeSub
    }),
    selectedData: {
      get() {
        return this.$store.state.dataToBeSub
      },
      set(val) {
        this.UPDATE_DATA_TO_BE_SUB(val)
      }
    }
  },
  data() {
    return {
      configType: 'subscribe',
      subscribeScript: '',
      scriptErrorMsg: '',
      structData: [],
      subScriptValid: true,
      drawer: false,
      dataTableColumn: {
        colPart: [
          { prop: 'interfaceName', label: '发布名称', minWidth: '110' },
          { prop: 'version', label: '版本', minWidth: '50' },
          // { prop: 'dataFormat', label: '数据类型', minWidth: '80', formatter: this.formatterDataFormat },
          { prop: 'sinkTableName', label: '数据表名', minWidth: '120', formatter: this.formatterSinkTableName }
        ],
        expandPart: [
          { prop: 'collectType', label: '采集类型', formatter: this.formatterCollectType },
          { prop: 'scheduleUnit', label: '更新周期', formatter: this.formatterScheduleAtCron },
          { prop: 'appName', label: '所属应用' },
          { prop: 'interfaceCode', label: '接口编码' },
          { prop: 'interfaceDesc', label: '接口描述' }
        ]
      },
      dataStructColumn: [
        { prop: 'name', label: '列名', minWidth: '130' },
        { prop: 'type', label: '数据类型', minWidth: '90' },
        // { prop: 'seq', label: '序列号', minWidth: '55' },
        { prop: 'abstract', label: '摘要', minWidth: '110', formatter: this.formatterAbstract }
        // { prop: 'default', label: '默认值', minWidth: '60' }
      ],
      dropdownItems: []
    }
  },
  created() {
    this.updatePageData()
  },
  beforeDestroy() {
    this.UPDATE_DATA_TO_BE_SUB([])
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM', 'UPDATE_DATA_TO_BE_SUB']),
    updatePageData() {
      if (this.dataToBeSub.length === 0) {
        this.fetchData()
      } else {
        this.$nextTick(() => { this.$refs.dataTable && this.$refs.dataTable.setCurrentRow(this.selectedData[0]) })
      }
      this.subscribeScript = this.configData.job.transform.script
      this.$refs['cmEditor'] && this.$refs['cmEditor'].refresh()
    },
    // handleBeforeChange(cm, changeObj) {
    //   const text = changeObj.text.map(item => item.trim()).join(' ')
    //   if (text && text.search(/导入表.*的全部列/g) !== -1) {
    //     const data = this.dataToBeSub.find(item => text.indexOf(this.formatterSinkTableName(item)) !== -1)
    //     if (data.job.sink.columns) {
    //       const str = data.job.sink.columns.map(item => item.name).join(', ')
    //       changeObj.from.ch = changeObj.from.ch - 1
    //       changeObj.update(changeObj.from, changeObj.to, [str])
    //     } else {
    //       changeObj.cancel()
    //     }
    //   }
    // },
    handleAdd() {
      this.drawer = true
    },
    handleAdded() {
      this.drawer = false
      this.$nextTick(() => { this.$refs.dataTable && this.$refs.dataTable.setCurrentRow(this.selectedData[this.selectedData.length - 1]) })
    },
    handleRemove(index, row) {
      this.$confirm('确认移除该行需订阅的数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const sources = DataUtil.clone(this.configData.job.sources)
        sources.splice(sources.findIndex(item => item.properties.subConfigId === row.configId), 1)
        const payload = { type: 'subscribe', keyPath: ['job', 'sources'], value: sources }
        this.UPDATE_CONFIG_ITEM(payload)
        this.selectedData.splice(index, 1)
      }).catch(res => {})
    },
    handleCurrentChange(row) {
      this.structData = row ? row.job.sink.columns : []
    },
    handleDropdown(val, row) {
      if (val) {
        this.dropdownItems = []
        const params = { configId: row.configId }
        queryData(params).then(res => {
          if (res.length > 0) {
            this.dropdownItems = res
          }
        }).catch(res => {
          this.$message.error('获取该数据发布的版本详情失败！原因：' + res.message || '暂无')
        })
      }
    },
    handleCommand(version, index, row) {
      if (version === row.version) return
      const newData = this.dropdownItems.find(item => item.version === version)
      const loc = this.configData.job.sources.findIndex(item => item.properties.subConfigId === row.configId)
      if (loc < 0) {
        this.$message.error('切换版本失败！请尝试先删除该版本，再添加新的版本')
        return
      }
      this.selectedData.splice(index, 1, newData)
      this.$nextTick(() => { this.$refs.dataTable && this.$refs.dataTable.setCurrentRow(newData) })
      const payload = { type: 'subscribe', keyPath: ['job', 'sources', loc, 'properties', 'subConfigVersion'], value: version }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleSubscribeScriptChange(value) {
      this.subscribeScript = value
      const payload = { type: 'subscribe', keyPath: ['job', 'transform', 'script'], value } 
      this.UPDATE_CONFIG_ITEM(payload)
      if (this.subScriptValid === false && this.scriptErrorMsg) {
        let valid = true
        this.selectedData.forEach(data => {
          this.subscribeScript.indexOf(this.formatterSinkTableName(data)) === -1 && (valid = false)
        })
        this.subScriptValid = valid
        valid && (this.scriptErrorMsg = '')
      } else {
        this.subScriptValid = true
      }
    },
    handleBlur() {
      // const sqlText = sqlFormatter.format(this.subscribeScript)
      // if (this.subscribeScript !== sqlText) {
      //   this.handleSubscribeScriptChange(sqlText)
      // }
    },
    handleFormatter() {
      const sqlText = sqlFormatter.format(this.subscribeScript)
      if (this.subscribeScript !== sqlText) {
        this.handleSubscribeScriptChange(sqlText)
      }
    },
    handleCmReady(cm) {
      cm.on('keypress', () => {
        if (this.moderateEditable) cm.showHint()
      })
    },
    fetchData() {
      const sources = this.configData.job.sources
      if (sources.length > 0) {
        const postData = sources.map(item => ({
          configId: item.properties.subConfigId,
          version: item.properties.subConfigVersion
        }))
        queryBatch(postData).then(res => {
          this.selectedData = res
          // this.UPDATE_DATA_TO_BE_SUB(this.selectedData)
          res.length > 0 && this.$nextTick(() => { this.$refs.dataTable && this.$refs.dataTable.setCurrentRow(res[0]) })
        }).catch(res => {
          this.selectedData = []
          this.$message.error('获取待订阅的数据失败！原因：' + res.message || '暂无')
        })
      } else {
        this.selectedData = []
      }
    },
    formValidate() {
      let validRes = true
      if (this.selectedData.length === 0) {
        validRes = false
      }
      if (!this.subscribeScript) {
        validRes = false
        this.subScriptValid = false
      } else {
        for (const data of this.selectedData) {
          if (this.subscribeScript.indexOf(this.formatterSinkTableName(data)) === -1) {
            validRes = false
            this.subScriptValid = false
            this.scriptErrorMsg = '缺少需订阅的数据发布列表中的数据表名'
            break
          }
        }
      }
      return validRes
    },
    handleGoPub(row) {
      this.$router.push({ path: '/publish/edit', query: { configId: row.configId } })
    },
    publishUrl(row) {
      const { href } = this.$router.resolve({ path: '/publish/edit', query: { configId: row.configId } })
      return href
    }
  }
}
</script>
<style lang="less">
  @import "~assets/css/bids-subscribe-rule.less";
  @import '~codemirror/theme/ambiance.css';
  @import '~codemirror/lib/codemirror.css';
  @import '~codemirror/addon/hint/show-hint.css';
</style>

<!--
 * @Description: 编辑数据订阅页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-14 18:21:53
-->
<template>
  <div class="subscribe-edit">
    <div class="page-header">
     <el-page-header @back="goBack" content="编辑数据订阅"></el-page-header>
    </div>
    <div>
     <el-tabs class="version-tabs" v-model="currentVersion" type="card" @tab-click="handleVersionChange">
        <el-tab-pane v-for="item in data" :key="item.version" :name="item.version">
          <span slot="label" class="verson-tab-label">
            <span class="label-text">{{item.version}}</span>
            <i style="color:#0ad0b6;" class="el-icon-success" v-if="item.jobStatus === 1"></i>
            <i style="color:#ACACAC;" class="el-icon-remove" v-else></i>
          </span>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="version-info-panel">
      <div class="tips"><i class="el-icon-info"></i>如要编辑更多配置项，请先将版本下线</div>
      <div class="info-row">
        <span class="text-item">
          <span>当前版本：</span>
          <span>{{currentData.version}}</span>
        </span>
        <span class="text-item">
          <span>当前状态：</span>
          <span><el-tag v-if="currentData.version" effect="dark" size="small" :type="currentData.jobStatus === 1 ? 'success' : 'info'">{{currentData.jobStatus === 1 ? "运行中" : "已下线"}}</el-tag></span>
        </span>
        <span class="text-item">
          <span>初版创建人：</span>
          <span>{{currentData.creatorName}}</span>
        </span>
        <span class="text-item">
          <span>最近更新时间：</span>
          <span>{{currentData.lastModifiedTime}}</span>
        </span>
      </div>
      <div class="operate-item" v-if="currentData.version">
        <el-button icon="el-icon-copy-document" size="mini" :disabled="!strictEditable" @click="handleClone">克隆当前版本/新增版本</el-button>
        <el-divider direction="vertical"></el-divider>
        <el-button icon="el-icon-remove" size="mini" v-if="currentData.jobStatus === 1" :disabled="!strictEditable" @click="handleOnline(false)">下线当前版本</el-button>
        <el-button icon="el-icon-success" size="mini" v-else :disabled="!strictEditable" @click="handleOnline(true)">上线当前版本</el-button>
        <el-divider direction="vertical"></el-divider>
        <el-button size="mini" @click="handleManageJobs">管理实例</el-button>
      </div>
    </div>
    <el-tabs class="config-tabs" v-model="tabValue" type="card" tab-position="right" @tab-click="handleConfigTabClick">
      <el-tab-pane
        :key="item.name"
        v-for="item in currentTabs"
        :label="item.label"
        :name="item.name"
        :lazy="true">
      </el-tab-pane>
      <div class="config-box">
        <bids-base-info ref="baseInfo" :page-status="pageStatus" configType="subscribe" :sub-editable="strictEditable" v-show="tabValue === 'baseInfo'"></bids-base-info>
        <bids-subscribe-rule ref="subscribeRule" :page-status="pageStatus" v-show="tabValue === 'subscribeRule'"></bids-subscribe-rule>
        <bids-subscribe-output ref="subscribeOutput" :page-status="pageStatus" :sub-editable="strictEditable" v-show="tabValue === 'subscribeOutput'"></bids-subscribe-output>
        <bids-before-after-event ref="beforeAfterEvent" :page-status="pageStatus" v-show="tabValue === 'beforeAfterEvent'"></bids-before-after-event>
        <bids-other-setting ref="setting" :page-status="pageStatus" :currentData="currentData" configType="subscribe" v-show="tabValue === 'setting'"></bids-other-setting>
      </div>
    </el-tabs>
    <div class="config-footer" v-if="currentData.version">
      <bids-global-tips-bar></bids-global-tips-bar>
      <div class="buttons">
        <el-button type="primary" size="medium" :disabled="!strictEditable" @click="handleSave">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { BidsBaseInfo, BidsSubscribeRule, BidsSubscribeOutput, BidsBeforeAfterEvent, BidsOtherSetting, BidsGlobalTipsBar } from 'components'
import { queryData, updateData, setOnline, setOffline } from 'services/subscribe.service'
import { queryJobInfo } from 'services/common.service'
import { mapState, mapMutations } from 'vuex'
import { subscribeEditable } from 'mixins'

export default {
  mixins: [subscribeEditable],
  components: {
    BidsBaseInfo,
    BidsSubscribeRule,
    BidsSubscribeOutput,
    BidsBeforeAfterEvent,
    BidsOtherSetting,
    BidsGlobalTipsBar
  },
  computed: {
    ...mapState({
      configData: state => state.currentSubscribeConfig
    }),
    dsType() {
      const { job } = this.configData
      return job?.sink?.properties?.dsType
    },
    isStarrocks() {
      return this.dsType === 'starrocks'
    }
  },
  data() {
    return {
      pageStatus: 'edit',
      data: [],
      tabValue: 'baseInfo',
      currentTabs: [
        { name: 'baseInfo', label: '基本信息' },
        { name: 'subscribeRule', label: '订阅规则' },
        { name: 'subscribeOutput', label: '数据输出' },
        { name: 'beforeAfterEvent', label: '前置后置' },
        { name: 'setting', label: '订阅配置' }
      ],
      currentVersion: '',
      currentData: {}
    }
  },
  created() {
    this.INIT_SUBSCRIBE_CONFIG()
    this.fetchData()
  },
  methods: {
    ...mapMutations(['INIT_SUBSCRIBE_CONFIG', 'SET_SUBSCRIBE_CONFIG', 'UPDATE_CONFIG_ITEM', 'UPDATE_DATA_TO_BE_SUB']),
    goBack() {
      this.$router.back()
    },
    fetchData() {
      const params = { configId: this.$route.query.configId }
      queryData(params).then(res => {
        if (res.length > 0) {
          res.some(item => item.auth_type === 'view') && this.$router.push({ path: '/subscribe/view', query: { configId: this.$route.query.configId } })
          this.data = res
          this.currentData = res.find(item => item.version === this.currentVersion) || res.find(item => item.jobStatus === 1) || res[res.length - 1]
          this.currentVersion = this.currentData.version
          this.SET_SUBSCRIBE_CONFIG(this.currentData)
          this.currentTabs.forEach(tab => {
            this.$refs[tab.name].updatePageData()
          })
        }
      }).catch(res => {
        this.$message.error('获取数据订阅失败！原因：' + res.message || '暂无')
      })
    },
    handleConfigTabClick(tab) {
      this.$refs[tab.name].updatePageData()
    },
    handleVersionChange(tab, event) {
      this.currentData = this.data.find(item => item.version === tab.name)
      this.currentVersion = this.currentData.version
      this.SET_SUBSCRIBE_CONFIG(this.currentData)
      this.UPDATE_DATA_TO_BE_SUB([])
      this.currentTabs.forEach(tab => {
        this.$refs[tab.name].updatePageData()
      })
    },
    handleOnline(val) {
      const opt = val ? '上线' : '下线'
      this.$confirm('确定' + opt + '数据的' + this.currentVersion + '版本？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const setOption = val ? setOnline : setOffline
        const data = [{ configId: this.$route.query.configId, version: this.currentVersion, lastModifiedTime: this.currentData.lastModifiedTime }]
        this.SET_SUBSCRIBE_CONFIG(this.currentData) // 修改完界面数据不提交，就进行上下线处理时，还原数据
        setOption(data).then(res => {
          const item = res[0]
          if (item.success) {
            this.fetchData()
            this.$message.success(this.currentVersion + '版本' + opt + '成功！')
            val && this.showPrompt()
          } else {
            this.$message.warning(this.currentVersion + '版本' + opt + '失败！原因：' + item.message)
          }
        }).catch(res => {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
      }).catch(() => {})
    },
    handleClone() {
      this.$router.push({ name: 'SubscribeBuild', query: { mode: 'cloning', configId: this.configData.configId, version: this.configData.version } })
    },
    handleSave() {
      let valid = true
      this.currentTabs.forEach(tab => {
        valid = valid && this.$refs[tab.name].formValidate()
      })
      if (!valid) {
        const message = this.isStarrocks ? '请按要求填写完整表单项或设置输出数据的结构中的主键！' : '请按要求填写完整表单项！'
        this.$message.error(message)
        return
      }
      const params = { configId: this.configData.configId, version: this.configData.version }
      const { cache, ...postData } = this.configData
      updateData(params, postData).then(res => {
        this.$message.success('提交成功！')
        if (this.configData.jobStatus === 1) {
          // this.$router.push({ name: 'SubscribeList' }) // 生效状态时直接跳转回列表页
          setTimeout(() => this.$router.back(-1), 100) 
        } else {
          this.fetchData() // 停留在当前页面时重新获取数据
        }
      }).catch(res => {
        this.$message.error('提交失败！原因：' + res.message || '暂无')
      })
    },
    showPrompt() {
      const { configId, version, collectType } = this.currentData
      if (collectType === 1) return
      // 增量的collectType = 2, 增量时查询接口
      const params = { jobName: `${configId}_${version}` }
      queryJobInfo(params).then(res => {
        if (res.state === 'RUNNING') {
          this.$alert('修改后的配置在实例重新运行后才会生效，请重启任务实例', '提示', { type: 'warning' })
        }
      }).catch((err) => {
        this.$message.error('提交失败！原因：' + err.message || '暂无')
      })
    },
    handleManageJobs() {
      this.$router.push({ path: '/subscribe/jobs', query: { configId: this.$route.query.configId } })
    }
  }
}
</script>

<style lang="less" scoped>
  @import "~assets/css/subscribe-edit.less";
</style>

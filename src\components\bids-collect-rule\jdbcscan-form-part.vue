<!--
 * @Description: 采集类型为JDBC/mongo短轮询时的form表单组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-06-10 14:43:22
-->
<template>
  <el-form ref="form" :model="form" :rules="rules"  size="small" :label-width="labelWidth" :disabled="!moderateEditable">
    <el-form-item label="增量标记字段" prop="increment_column" required>
      <template v-if="mildEditable">
        <el-input v-model="form.increment_column" class="custom-input" @change="(val) => { updateSourcesProperties(val, 'increment_column') }"/>
        <el-checkbox style="margin-left: 10px;" v-model="form.increment_column_type" 
          :true-label="1" :false-label="0" @change="(val) => { updateSourcesProperties(val, 'increment_column_type') }">
          日期类型
        </el-checkbox>
      </template>
      <span v-else>{{`${form.increment_column}${form.increment_column_type == 1 ? '（日期类型）': ''}`}}</span>
    </el-form-item>
    <el-form-item label="采集起始位置" prop="scan_startup_mode" required>
      <el-select v-model="form.scan_startup_mode" @change="(val) => { updateSourcesProperties(val, 'scan_startup_mode') }" v-if="mildEditable">
        <el-option v-for="item in offsetOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <span v-else>{{form.scan_startup_mode ? offsetOptions.find(item => item.value === form.scan_startup_mode).label : ''}}</span>
    </el-form-item>
    <el-form-item label="指定起始值" prop="specificOffsets" required v-if="form.scan_startup_mode === 'specific-offsets'">
      <template v-if="mildEditable">
        <el-input-number size v-model="form.specificOffsets" class="custom-input" v-if="form.increment_column_type === 0"
          @change="(val) => { updateSourcesProperties(val, 'scan_startup_specific-offset') }"/>
        <el-date-picker
          v-else
          v-model="form.specificOffsets"
          value-format="timestamp"
          type="datetime"
          align="center"
          placeholder="选择日期时间"
          @change="(val) => { updateSourcesProperties(val, 'scan_startup_specific-offset') }">
        </el-date-picker>
      </template>
      <span v-else>{{form.specificOffsets}}</span>
    </el-form-item>
    <el-form-item label="短轮询间隔时间" prop="polling_interval_seconds">
      <template v-if="mildEditable">
        <el-input-number style="width:100px;" v-model="form.polling_interval_seconds" size="mini" :min="dt_system.minPollingIntervalSeconds" 
          @change="(val) => { updateSourcesProperties(val, 'polling_interval_seconds') }"/>
        <span style="margin-left:5px;">秒</span>
      </template>
      <span v-else>{{`${form.polling_interval_seconds} 秒`}}</span>
    </el-form-item>
    <el-form-item label="每次轮询往前偏移量" prop="polling_offset" v-if="form.connector === 'jdbc_scan'">
      <template v-if="mildEditable">
        <el-input-number style="width:100px;" v-model="form.polling_offset" size="mini" :min="dt_system.minPollingOffset" 
          @change="(val) => { updateSourcesProperties(val, 'polling_offset') }"/>
        <span style="margin-left:5px;" v-show="form.increment_column_type === 1">秒</span>
      </template>
      <span v-else>{{`${form.polling_offset} ${form.increment_column_type === 1 ? '秒' : ''}`}}</span>
    </el-form-item>
  </el-form>
</template>
<script>
import { editable } from 'mixins' 
import { mapMutations, mapState } from 'vuex'

export default {
  name: 'jdbcscan-form-part',
  mixins: [editable],
  props: {
    labelWidth: String,
    pageStatus: String,
    configType: String
  },
  computed: {
    ...mapState({
      configData: state => state.currentPublishConfig,
      dt_system: 'dt_system'
    })
  },
  data() {
    return {
      form: {},
      offsetOptions: [{ label: 'latest-offset', value: 'latest-offset' }, { label: 'earliest-offset', value: 'earliest-offset' }, { label: 'specific-offsets', value: 'specific-offsets' }],
      rules: {
        increment_column: [{ required: true, message: '请输入增量标记字段' }],
        polling_interval_seconds: [{ required: true, message: '请设置短轮询间隔时间', trigger: 'change' }],
        scan_startup_mode: [{ required: true, message: '请选择采集起始位置', trigger: 'change' }],
        specificOffsets: [{ required: true, message: '请指定起始值' }],
        polling_offset: [{ required: true, message: '请设置每次轮询往前偏移量', trigger: 'change' }]
      }
    }
  },
  created() {
    this.updatePageData()
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    updatePageData() {
      let { increment_column, increment_column_type, polling_interval_seconds, scan_startup_mode, 'scan_startup_specific-offset': specificOffsets, polling_offset } = this.configData.job.sources[0].properties
      if (!this.offsetOptions.some(item => item.value === scan_startup_mode)) {
        scan_startup_mode = undefined
      }
      this.form = { increment_column, increment_column_type, polling_interval_seconds, scan_startup_mode, specificOffsets, polling_offset }
    },
    updateSourcesProperties(value, name) {
      const payload = { type: 'publish', keyPath: ['job', 'sources', 0, 'properties', name], value }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    }
  }
}
</script>
<style lang="less">
 @import "~assets/css/bids-collect-rule.less";
</style>

<!--
 * @Description: 数据发布-管理实例页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-04-01 15:05:04
-->
<template>
  <div class="publish-jobs">
    <div class="page-header">
     <el-page-header @back="goBack" content="管理数据发布的实例"></el-page-header>
    </div>
    <div>
      <el-tabs class="version-tabs" v-model="currentVersion" type="card" @tab-click="handleVersionChange">
        <el-tab-pane v-for="item in data" :key="item.version" :name="item.version">
          <span slot="label" class="verson-tab-label">
            <span class="label-text">{{item.version}}</span>
            <i style="color:#0ad0b6;" class="el-icon-success" v-if="item.jobStatus === 1"></i>
            <i style="color:#ACACAC;" class="el-icon-remove" v-else></i>
          </span>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="version-info-panel">
      <div class="info-row">
        <span class="text-item">
          <span>当前版本：</span>
          <span>{{currentData.version}}</span>
        </span>
        <span class="text-item">
          <span>当前状态：</span>
          <span><el-tag v-if="currentData.version" effect="dark" size="small" :type="currentData.jobStatus === 1 ? 'success' : 'info'">{{currentData.jobStatus === 1 ? "已上线" : "已下线"}}</el-tag></span>
        </span>
        <span class="text-item">
          <span>负责/维护人：</span>
          <span>{{formatterOwners(currentData)}}</span>
        </span>
      </div>
      <div class="info-row">
        <span class="text-item">
          <span>所属应用：</span>
          <span>{{currentData.appName}}</span>
        </span>
        <span class="text-item">
          <span>发布名称：</span>
          <span>{{currentData.interfaceName}}</span>
        </span>
        <span class="text-item">
          <span>发布编码：</span>
          <span>{{currentData.interfaceCode}}</span>
        </span>
        <span class="text-item">
          <span>发布描述：</span>
          <span>{{currentData.interfaceDesc}}</span>
        </span>
      </div>
      <div class="info-row">
        <span class="text-item">
          <span>采集类型：</span>
          <span>{{formatterCollectType(currentData)}}</span>
        </span>
        <span class="text-item" v-if="currentData.collectType === 1">
          <span>开始时间：</span>
          <span>{{formatterScheduleBeginTime(currentData)}}</span>
        </span>
        <span class="text-item">
          <span>发布周期：</span>
          <span>{{formatterScheduleAtCron(currentData)}}</span>
        </span>
      </div>
      <div class="operate-item" v-if="currentData.version">
        <el-button icon="el-icon-refresh" size="mini" @click="handleRefresh" :disabled="refreshDisabled">刷新列表</el-button>
        <template v-if="showStartJobNowBtn">
          <el-divider direction="vertical"></el-divider>
          <el-button icon="el-icon-video-play" size="mini" @click="handleStartJobNow">立即执行一次</el-button>
        </template>
      </div>
    </div>
    <div class="table">
      <bids-job-table ref="jobTable" :init-data="jobTableInitData"></bids-job-table>
    </div>
  </div>
</template>
<script>
import BidsJobTable from 'components/bids-job-table'
import { queryData } from 'services/publish.service'
import { startJobNow } from 'services/common.service'
import { formatter } from 'mixins'

export default {
  mixins: [formatter],
  components: {
    BidsJobTable
  },
  data() {
    return {
      currentVersion: '',
      currentData: { configId: this.$route.query.configId, job: { sink: {} } },
      data: []
    }
  },
  created() {
    this.fetchData()
  },
  computed: {
    jobTableInitData() {
      return { configId: this.currentData.configId, version: this.currentData.version, collectType: this.currentData.collectType, type: 'PUB', storageStrategy: this.currentData.job.sink.storageStrategy }
    },
    showStartJobNowBtn() {
      return this.currentData.collectType === 1 && this.currentData.job.sink.storageStrategy === 'SAVE'
    },
    refreshDisabled () {
      return this.currentData.job.sink.storageStrategy === 'NOT_SAVE'
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleVersionChange(tab) {
      this.currentData = this.data.find(item => item.version === tab.name)
    },
    fetchData() {
      const params = { configId: this.$route.query.configId }
      queryData(params).then(res => {
        this.data = res
        if (res.length > 0) {
          this.currentData = res.find(item => item.version === this.currentVersion) || res.find(item => item.jobStatus === 1) || res[res.length - 1]
          this.currentVersion = this.currentData.version
        }
      }).catch(res => {
        this.data = []
        this.$message.error('获取数据发布详情失败！原因：' + res.message || '暂无')
        this.currentVersion = ''
      })
    },
    handleRefresh() {
      this.$refs['jobTable'].fetchData()
    },
    handleStartJobNow() {
      this.$confirm('确认执行一次数据发布任务？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const postData = { configId: this.currentData.configId, version: this.currentVersion }
        startJobNow(postData).then(res => {
          this.$message.success('执行成功！')
          setTimeout(this.handleRefresh(), 1000)
        }).catch(res => {
          this.$message.error('执行失败！原因：' + res.message || '暂无')
        })
      }).catch(() => {})
    }
  }
}
</script>
<style lang="less">
  @import "~assets/css/publish-jobs.less";
</style>

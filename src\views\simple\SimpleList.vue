<template>
  <div class="simple-list-page">
    <div class="simple-title">
      <span>简单模式 (数据集成)</span>
      <el-button class="create-button" type="primary" size="medium" @click="handleCreate" v-if="activeTab === 'config'">新建集成任务</el-button>
    </div>
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="任务配置" name="config">
        <config-list ref="config" :appOptions="appOptions" @switch="handleSwitch"></config-list>
      </el-tab-pane>
      <el-tab-pane label="任务实例" name="instance">
        <instance-list ref="instance" :appOptions="appOptions"></instance-list>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ConfigList from './config-list.vue'
import InstanceList from './instance-list.vue'
import { queryAppsInConfig } from 'services/common.service'

export default {
  name: 'sync-list-page',
  components: {
    ConfigList,
    InstanceList
  },
  data() {
    return {
      activeTab: 'config',
      appOptions: []
    }
  },
  computed: {
    
  },
  activated() {
    this.getApps()
    this.$refs[this.activeTab].fetchData() // 回到页面时重新加载数据
  },
  methods: {
    handleSwitch(tab) {
      this.activeTab = tab
      this.$refs[tab].fetchData()
    },
    handleClick(tab) {
      this.$refs[tab.name].fetchData() // 切换tag时重新加载数据
    },
    handleCreate() {
      this.$router.push({ name: 'Config' })
    },
    getApps() {
      const params = { configType: 'SUB', kind: 'MINE' }
      queryAppsInConfig(params).then(res => {
        res && (res = res.map(v => { return { ...v, label: `${v.appKey}（${v.appName}）` } }))
        this.appOptions = res
      })
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/simple-list.less";
</style>

<!--
 * @Description: 数据发布-采集规则组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-07-29 18:31:08
-->
<template>
  <div class="bids-collect-rule">
    <el-form :model="form" ref="form" style="min-height: 300px;" size="small" :label-width="labelWidth" :rules="rules">
      <el-form-item label="数据来源" prop="dsId">
        <el-select size="small" v-model="form.dsId" filterable @change="handleDsIdChange" :loading="dsLoading" :disabled="!moderateEditable" v-if="mildEditable">
          <el-option v-for="item in dataSourceOptions" :key="item.dsId" :label="getDsLabel(item)" :value="item.dsId"></el-option>
        </el-select>
        <span v-else>{{'('+ currentDataSource.dsType +')'+currentDataSource.dsName}}</span>
        <span v-if="moderateEditable">
          <el-button size="small" @click="handleAddDataSource" style="margin-left:20px;">新增数据源</el-button>
          <el-button size="small" @click="handleTestConnect" style="margin-left:10px;">测试连接</el-button>
        </span>
      </el-form-item>
      <el-form-item label="数据采集类型" prop="collectType">
        <el-radio-group v-model="form.collectType" @change="handleCollectTypeChange" :disabled="!moderateEditable || disabledCollectType || !form.dsId" v-if="mildEditable">
          <el-radio :label="1">全量采集</el-radio>
          <el-radio :label="2">增量采集</el-radio>
        </el-radio-group>
        <span v-else>{{form.collectType === 1 ? '全量采集' : '增量采集'}}</span>
      </el-form-item>
      <el-form-item label="增量采集方式" prop="connector" v-if="form.collectType === 2 && dsConnector !== 'http_write'">
        <el-select size="small" v-model="form.connector" placeholder="请选择" @change="handleConnectorChange" :disabled="!moderateEditable" v-if="mildEditable">
          <el-option v-for="(item, index) in connectorOptions" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <span v-else>{{form.connector ? connectorOptions.find(item => item.id === form.connector).name : ''}}</span>
      </el-form-item>
      <!-- 采集类型为JDBC/mongo短轮询时的form表单组件 -->
      <jdbcscan-form-part v-if="form.collectType === 2 && ['jdbc_scan', 'mongo_scan'].includes(form.connector)" :label-width="labelWidth" ref="jdbcscanFormPart" :page-status="pageStatus" :configType="configType"></jdbcscan-form-part>
      <kafka-form-part ref="kafkaFormPart" v-if="form.collectType === 2 && form.connector === 'kafka'" :label-width="labelWidth" :page-status="pageStatus"></kafka-form-part>
      <!-- <transition mode="out-in">
        <jdbcscan-form-part v-if="form.collectType === 2 && form.connector === 'jdbc_scan'" :label-width="labelWidth" ref="jdbcscanFormPart" key="jdbcscan" :page-status="pageStatus"></jdbcscan-form-part>
        <kafka-form-part ref="kafkaFormPart" v-else-if="form.collectType === 2 && form.connector === 'kafka'" :label-width="labelWidth" key="kafka" :page-status="pageStatus"></kafka-form-part>
      </transition> -->
      <!-- jdbc -->
      <el-form-item label="缓冲行数" prop="scan_fetch-size" v-if="form.collectType === 1 && dsConnector === 'jdbc'">
        <el-input-number v-model="form['scan_fetch-size']" size="mini" :precision="0" :min="0" @change="(val) => {updateSourcesProperties(val, 'scan_fetch-size')}" :disabled="!mildEditable"  v-if="mildEditable"/>
        <span v-else>{{form['scan_fetch-size']}}</span>
      </el-form-item>
      <el-form-item label="数据采集SQL语句" prop="query" v-if="dsConnector === 'jdbc'">
        <div class="codemirror-wrap">
          <el-tooltip content="格式化">
            <i class="el-icon-brush" @click="handleFormatter"></i>
          </el-tooltip>
          <codemirror ref="cmEditor" :value="form.query" :options="{readOnly: !moderateEditable, hintOptions: hintOptions}" @beforeChange="handleBeforeChange" @input="handleQueryChange" @ready="handleCmReady"/>
        </div>
      </el-form-item>
      <!-- hdfs -->
      <template v-if="dsConnector === 'filesystem'">
        <el-form-item label="数据格式" prop="format">
          <el-select v-model="form.format" @change="handleFormatChange" :disabled="!moderateEditable" v-if="mildEditable">
            <el-option v-for="item in fsFormatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <span v-else>{{this.fsFormatOptions.find(item => item.value === form.format).label}}</span>
        </el-form-item>
        <el-form-item label="原始值charset" prop="raw_charset" v-if="form.format === 'raw'">
          <el-input class="custom-input" v-model="form.raw_charset" @change="(val) => { updateSourcesProperties(val, 'raw_charset') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
          <span v-else>{{form.raw_charset}}</span>
        </el-form-item>
        <el-form-item label="数据来源路径" prop="path">
          <el-input v-model="form.path" style="width: 420px;" class="custom-input" @change="(val) => { updateSourcesProperties(val, 'path') }" :disabled="!moderateEditable" v-if="mildEditable" placeholder="/dirA/dirB/dirC"></el-input>
          <span v-else>{{form.path}}</span>
        </el-form-item>
      </template>
      <!-- mongo -->
      <template v-if="dsConnector === 'mongo'">
        <el-form-item label="输入数据集合" prop="collection">
          <el-input v-model="form.collection" class="custom-input" @change="(val) => { updateSourcesProperties(val, 'collection') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
          <span v-else>{{form.collection}}</span>
        </el-form-item>
        <el-form-item label="单列读取">
          <el-checkbox v-model="form.format" true-label="raw" false-label="json" @change="handleFormatChange" :disabled="!moderateEditable">是否读取原始值作为单列</el-checkbox>
        </el-form-item>
        <el-form-item label="原始值charset" prop="raw_charset" v-if="form.format === 'raw'">
          <el-input class="custom-input" v-model="form.raw_charset" @input="change($event)" @change="(val) => { updateSourcesProperties(val, 'raw_charset') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
          <span v-else>{{form.raw_charset}}</span>
        </el-form-item>
        <el-form-item label="filter" prop="filter">
          <el-input type="textarea" style="width: 300px" :rows="4" :placeholder="filterPlaceholder" v-model="form.filter" @change="(val) => { updateSourcesProperties(val, 'filter') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
          <span v-else>{{form.filter}}</span>
        </el-form-item>
      </template>
      <!-- http_write -->
      <template v-if="dsConnector === 'http_write'">
        <el-form-item label="数据输入接口">
          <div class="urlPanel">
            <span :data-clipboard-text="dataUploadUrl" id="dataUploadUrl">{{dataUploadUrl}}</span>
            <div class="event-icon copy-icon dataUploadUrl" data-clipboard-target="#dataUploadUrl" @click="handleCopyUrl('.dataUploadUrl')"><i class="el-icon-document-copy"></i></div>
          </div>
        </el-form-item>
        <el-form-item label="请求方式">
          <span v-if="configData.job.sources[0].properties.dsType === 'http_write_file'">POST（multipart/form）</span>
          <span v-else>POST（application/json）</span>
        </el-form-item>
        <el-form-item label="请求体示例">
          <span v-if="configData.job.sources[0].properties.dsType === 'http_write_file'">{ "file": "..." } 目前仅支持csv文件</span>
          <span v-else>{ "data": [ { "列名": "..."} ] }</span>
        </el-form-item>
      </template>
      <!-- es67 -->
      <template v-if="['elasticsearch-6', 'elasticsearch-7'].includes(dsConnector)">
        <el-form-item label="文档索引名称" prop="index">
          <el-input v-model="form.index" class="custom-input" @change="(val) => { updateSourcesProperties(val, 'index') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
          <span v-else>{{form.index}}</span>
        </el-form-item>
        <el-form-item label="文档类型" prop="documentType" v-if="dsConnector === 'elasticsearch-6'">
          <el-input v-model="form.documentType" class="custom-input" @change="(val) => { updateSourcesProperties(val, 'document-type') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
          <span v-else>{{form.documentType}}</span>
        </el-form-item>
        <el-form-item label="单列输出">
          <el-checkbox v-model="form.format" true-label="raw" false-label="json" @change="handleFormatChange" :disabled="!moderateEditable">是否将输入单列的值作为原始值输出</el-checkbox>
        </el-form-item>
        <el-form-item label="原始值charset" prop="raw_charset" v-if="form.format === 'raw'">
          <el-input class="custom-input" v-model="form.raw_charset" @input="change($event)" @change="(val) => { updateSourcesProperties(val, 'raw_charset') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
          <span v-else>{{form.raw_charset}}</span>
        </el-form-item>
        <el-form-item label="query">
          <el-input type="textarea" style="width: 300px" :rows="4" placeholder="请输入示例{'match_all': { }}" v-model="form.query" @change="(val) => { updateSourcesProperties(val, 'query') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
          <span v-else>{{form.query}}</span>
        </el-form-item>
      </template>
      <!-- cos -->
      <template v-if="dsConnector === 'COS'">
        <el-form-item label="数据格式" prop="format">
          <el-select v-model="form.format" @change="handleFormatChange" :disabled="!moderateEditable" v-if="mildEditable">
            <el-option v-for="item in fsFormatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <span v-else>{{this.fsFormatOptions.find(item => item.value === form.format).label}}</span>
        </el-form-item>
        <el-form-item label="原始值charset" prop="raw_charset" v-if="form.format === 'raw'">
          <el-input class="custom-input" v-model="form.raw_charset" @change="(val) => { updateSourcesProperties(val, 'raw_charset') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
          <span v-else>{{form.raw_charset}}</span>
        </el-form-item>
        <el-form-item label="文件目录" prop="prefix">
          <el-input v-model="form.prefix" style="width: 420px;" class="custom-input" @change="(val) => { updateSourcesProperties(val, 'prefix') }" :disabled="!moderateEditable" v-if="mildEditable" placeholder="/directory"></el-input>
          <span v-else>{{form.prefix}}</span>
        </el-form-item>
      </template>
      <template v-if="dsConnector === 'HTTP'">
        <el-form-item label="请求header">
          <div class="header-wrapper">
            <div class="wrapper" v-for="(item, index) in form.requestHeader" :key="index">
              <div class="input-wrapper">
                <el-input v-model="item.key" clearable placeholder="key" :disabled="!moderateEditable"  @change="(val) => { updateSourcesByName('requestHeader') }"></el-input>
                <el-input v-model="item.value" clearable placeholder="vlaue" :disabled="!moderateEditable"  @change="(val) => { updateSourcesByName('requestHeader') }"></el-input>
              </div>
              <div class="btn-wrapper" v-if="moderateEditable">
                <el-button v-if="index === 0" type="text" icon="el-icon-circle-plus-outline" @click="addRow('requestHeader')"></el-button>
                <el-button v-else class="remove-btn" type="text" icon="el-icon-remove-outline" @click="removeRow('requestHeader', index)"></el-button>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="请求参数" v-if="currentDataSource.dsProperties.method === 'GET'">
          <div class="params-wrapper">
            <div class="wrapper" v-for="(item, index) in form.requestParams" :key="index">
              <div class="input-wrapper">
                <el-input v-model="item.key" clearable placeholder="key" :disabled="!moderateEditable" @change="(val) => { updateSourcesByName('requestParams') }"></el-input>
                <el-input v-model="item.value" clearable placeholder="vlaue" :disabled="!moderateEditable" @change="(val) => { updateSourcesByName('requestParams') }"></el-input>
              </div>
              <div class="btn-wrapper" v-if="moderateEditable">
                <el-button v-if="index === 0" type="text" icon="el-icon-circle-plus-outline" @click="addRow('requestParams')"></el-button>
                <el-button v-else class="remove-btn" type="text" icon="el-icon-remove-outline" @click="removeRow('requestParams', index)"></el-button>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="请求body">
          <el-input v-if="mildEditable" :disabled="!moderateEditable" v-model="form.body" rows="3" type="textarea" resize="none" placeholder="{}" class="custom-input-body" @change="(val) => { updateSourcesProperties(val, 'body') }"></el-input>
          <span v-else>{{form.body}}</span>
        </el-form-item>
      </template>
    </el-form>
    <bids-datastruct-form-part ref="datastruct" :page-status="pageStatus" type="source" :configType="configType" :init-data="currentDataSource" :label-width="labelWidth"></bids-datastruct-form-part>
    <bids-datasource-dialog ref="addDatasouceDialog" type="add" :options="addDsOptions" @submit="handleSubmitDs"/>
    <div class="description-panel" v-if="mildEditable">
      <div class="question">
        <i class="el-icon-question"></i>
        <span>数据来源的选择</span>
      </div>
      <div class="answer">
        <span>选择所属应用下的数据源，用于数据采集，首次使用时需新增数据源</span>
      </div>
      <template v-if="['kafka', 'mongo', 'filesystem'].includes(dsConnector)">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>单列读取</span>
        </div>
        <div class="answer">
          <span>当选择读取原始值作为单列时，输入数据的结构只允许定义1个列<span v-if="dsConnector === 'mongo'">，类型是STRING</span></span>
        </div>
      </template>
      <template v-if="form.connector === 'jdbc_scan'">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>JDBC短轮询的增量标记字段</span>
        </div>
        <div class="answer">
          <span>需要是输出数据结构里的字段，默认数值类型，仅支持日期类型和数值类型字段</span>
        </div>
      </template>
      <template v-if="dsConnector === 'jdbc' && this.form.collectType === 1">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>缓冲行数 / fetch-size</span>
        </div>
        <div class="answer">
          <!-- <span>决定了每批次传输数据的缓冲区大小，可提升采集性能，设置为0表示禁用</span> -->
          <div>决定了每批次从数据库中读取多少条数据（jdbc默认一次性将所有结果都读取到内存中），避免在数据量很大时可能会造成OOM</div>
          <div>fetch-size 真正生效需要：数据源地址带上连接参数 <b>useCursorFetch=true</b>；数据库版本高于<b>5.0.2</b></div>
          <div>注意：fetch-size的值不可设置过大，否则会读取超时，导致任务失败；设置为0表示禁用</div>
        </div>
      </template>
      <template v-if="dsConnector === 'jdbc'">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>数据采集SQL语句</span>
        </div>
        <div class="answer">
          <span>平台通过该SELECT语句从数据源采集数据</span>
          <span v-if="form.connector === 'jdbc_scan'">，只支持单表查询，不支持join和复杂sql</span>
        </div>
      </template>
      <template v-if="form.connector === 'kafka'">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>各分区启动偏移量</span>
        </div>
        <div class="answer">
          <span>例子：partition:0,offset:42;partition:1,offset:300</span>
        </div>
      </template>
      <template v-if="dsConnector === 'filesystem'">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>数据来源路径</span>
        </div>
        <div class="answer">
          <span>指明数据所在HDFS文件夹的相对路径，3层目录以上</span>
        </div>
      </template>
      <template  v-if="['http_write'].includes(dsConnector)">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>调用接口时的身份认证</span>
        </div>
        <div class="answer">
          <span>参考<el-link type="primary" href="http://tapd.oa.com/HR_Platform/markdown_wikis/show/#1220394402000327171" target="_blank">身份认证规范</el-link></span>
        </div>
      </template>
      <template>
        <div class="question">
          <i class="el-icon-question"></i>
          <span>输入数据的结构</span>
        </div>
        <div class="answer">
          <span v-if="dsConnector === 'jdbc'">数据结构里的列名需要和SQL语句里的列名保持一致，顺序也要相同，平台提供解析SQL的方式快速定义输入数据的结构</span>
          <span v-else>数据结构需和数据源中的真实数据结构保持一致，平台提供一定的方式辅助定义数据的结构</span>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import { DataType } from 'sdc-core'
import BidsDatasourceDialog from 'components/bids-datasource-dialog'
import KafkaFormPart from './kafka-form-part'
import JdbcscanFormPart from './jdbcscan-form-part'
import { mapMutations, mapState } from 'vuex'
import { editable, sqlExpressions } from 'mixins'
import BidsDatastructFormPart from 'components/bids-datastruct-form-part'
import { testDataSourceById } from 'services/common.service'
import ClipboardJS from 'clipboard'
import sqlFormatter from 'utils/sql-formatter/sqlFormatter'
import { isDef } from 'utils/shared'
import { conf } from 'utils'

export default {
  name: 'bids-collect-rule',
  mixins: [editable, sqlExpressions], // 表单项是否可编辑
  components: {
    BidsDatasourceDialog,
    KafkaFormPart,
    JdbcscanFormPart,
    BidsDatastructFormPart
  },
  props: {
    pageStatus: String // 该组件所在的页面（编辑edit｜构建build｜查看read）
  },
  computed: {
    ...mapState({
      configData: state => state.currentPublishConfig, // 当前配置
      dt_system: 'dt_system',
      dt_dsConnector: 'dt_dsConnector',
      defaultConfig: state => state.defaultPublishConfig
    }),
    dsConnector() {
      return this.dt_dsConnector[this.configData.job.sources[0].properties.dsType]
    },
    currentDataSource() {
      return this.dataSourceOptions.find(item => item.dsId === this.form.dsId) || {}
    },
    disabledCollectType() {
      const dsType = (this.dataSourceOptions.find(item => item.dsId === this.form.dsId) || {}).dsType
      const connector = this.dt_dsConnector[dsType]
      return ['filesystem', 'elasticsearch-6', 'elasticsearch-7', 'kafka', 'http_write', 'COS', 'HTTP'].includes(connector)
    },
    dataSourceOptions() {
      return (this.configData.cache.dataSourceOptions || this.dsOptionsBySource).filter(item => !['FrontRepo'].includes(item.dsType))
    },
    connectorOptions() {
      const options = {
        kafka: [{ name: 'Kafka增量采集', id: 'kafka' }],
        jdbc: [{ name: 'JDBC短轮询采集', id: 'jdbc_scan' }],
        mongo: [{ name: 'mongo短轮询采集', id: 'mongo_scan' }]
      }
      return options[this.dsConnector]
    },
    dataUploadUrl() {
      const dsType = this.configData.job.sources[0].properties.dsType
      const configId = this.pageStatus.indexOf('build') !== -1 ? '{configId}' : this.configData.configId
      const version = this.pageStatus.indexOf('build') !== -1 ? '{version}' : this.configData.version
      return conf('esb').main + `/bidsplus-http-services/${configId}/${version}/write/${dsType === 'http_write_json' ? 'json' : 'file'}`
    }
  },
  data() {
    // var validatefilter = (rule, value, callback) => {
    //   if (typeof value === 'string') {
    //     try {
    //       var obj = JSON.parse(value)
    //       if (typeof obj === 'object' && obj) {
    //         callback()
    //       } else {
    //         callback(new Error('请输入符合要求的json'))
    //       }
    //     } catch (e) {
    //       callback(new Error('请输入符合要求的json'))
    //     }
    //   }
    // }
    return {
      configType: 'publish', // 该组件用于数据发布
      form: {},
      scriptErrorMsg: '',
      scriptValid: true,
      dsLoading: false,
      btnConnLoading: false,
      dsOptionsBySource: [],
      storageOptions: [{ label: '暂存平台', value: 'SAVE' }, { label: '不暂存，直连源库', value: 'NOT_SAVE' }],
      partitionerOptions: [
        { label: 'default', value: 'default' }, { label: 'fixed', value: 'fixed' }, 
        { label: 'round-robin', value: 'round-robin' }
      ],
      rules: {
        dsId: [{ required: true, message: '请选择数据来源', trigger: 'change' }],
        collectType: [{ required: true, message: '请选择数据采集类型', trigger: 'change' }],
        query: [{ required: true, message: '请输入采集的SQL语句' }],
        connector: [{ required: true, message: '请选择增量采集方式', trigger: 'change' }],
        storageStrategy: [{ required: true, message: '请选择落地策略', trigger: 'change' }],
        partition: [{ required: true, message: '请设置分区数', trigger: 'change' }],
        partitioner: [{ required: true, message: '请选择分区器', trigger: 'change' }],
        path: [
          { required: true, message: '请输入数据来源路径' },
          { pattern: /^(\\|\/.+\1{0,1}){3,}$/, message: '路径格式不正确' }
        ],
        format: [{ required: true, message: '请选择数据格式' }],
        collection: [{ required: true, message: '请填写输入数据集合' }],
        raw_charset: [{ required: true, message: '请输入原始charset' }],
        prefix: [
          { required: true, message: '请输入文件目录' },
          { pattern: /^(\\|\/.+\1{0,1}){3,}$/, message: '路径格式不正确' }
        ]
        // filter: [{ required: true, message: '请输入filter' }, { validator: validatefilter, trigger: 'blur' }]
      },
      schedulePopoverVisible: false,
      labelWidth: '150px',
      queryValid: true,
      addDsOptions: [
        { value: 'ESjdbc', label: 'ESjdbc' }, { value: 'ES6', label: 'Elasticsearch 6' }, { value: 'ES7', label: 'Elasticsearch 7' }, 
        { value: 'HDFS', label: 'HDFS' }, { value: 'Hive', label: 'Hive' }, 
        { value: 'http_write_json', label: 'http_write_json' }, { value: 'http_write_file', label: 'http_write_file' }, 
        { value: 'Kafka', label: 'Kafka_0.11及以后版本' },
        { value: 'MongoDB', label: 'MongoDB' }, { value: 'MySQL', label: 'MySQL' }, { value: 'Oracle', label: 'Oracle' }, { value: 'PostgreSQL', label: 'PostgreSQL' },
        { value: 'SQLServer', label: 'SQLServer' }, { value: 'MySqlTenant', label: 'MySqlTenant' }, { value: 'clickhouse', label: 'clickhouse' }, { value: 'COS', label: 'COS' },
        { value: 'ApacheIgnite', label: 'ApacheIgnite' }, { value: 'HTTP', label: 'HTTP' }, { value: 'starrocks', label: 'starrocks' }
      ],
      fsFormatOptions: [
        { label: 'json', value: 'json' }, { label: 'csv', value: 'csv' }, { label: 'parquet', value: 'parquet' }, { label: 'raw', value: 'raw' }
        // { label: 'avro', value: 'avro' }, { label: 'orc', value: 'orc' }
      ],
      filterPlaceholder: '请输入示例{"AppKey" : {"$in": ["4568", "456"]}}'
    }
  },
  watch: {
    kafkaSink(val) {
      if (val) {
        // 设置kafka默认字段
        if (!this.form.partition) {
          this.form.partition = 1
          this.updateSinkDsProperties(1, 'partition')
        }
      } else {
        const { partitioner, partition, ...others } = this.configData.job.sink.properties
        // 删除kafka相关字段
        if (partitioner || partition) {
          const payload = { type: 'publish', keyPath: ['job', 'sink', 'properties'], value: others }
          this.UPDATE_CONFIG_ITEM(payload)
        }
      }
    }
  },
  created() {
    this.updatePageData()
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    change() {
      this.$forceUpdate()
    },
    updatePageData() {
      // 从配置读取数据，更新页面 
      const { collectType } = this.configData
      const { dsId, dsType, dsName, dsUserName, connector, ...otherSourceProperties } = this.configData.job.sources[0].properties
      if (dsId && dsType && dsName) {
        this.dsOptionsBySource = [{ dsId, dsType, dsName, dsProperties: { dsUserName } }]
      }
      if (dsType === 'HTTP') {
        const { requestHeader, requestParams } = otherSourceProperties
        otherSourceProperties.requestHeader = this.getConvertData(requestHeader)
        otherSourceProperties.requestParams = this.getConvertData(requestParams)
      }
      this.form = { dsId, collectType, connector, ...otherSourceProperties }
      // if (collectType === 1 && this.dsConnector === 'jdbc') {
      //   this.$set(this.form, 'scan_fetch-size', this.configData.job.sources[0].properties['scan_fetch-size'])
      // }
      if (connector === 'kafka') {
        this.$refs['kafkaFormPart'] && this.$refs['kafkaFormPart'].updatePageData()
      } else if (['jdbc_scan', 'mongo_scan'].includes(connector)) {
        this.$refs['jdbcscanFormPart'] && this.$refs['jdbcscanFormPart'].updatePageData()
      }
      this.$refs['cmEditor'] && this.$refs['cmEditor'].refresh()
      this.$refs['datastruct'] && this.$refs['datastruct'].updatePageData()
    },
    getDsLabel(item) {
      if (!item) return ''
      let label = '(' + item.dsType + ')' + item.dsName
      if (item.dsProperties && item.dsProperties.dsUserName && this.mildEditable) {
        label = label + ' [' + item.dsProperties.dsUserName + ']'
      }
      return label
    },
    handleAddDataSource() {
      if (!this.configData.appKey) {
        this.$message.warning('请先选择一个应用！')
        return
      }
      const data = { dsAppKey: this.configData.appKey, dsAppName: this.configData.appName, dsNameList: this.dataSourceOptions.map(item => item.dsName) }
      this.$refs.addDatasouceDialog.show(data)
    },
    handleDsIdChange(val) {
      let payload = { type: 'publish', keyPath: ['job', 'sources', 0, 'properties', 'dsId'], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
      const dsType = (this.dataSourceOptions.find(item => item.dsId === val) || {}).dsType
      if (!dsType) return 
      // 考虑联动需要，将dsType放进结构里
      payload = { type: 'publish', keyPath: ['job', 'sources', 0, 'properties', 'dsType'], value: dsType }
      this.UPDATE_CONFIG_ITEM(payload)
      const dsConnector = this.dt_dsConnector[dsType]
      // 联动采集类型和采集方式
      if (['filesystem', 'elasticsearch-6', 'elasticsearch-7', 'COS', 'HTTP'].includes(dsConnector)) {
        // 只允许全量采集
        this.handleCollectTypeChange(1)
      } else if (['kafka', 'http_write'].includes(dsConnector)) {
        // 只允许增量采集
        this.handleCollectTypeChange(2)
        // 增量采集方式只能为kafka
      } else if (['mongo', 'jdbc'].includes(dsConnector)) {
        // jdbc mongo增量情况
        this.handleCollectTypeChange(this.form.collectType)
      }
    },
    handleCollectTypeChange(val) {
      let payload = {}
      if (this.form.collectType !== val) {
        // 代码触发该函数的情况
        this.form.collectType = val
        // 重置调度
        payload = { type: 'publish', keyPath: ['setting', 'scheduleStrategy'], value: this.defaultConfig.setting.scheduleStrategy }
        this.UPDATE_CONFIG_ITEM(payload)
      }
      payload = { type: 'publish', keyPath: ['collectType'], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
      this.handleConnectorChange(undefined)
      
      // 增量时重试策略为false
      if (val === 2 && this.configData.setting.retryStrategy.enabled) {
        payload = { type: 'publish', keyPath: ['setting', 'retryStrategy', 'enabled'], value: false }
        this.UPDATE_CONFIG_ITEM(payload)
      }
    },
    handleConnectorChange(val) {
      this.form.connector !== val && (this.form.connector = val) // 代码触发该函数的情况
      this.updateSourcesProperties(val, 'connector')
      this.normalizeSourcesProperties()
    },
    normalizeSourcesProperties() {
      // 当数据源的类型改变时，需要规范sources[0].properties里的属性，仅保留该类型对应的属性
      const properties = this.configData.job.sources[0].properties
      const dsConnector = this.dt_dsConnector[properties.dsType] // 这个不是sources[0].properties.connector/this.form.connector
      let payload = { type: 'publish', keyPath: ['job', 'sources', 0, 'properties'], value: {} }
      let keys = []
      let defautValue = {}
      this.form = { dsId: this.form.dsId, collectType: this.form.collectType, connector: this.form.connector }
      if (dsConnector === 'jdbc') {
        if (this.form.connector === 'jdbc_scan') {
          keys = ['dsId', 'dsType', 'connector', 'query', 'increment_column', 'increment_column_type', 'polling_interval_seconds', 'scan_startup_mode', 'scan_startup_specific-offset', 'polling_offset']
          defautValue = { increment_column_type: 0, polling_interval_seconds: 10, query: '', polling_offset: 0 }
          // form的设置在子组件内完成
        } else {
          defautValue = { 'scan_fetch-size': properties['scan_fetch-size'] || 100, query: properties['query'] || '' }
          keys = ['dsId', 'dsType', 'query', 'scan_fetch-size']
          this.$set(this.form, 'scan_fetch-size', defautValue['scan_fetch-size'])
        }
        this.$set(this.form, 'query', defautValue['query'])
      } else if (dsConnector === 'kafka') {
        keys = ['dsId', 'dsType', 'connector', 'topic', 'scan_startup_mode', 'scan_startup_specific-offsets', 'scan_startup_timestamp-millis', 'properties_group_id', 'format']
        defautValue = { scan_startup_mode: 'group-offsets', format: 'json' }
        // form的设置在子组件内完成
      } else if (dsConnector === 'filesystem') {
        keys = ['dsId', 'dsType', 'format', 'path']
        defautValue = { format: '', path: properties['path'] || '' }
        this.$set(this.form, 'format', '')
        this.$set(this.form, 'path', defautValue['path'])
      } else if (dsConnector === 'COS') {
        keys = ['dsId', 'dsType', 'format', 'prefix']
        defautValue = { format: '', prefix: properties['prefix'] || '' }
        this.$set(this.form, 'format', '')
        this.$set(this.form, 'prefix', defautValue['prefix'])
      } else if (dsConnector === 'mongo') {
        keys = ['dsId', 'dsType', 'collection', 'format', 'raw_charset', 'filter']
        defautValue = { format: 'json', collection: properties['collection'] || '', filter: properties['filter'] || '' }
        if (this.form.connector === 'mongo_scan') {
          keys.push(...['connector', 'increment_column', 'increment_column_type', 'polling_interval_seconds', 'scan_startup_mode', 'scan_startup_specific-offset'])
          Object.assign(defautValue, { increment_column_type: 0, polling_interval_seconds: 10 })
          // form的设置在子组件内完成
        }
        this.$set(this.form, 'format', 'json')
        this.$set(this.form, 'collection', defautValue['collection'])
        this.$set(this.form, 'filter', defautValue['filter'])
        this.handleFormatChange('')
      } else if (['elasticsearch-6', 'elasticsearch-7'].includes(dsConnector)) {
        keys = ['dsId', 'dsType', 'index', 'doucumentType', 'format', 'raw_charset', 'query']
        defautValue = { index: properties['index'] || '', format: 'json', doucumentType: properties['doucumentType'] || '', query: properties['query'] || '' }
        this.$set(this.form, 'format', 'json')
        this.$set(this.form, 'index', defautValue['index'])
        this.$set(this.form, 'doucumentType', defautValue['doucumentType'])
        this.$set(this.form, 'query', defautValue['query'])
        this.handleFormatChange('')
      } else if (dsConnector === 'HTTP') {
        keys = ['dsId', 'dsType', 'requestHeader', 'requestParams', 'body']
        // 自动填充option的值
        const option = this.dataSourceOptions.find(item => item.dsId === this.form.dsId)
        const { requestHeader, requestParams, body } = option.dsProperties
        const header = this.getConvertData(requestHeader)
        const params = this.getConvertData(requestParams)
        this.$set(this.form, 'requestHeader', header)
        this.$set(this.form, 'requestParams', params)
        this.$set(this.form, 'body', body)
        defautValue = { requestHeader, requestParams, body }
      } else {
        keys = ['dsId', 'dsType']
      }
      keys.forEach(key => payload.value[key] = isDef(defautValue[key]) ? defautValue[key] : properties[key])
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleFormatChange(val) {
      this.updateSourcesProperties(val, 'format')
      if (val === 'raw') {
        this.$set(this.form, 'raw_charset', 'utf-8')
        this.updateSourcesProperties('utf-8', 'raw_charset')
      } else {
        this.form.raw_charset = undefined
        this.updateSourcesProperties(undefined, 'raw_charset')
      }
    },
    handleCopyUrl(className) {
      const clipboard = new ClipboardJS(className)
      const _this = this
      clipboard.on('success', function () {
        _this.$sdc.toast('复制成功')
      })
      clipboard.on('error', function () {
        _this.$sdc.toast('复制失败')
      })
    },
    handleQueryChange(value) {
      this.form.query = value
      this.updateSourcesProperties(value, 'query')
      this.$refs['form'].validateField('query')    
    },
    handleCmReady(cm) {
      cm.on('keypress', () => {
        if (this.moderateEditable) cm.showHint()
      })
    },
    handleFormatter() {
      const sqlText = sqlFormatter.format(this.form.query)
      if (this.form.query !== sqlText) {
        this.handleQueryChange(sqlText)
      }
    },
    handleTestConnect() {
      if (!this.form.dsId) {
        this.$message.warning('请先选择一个数据源！')
        return
      }
      const params = { dsId: this.form.dsId }
      this.btnConnLoading = true
      testDataSourceById(params).then(res => {
        this.$message.success('连接成功！')
        this.btnConnLoading = false
      }).catch(res => {
        this.$message.error('连接失败！原因：' + res.message || '暂无')
        this.btnConnLoading = false
      }) 
    },
    handleSubmitDs(option) {
      // 新增数据源成功后, 加入下拉列表
      if (option) {
        const options = [...this.dataSourceOptions, option]
        const payload = { type: 'publish', keyPath: ['cache', 'dataSourceOptions'], value: options }
        this.UPDATE_CONFIG_ITEM(payload)
        this.form.dsId = option.dsId
        this.handleDsIdChange(option.dsId)
      }
    },
    updateSinkDsProperties(val, name) {
      // 更新sink.properties里的字段
      const payload = { type: 'publish', keyPath: ['job', 'sink', 'properties', name], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    updateSourcesProperties(val, name) {
      // 更新sources[0].properties里的字段
      const payload = { type: 'publish', keyPath: ['job', 'sources', 0, 'properties', name], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    formValidate() {
      // 校验本组件内的表单项
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      if (this.dsConnector === 'jdbc' && this.form.query === '') {
        validRes = false
        this.queryValid = false
      }
      if (!this.$refs['datastruct'].formValidate()) {
        validRes = false
      }
      return validRes
    },
    addRow (key) {
      this.form[key].push({ key: '', value: '' })
    },
    removeRow (key, index) {
      this.form[key].splice(index, 1)
      this.updateSourcesByName(key)
    },
    updateSourcesByName(name) {
      const array = this.form[name]
      const obj = array.reduce((prev, item) => {
        const { key, value } = item
        if (key && value) {
          prev[key] = value
        }
        return prev
      }, {})
      const value = DataType.isEmptyObject(obj) ? '' : JSON.stringify(obj)
      this.updateSourcesProperties(value, name)
    },
    getConvertData(params) {
      let data = [{ key: '', value: '' }]
      if (params) {
        const obj = JSON.parse(params)
        data = Object.entries(obj).reduce((prev, item) => {
          const [key, value] = item
          return prev.push({ key, value }), prev
        }, [])
      }
      return data
    }
  }
  
}
</script>
<style lang="less" scoped>
 @import "~assets/css/bids-collect-rule.less";
</style>

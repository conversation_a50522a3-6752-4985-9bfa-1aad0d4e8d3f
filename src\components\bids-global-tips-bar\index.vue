<!--
 * @Description: 全局提示栏，至于配置界面的footer栏左侧
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-14 14:49:59
-->
<template>
  <div class="bids-global-tips-bar">
    <var-expression-tip></var-expression-tip>
    <desensite-rule-tip></desensite-rule-tip>
  </div>
</template>
<script>
import VarExpressionTip from './var-expression-tip'
import DesensiteRuleTip from './desensite-rule-tip.vue'
export default {
  name: 'bids-global-tips-bar',
  components: {
    VarExpressionTip,
    DesensiteRuleTip
  }
}
</script>

<style lang="less">
@import '~assets/css/bids-global-tips-bar.less';
</style>

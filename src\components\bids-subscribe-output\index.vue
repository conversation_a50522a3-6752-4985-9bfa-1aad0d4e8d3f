<!--
 * @Description: 数据订阅-数据订阅的目的输出组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-09-23 10:10:07
-->
<template>
  <div class="bids-subscribe-output">
    <el-form :model="form" ref="form" size="small" style="min-height: 400px;" :label-width="labelWidth" :rules="rules">
      <el-form-item label="数据输出方式" required>
        <el-tag size="small" effect="plain">{{dt_collectType.get(configData.collectType) || '未定'}}</el-tag>
      </el-form-item>
      <el-form-item label="订阅数据获取方式" prop="collectWay">
        <el-select v-model="form.collectWay" placeholder="请选择" @change="handleCollectWayChange" :disabled="!moderateEditable" v-if="mildEditable">
          <el-option label="推送" value="PUSH"></el-option>
          <el-option label="拉取" value="PULL" :disabled="configData.collectType !== 1"></el-option>
        </el-select>
        <span v-else>{{ form.collectWay === 'PUSH' ? '推送' : '拉取' }}</span>
      </el-form-item>
      <!-- 推送模式 -->
      <el-form-item label="数据输出去向" prop="dsId" v-if="configData.collectWay !== 'PULL'">
        <el-select v-model="form.dsId" @change="handleDsIdChange" :disabled="!moderateEditable" v-if="mildEditable">
          <el-option v-for="item in dataSourceOptions" :key="item.dsId" :label="getDsLabel(item)" :value="item.dsId"></el-option>
        </el-select>
        <span v-else>{{getDsLabel(dsOptionsBySink[0])}}</span>
        <span v-if="moderateEditable">
          <el-button style="margin-left:20px;" @click="handleAddDataSource">新增数据源</el-button>
          <el-button style="margin-left:10px;" @click="handleTestConnect" :loading="btnConnLoading">测试连接</el-button>
        </span>
      </el-form-item>
      <el-form-item label="覆盖策略" prop="overrideStrategy">
        <el-select v-model="form.overrideStrategy" @change="handleOverrideStrategyChange" placeholder="请选择" :disabled="!moderateEditable"  v-if="mildEditable">
          <el-option label="覆盖" value="TRUNCATE" :disabled="disabledTruncate"></el-option>
          <el-option label="追加" value="APPEND" :disabled="configData.collectWay === 'PULL'"></el-option>
        </el-select>
        <span v-else>{{ form.overrideStrategy === 'TRUNCATE' ? '覆盖' : '追加' }}</span>
      </el-form-item>
      <!-- 拉取模式 -->
      <template v-if="form.collectWay === 'PULL'">
        <el-form-item label="数据格式" prop="format">
          <el-select v-model="form.format" @change="(val) => { updateSinkDsProperties(val, 'format') }" :disabled="!moderateEditable" v-if="mildEditable">
            <el-option v-for="item in pullFormatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <span v-else>{{this.pullFormatOptions.find(item => item.value === form.format).label}}</span>
        </el-form-item>
        <!-- 添加是否压缩按钮 默认为关闭 -->
        <el-form-item label="是否压缩">
          <el-switch v-model="form.isCompress" v-if="mildEditable" :disabled="!moderateEditable" @change="(val) => { updateSinkDsProperties(val, 'isCompress') }"></el-switch>
          <span v-else>{{ form.isCompress ? '是' : '否' }}</span>
        </el-form-item>
        <!-- 添加返回空文件按钮 默认为关闭 -->
        <el-form-item label="无数据时返回空文件">
          <el-switch v-model="form.returnDefaultEmptyFile" v-if="mildEditable" :disabled="!moderateEditable" @change="(val) => { updateSinkDsProperties(val, 'returnDefaultEmptyFile') }"></el-switch>
          <span v-else>{{ form.returnDefaultEmptyFile ? '是' : '否' }}</span>
        </el-form-item>
        <el-form-item label="数据下载接口路径" prop="downloadUrl" required>
          <div class="downloadUrlPanel">
            <span :data-clipboard-text="form.downloadUrl" id="downloadUrl">{{form.downloadUrl}}</span>
            <div class="event-icon copy-icon" data-clipboard-target="#downloadUrl" @click="handleCopyUrl"><i class="el-icon-document-copy"></i></div>
          </div>
          <el-button style="margin-top:15px;" v-if="['edit'].includes(pageStatus)" :disabled="!subEditable" @click="handleExport">下 载</el-button>
        </el-form-item>
      </template>
      <template v-if="configData.collectWay === 'PUSH'">
        <!-- 推送:jdbc -->
        <template  v-if="curDsConnector === 'jdbc'">
          <el-form-item label="数据输出表名" prop="tableName">
            <el-input v-model="form.tableName" class="custom-input" @change="(val) => { updateSinkDsProperties(val, 'table-name') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
            <span v-else>{{form.tableName}}</span>
          </el-form-item>
          <el-form-item label="缓冲的最大记录数" prop="sinkBufferFlushMaxRows">
            <el-input-number v-model="form.sinkBufferFlushMaxRows" size="mini" :precision="0" :min="0" @change="(val) => {updateSinkDsProperties(val, 'sink_buffer-flush_max-rows')}" :disabled="!mildEditable" v-if="mildEditable"/>
            <span v-else>{{form.sinkBufferFlushMaxRows}}</span>
          </el-form-item>
          <el-form-item label="提交缓冲记录间隔" prop="sinkBufferFlushInterval">
            <el-input-number v-model="form.sinkBufferFlushInterval" size="mini" :precision="0" :min="0" @change="(val) => {updateSinkDsProperties(val * 1000, 'sink_buffer-flush_interval')}" :disabled="!mildEditable" v-if="mildEditable"/>
            <span v-else>{{form.sinkBufferFlushInterval}}</span>
            秒
          </el-form-item>
          <el-form-item label="写数据失败重试次数" prop="sink_max-retries">
            <el-input-number v-model="form.sinkMaxRetries" size="mini" :precision="0" :min="0" @change="(val) => {updateSinkDsProperties(val, 'sink_max-retries')}" :disabled="!mildEditable" v-if="mildEditable"/>
            <span v-else>{{form.sinkMaxRetries}}</span>
          </el-form-item>
        </template>
        <!-- 推送:starrocks -->
        <template  v-if="curDsConnector === 'starrocks'">
          <el-form-item label="数据输出表名" prop="tableName">
            <el-input v-model="form.tableName" class="custom-input" @change="(val) => { updateSinkDsProperties(val, 'table-name') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
            <span v-else>{{form.tableName}}</span>
          </el-form-item>

          <el-form-item label="缓冲的最大字节数" prop="sinkBufferFlushMaxBytes">
            <el-input-number v-model="form.sinkBufferFlushMaxBytes" size="mini" :precision="0" :min="64 " :max="10 * 1024" @change="(val) => {updateSinkDsProperties(val, 'sink_buffer-flush_max-bytes')}" :disabled="!mildEditable" v-if="mildEditable"/>
            <span v-else>{{form.sinkBufferFlushMaxBytes}}</span>
            M
          </el-form-item>

          <el-form-item label="缓冲的最大记录数" prop="sinkBufferFlushMaxRows">
            <el-input-number v-model="form.sinkBufferFlushMaxRows" size="mini" :precision="0" :min="64000" :max="5000000" @change="(val) => {updateSinkDsProperties(val, 'sink_buffer-flush_max-rows')}" :disabled="!mildEditable" v-if="mildEditable"/>
            <span v-else>{{form.sinkBufferFlushMaxRows}}</span>
            行
          </el-form-item>

          <el-form-item label="提交缓冲记录间隔" prop="sinkBufferFlushInterval">
            <el-input-number v-model="form.sinkBufferFlushInterval" size="mini" :precision="0" :min="1" :max="3600" @change="(val) => {updateSinkDsProperties(val, 'sink_buffer-flush_interval')}" :disabled="!mildEditable" v-if="mildEditable"/>
            <span v-else>{{form.sinkBufferFlushInterval}}</span>
            秒
          </el-form-item>

          <el-form-item label="严格模式" prop="sinkPropertiesStrictMode">
            <template  v-if="mildEditable">
              <el-select v-model="form.sinkPropertiesStrictMode" size="mini" @change="(val) => {updateSinkDsProperties(val, 'sink_properties_strict_mode')}" :disabled="!mildEditable">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
              <el-tooltip effect="dark" placement="top" style="margin-left: 5px;">
                <div slot="content">
                  开启严格模式，starrocks会把错误的数据行过滤掉，只导入正确的数据行，并返回错误数据详情
                  <br/>
                  关闭严格模式，starrocks会把转换失败的错误字段转换成NULL值，并把这些包含NULL值的错误数据行跟正确的数据行一起导入
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
            <span v-else>{{form.sinkPropertiesStrictMode}}</span>
          </el-form-item>

          <el-form-item label="semantic" prop="sinkSemantic">
            <el-select v-model="form.sinkSemantic" size="mini" @change="(val) => {updateSinkDsProperties(val, 'sink_semantic')}" :disabled="!mildEditable || configData.collectType === 1" v-if="mildEditable">
              <el-option label="至少一次" value="at-least-once"></el-option>
              <el-option label="仅一次" value="exactly-once"></el-option>
            </el-select>
            <span v-else>{{form.sinkSemantic}}</span>
          </el-form-item>

          <el-form-item label="局部更新" prop="sinkPropertiesPartialUpdate">
            <template v-if="mildEditable">
              <el-select v-model="form.sinkPropertiesPartialUpdate" size="mini" @change="(val) => {updateSinkDsProperties(val, 'sink_properties_partial_update')}" :disabled="!mildEditable">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
              <div>{{ partialUpdateTip }}</div>
            </template>
            <span v-else>{{form.sinkPropertiesPartialUpdate}}</span>
          </el-form-item>
          
        </template>
        <!-- 推送:es -->
        <template v-if="curDsConnector.startsWith('elasticsearch')">
          <el-form-item label="文档索引名称" prop="index">
            <el-input v-model="form.index" class="custom-input" @change="(val) => { updateSinkDsProperties(val, 'index') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
            <span v-else>{{form.index}}</span>
          </el-form-item>
          <el-form-item label="文档类型" prop="documentType" v-if="curDsConnector === 'elasticsearch-6'">
            <el-input v-model="form.documentType" class="custom-input" @change="(val) => { updateSinkDsProperties(val, 'document-type') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
            <span v-else>{{form.documentType}}</span>
          </el-form-item>
          <el-form-item label="单列输出">
            <el-checkbox v-model="form.format" true-label="raw" false-label="json" @change="handleFormatChange" :disabled="!moderateEditable">是否将输入单列的值作为原始值输出</el-checkbox>
          </el-form-item>
          <el-form-item label="原始值charset" prop="rawCharset" v-if="form.format === 'raw'">
            <el-input class="custom-input" v-model="form.rawCharset" @change="(val) => { updateSinkDsProperties(val, 'raw_charset') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
            <span v-else>{{form.rawCharset}}</span>
          </el-form-item>
        </template>
        <!-- 推送:fs -->
        <template v-if="curDsConnector === 'filesystem'">
          <el-form-item label="数据格式" prop="format">
            <el-select v-model="form.format" @change="handleFormatChange" :disabled="!moderateEditable" v-if="mildEditable">
              <el-option v-for="item in fsFormatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <span v-else>{{this.fsFormatOptions.find(item => item.value === form.format).label}}</span>
          </el-form-item>
          <el-form-item label="原始值charset" prop="rawCharset" v-if="form.format === 'raw'">
            <el-input class="custom-input" v-model="form.rawCharset" @change="(val) => { updateSinkDsProperties(val, 'raw_charset') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
            <span v-else>{{form.rawCharset}}</span>
          </el-form-item>
          <el-form-item label="数据输出路径" prop="path">
            <el-input v-model="form.path" style="width: 420px;" class="custom-input" @change="(val) => { updateSinkDsProperties(val, 'path') }" :disabled="!moderateEditable" v-if="mildEditable" placeholder="/dirA/dirB/dirC"></el-input>
            <span v-else>{{form.path}}</span>
          </el-form-item>
        </template>
        <!-- 推送:kafka -->
        <template v-if="curDsConnector === 'kafka'">
          <el-form-item label="主题名称" prop="topic">
            <el-input v-model="form.topic" class="custom-input" @change="(val) => { updateSinkDsProperties(val, 'topic') }" :disabled="!moderateEditable" v-if="mildEditable"/>
            <span v-else>{{form.topic}}</span>
          </el-form-item>
          <el-form-item label="数据格式" prop="format">
            <el-select v-model="form.format" @change="handleFormatChange" :disabled="!moderateEditable" v-if="mildEditable">
              <el-option v-for="item in fsFormatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <span v-else>{{this.formatOptions.find(item => item.value === form.format).label}}</span>
          </el-form-item>
          <el-form-item label="原始值charset" prop="rawCharset" v-if="form.format === 'raw'">
            <el-input class="custom-input" v-model="form.rawCharset" @change="(val) => { updateSourcesProperties(val, 'raw_charset') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
            <span v-else>{{form.rawCharset}}</span>
          </el-form-item>
          <el-form-item label="分区器" prop="partitioner">
            <el-select v-model="form.partitioner" @change="(val) => { updateSinkDsProperties(val, 'partitioner') }" :disabled="!moderateEditable" v-if="mildEditable">
              <el-option v-for="item in partitionerOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <span v-else>{{this.partitionerOptions.find(item => item.value === form.partitioner).label}}</span>
          </el-form-item>
        </template>
        <!-- 推送:mongo -->
        <template  v-if="curDsConnector === 'mongo'">
          <el-form-item label="数据输出集合" prop="collection">
            <el-input v-model="form.collection" class="custom-input" @change="(val) => { updateSinkDsProperties(val, 'collection') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
            <span v-else>{{form.collection}}</span>
          </el-form-item>
          <el-form-item label="单列输出">
            <el-checkbox v-model="form.format" true-label="raw" false-label="json" @change="handleFormatChange" :disabled="!moderateEditable">是否将输入单列的值作为原始值输出</el-checkbox>
          </el-form-item>
          <el-form-item label="原始值charset" prop="rawCharset" v-if="form.format === 'raw'">
            <el-input class="custom-input" v-model="form.rawCharset" @change="(val) => { updateSinkDsProperties(val, 'raw_charset') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
            <span v-else>{{form.rawCharset}}</span>
          </el-form-item>
        </template>
      </template>
    </el-form>
    <bids-datastruct-form-part type="sink" ref="datastruct" :page-status="pageStatus" :partial-update="partialUpdate" configType="subscribe" :init-data="{ appName: form.appName, ...dataSourceOptions.find(item => item.dsId === form.dsId) }" :label-width="labelWidth"></bids-datastruct-form-part>
    <bids-datasource-dialog ref="addDatasouceDialog" @submit="handleSubmitDs" type="add" :options="addDsOptions"></bids-datasource-dialog>
    <div class="description-panel" v-if="mildEditable">
      <div class="question">
        <i class="el-icon-question"></i>
        <span>数据输出方式</span>
      </div>
      <div class="answer">
        <span>只有需订阅的数据发布都为全量时，方为全量输出；否则为增量输出</span>
      </div>
      <template v-if="configData.collectType === 1 && configData.collectWay !== 'PULL'">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>覆盖策略</span>
        </div>
        <div class="answer">
          <span>追加策略下，可定义一个STRING类型，列名为BIDS_BATCH_NUMBER的字段作为批次号</span>
        </div>
      </template>
      <template v-if="['kafka', 'mongo', 'filesystem'].includes(curDsConnector)">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>单列输出</span>
        </div>
        <div class="answer">
          <span>当选择将输入单列的值作为原始值输出，输入和输出数据的结构保持一致且都只有1个列<span v-if="curDsConnector === 'mongo'">，类型是STRING</span></span>
        </div>
      </template>
      <template v-if="curDsConnector === 'jdbc'">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>缓冲相关设置</span>
        </div>
        <div class="answer">
          <span>设置为0表示禁用</span>
        </div>
      </template>
      <template v-if="curDsConnector === 'filesystem'">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>数据输出路径</span>
        </div>
        <div class="answer">
          <span>指明输出到HDFS文件夹的相对路径，3层目录以上</span>
        </div>
      </template>
      <div class="question">
        <i class="el-icon-question"></i>
        <span>输出数据的结构</span>
      </div>
      <div class="answer">
        <span>需要和订阅规则加工后的数据结构保持一致，顺序也要相同，真实的输出库中也必须要包含定义的字段，平台提供一定的方式辅助定义数据的结构</span>
      </div>
      <div class="question">
        <i class="el-icon-question"></i>
        <span>脱敏规则</span>
      </div>
      <div class="answer">
        <span>对于勾选为加密的数据列，只会对原值进行加密，不会再进行脱敏</span>
      </div>
    </div>
  </div>
</template>
<script>
import BidsDatasourceDialog from 'components/bids-datasource-dialog'
import BidsDatastructFormPart from 'components/bids-datastruct-form-part'
import { mapState, mapMutations } from 'vuex'
import { testDataSourceById, downloadFile } from 'services/common.service'
import { editable } from 'mixins'
import { camelCase } from 'camel-case'
import { conf } from 'utils'
import ClipboardJS from 'clipboard'

export default {
  name: 'bids-subscribe-output',
  mixins: [editable],
  components: {
    BidsDatasourceDialog,
    BidsDatastructFormPart
  },
  props: {
    pageStatus: String,
    subEditable: { // 用于subscribe-edit的按钮权限
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapState({
      configData: 'currentSubscribeConfig',
      dt_collectType: 'dt_collectType',
      dt_dsConnector: 'dt_dsConnector'
    }),
    disabledTruncate() {
      // return ['elasticsearch-6', 'elasticsearch-7', 'kafka', 'mongo'].includes(this.curDsConnector)
      return ['kafka'].includes(this.curDsConnector)
    },
    dataSourceOptions() {
      return (this.configData.cache.dataSourceOptions || this.dsOptionsBySink).filter(item => !['ESjdbc', 'Hive'].includes(item.dsType))
    },
    partialUpdate() {
      return !!this.form.sinkPropertiesPartialUpdate
    },
    partialUpdateTip() {
      return this.partialUpdate ? '目的表需要设置主键，并在输出数据结构中配置主键' : '全字段输出，输出数据结构需与目的表字段一致'
    }
  },
  data() {
    return {
      configType: 'subscribe',
      form: {},
      appOptions: [],
      fsFormatOptions: [
        { label: 'json', value: 'json' }, { label: 'csv', value: 'csv' }, { label: 'parquet', value: 'parquet' }, { label: 'raw', value: 'raw' }
        // { label: 'avro', value: 'avro' }, { label: 'orc', value: 'orc' }
      ],
      partitionerOptions: [{ label: 'default', value: 'default' }, { label: 'fixed', value: 'fixed' }, { label: 'round-robin', value: 'round-robin' }],
      pullFormatOptions: [{ label: 'json', value: 'file_json' }, { label: 'csv', value: 'file_csv' }, { label: 'dat', value: 'file_dat' }, { label: 'excel', value: 'file_excel' }],
      labelWidth: '140px',
      activeScript: 'beforeEvent',
      btnConnLoading: false,
      curDsConnector: '',
      rules: {
        collectWay: [{ required: true, message: '请选择订阅数据获取方式', trigger: 'change' }],
        dsId: [{ required: true, message: '请选择数据输出去向' }],
        overrideStrategy: [{ required: true, message: '请选择落地覆盖策略', trigger: 'change' }],
        tableName: [{ required: true, message: '请输入数据输出表名' }],
        index: [{ required: true, message: '请输入文档索引名称' }],
        documentType: [{ required: true, message: '请输入文档类型' }],
        path: [
          { required: true, message: '请输入数据输出路径' },
          { pattern: /^(\\|\/.+\1{0,1}){3,}$/, message: '路径格式不正确' }
        ],
        format: [{ required: true, message: '请选择数据格式' }],
        topic: [{ required: true, message: '请输入主题名称' }],
        partitioner: [{ required: true, message: '请选择分区器', trigger: 'change' }],
        collection: [{ required: true, message: '请输入数据输出集合' }],
        rawCharset: [{ required: true, message: '请输入原始值charset' }],
        sinkPropertiesStrictMode: [{ required: true, message: '请选择严格模式', trigger: 'change' }],
        sinkSemantic: [{ required: true, message: '请选择semantic', trigger: 'change' }],
        sinkPropertiesPartialUpdate: [{ required: true, message: '请选择局部更新', trigger: 'change' }]
      },
      dsOptionsBySink: [],
      addDsOptions: [
        { value: 'ES6', label: 'Elasticsearch 6' }, { value: 'ES7', label: 'Elasticsearch 7' },
        { value: 'HDFS', label: 'HDFS' }, { value: 'Kafka', label: 'Kafka_0.11及以后版本' },
        { value: 'MongoDB', label: 'MongoDB' }, { value: 'MySQL', label: 'MySQL' }, { value: 'Oracle', label: 'Oracle' }, { value: 'PostgreSQL', label: 'PostgreSQL' },
        { value: 'FrontRepo', label: 'FrontRepo(前置仓)' }, { value: 'SQLServer', label: 'SQLServer' }, { value: 'MySqlTenant', label: 'MySqlTenant' },
        { value: 'clickhouse', label: 'clickhouse' }, { value: 'ApacheIgnite', label: 'ApacheIgnite' }, { value: 'starrocks', label: 'starrocks' }
      ]
    }
  },
  created() {
    this.updatePageData()
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    updatePageData() {
      const { collectWay, collectType } = this.configData
      const overrideStrategy = this.configData.job.sink.overrideStrategy
      const defaultPath = conf('esb').main + '/download/{appkey}/{interfaceCode}/{version}/latest'
      const { dsId, dsType, dsName, dsUserName, downloadUrl = defaultPath, ...otherSinkProperties } = this.configData.job.sink.properties
      if (dsType) {
        this.curDsConnector = this.dt_dsConnector[dsType]
      }
      if (dsId && dsType && dsName) {
        this.dsOptionsBySink = [{ dsId, dsName, dsType, dsProperties: { dsUserName } }]
      }
      const transfromSinkP = {}
      for (const key in otherSinkProperties) {
        transfromSinkP[camelCase(key)] = otherSinkProperties[key]
      }
      // 一些需要特殊处理的属性
      if (dsType !== 'starrocks') {
        transfromSinkP['sinkBufferFlushInterval'] && (transfromSinkP['sinkBufferFlushInterval'] = transfromSinkP['sinkBufferFlushInterval'] / 1000)
      }
      // 填充表单
      this.form = { collectWay, collectType, overrideStrategy, dsId, downloadUrl, ...transfromSinkP }
      this.$refs['datastruct'] && this.$refs['datastruct'].updatePageData()
    },
    getDsLabel(item) {
      if (!item) return ''
      let label = '(' + item.dsType + ')' + item.dsName
      if (item.dsProperties && item.dsProperties.dsUserName && this.mildEditable) {
        label = label + ' [' + item.dsProperties.dsUserName + ']'
      }
      return label
    },
    handleAddDataSource() {
      if (!this.configData.appKey) {
        this.$message.warning('请先选择一个应用！')
        return
      }
      const data = { dsAppKey: this.configData.appKey, dsAppName: this.configData.appName, dsNameList: this.dataSourceOptions.map(item => item.dsName) }
      this.$refs.addDatasouceDialog.show(data)
    },
    handleCollectWayChange(val) {
      this.UPDATE_CONFIG_ITEM({ type: 'subscribe', keyPath: ['collectWay'], value: val })
      // 拉取模式只允许覆盖策略
      if (val === 'PULL' && this.form.overrideStrategy !== 'TRUNCATE') {
        this.form.overrideStrategy = 'TRUNCATE'
        this.handleOverrideStrategyChange('TRUNCATE')
      }
      // 清除用户之前关于输出去向的所有配置
      this.handleDsIdChange(undefined)
    },
    handleDsIdChange(val) {
      this.form.dsId !== val && (this.form.dsId = val)
      const dsType = val ? this.dataSourceOptions.find(item => item.dsId === val).dsType : undefined
      this.curDsConnector = dsType ? this.dt_dsConnector[dsType] : ''
      const keys = Object.keys(this.configData.job.sink.properties)
      keys.forEach(key => key !== 'dsId' && this.form[camelCase(key)] && (this.form[camelCase(key)] = undefined))
      let defaultProperties = {}
      if (this.form.collectWay === 'PULL') {
        const defaultPath = conf('esb').main + '/download/' + `${this.configData.appKey || '{appKey}'}` + '/' + `${this.configData.interfaceCode || '{interfaceCode}'}` + '/' + `${this.configData.version || '{version}'}` + '/latest'
        defaultProperties = { downloadUrl: defaultPath, isCompress: false, returnDefaultEmptyFile: false }
        this.$set(this.form, 'downloadUrl', defaultPath)
      } else if (this.curDsConnector === 'jdbc') {
        defaultProperties = { 'sink_buffer-flush_max-rows': 100, 'sink_buffer-flush_interval': 1000, 'sink_max-retries': 3 }
        this.$set(this.form, `${camelCase('sink_buffer-flush_max-rows')}`, 100)
        this.$set(this.form, `${camelCase('sink_buffer-flush_interval')}`, 1) // 页面是秒，后端保存为毫秒
        this.$set(this.form, `${camelCase('sink_max-retries')}`, 3)
      } else if (this.curDsConnector === 'kafka') {
        defaultProperties = { format: 'json' }
        this.$set(this.form, 'format', 'json')
      } else if (this.curDsConnector === 'mongo' || this.curDsConnector.startsWith('elasticsearch')) {
        defaultProperties = { format: 'json' }
        this.$set(this.form, 'format', 'json')
      } else if (this.curDsConnector === 'starrocks') {
        defaultProperties = { 'sink_buffer-flush_max-rows': 500000, 'sink_buffer-flush_max-bytes': 90, 'sink_buffer-flush_interval': 1, 'sink_properties_strict_mode': true, 'sink_semantic': 'at-least-once', 'sink_properties_partial_update': true }
        this.$set(this.form, `${camelCase('sink_buffer-flush_max-rows')}`, 500000)
        this.$set(this.form, `${camelCase('sink_buffer-flush_interval')}`, 1)
        this.$set(this.form, `${camelCase('sink_buffer-flush_max-bytes')}`, 90)
        this.$set(this.form, `${camelCase('sink_properties_strict_mode')}`, true)
        this.$set(this.form, `${camelCase('sink_semantic')}`, 'at-least-once')
        this.$set(this.form, `${camelCase('sink_properties_partial_update')}`, true)
      }
      // this.$refs.form.clearValidate(keys.map(key => camelCase(key)))
      this.$refs.form.clearValidate()
      let payload = { type: 'subscribe', keyPath: ['job', 'sink', 'properties'], value: { dsId: val, dsType, ...defaultProperties } }
      this.UPDATE_CONFIG_ITEM(payload)
      // 部分数据源仅允许追加策略
      if (this.disabledTruncate && this.form.overrideStrategy === 'TRUNCATE') {
        this.$set(this.form, 'overrideStrategy', 'APPEND')
        this.handleOverrideStrategyChange('APPEND')
      }
    },
    updateSinkDsProperties(val, name) {
      const payload = { type: 'subscribe', keyPath: ['job', 'sink', 'properties', name], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleTestConnect() {
      if (!this.form.dsId) {
        this.$message.warning('请先选择一个数据源！')
        return
      }
      const params = { dsId: this.form.dsId }
      this.btnConnLoading = true
      testDataSourceById(params).then(res => {
        this.$message.success('连接成功！')
        this.btnConnLoading = false
      }).catch(res => {
        this.$message.error('连接失败！原因：' + res.message || '暂无')
        this.btnConnLoading = false
      }) 
    },
    handleSubmitDs(option) {
      // 新增数据源成功, 加入下拉列表
      if (option) {
        const options = [...this.dataSourceOptions, option]
        const payload = { type: 'subscribe', keyPath: ['cache', 'dataSourceOptions'], value: options }
        this.UPDATE_CONFIG_ITEM(payload)
        this.form.dsId = option.dsId
        this.handleDsIdChange(option.dsId)
      }
    },
    handleOverrideStrategyChange(value) {
      const payload = { type: 'subscribe', keyPath: ['job', 'sink', 'overrideStrategy'], value }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleFormatChange(val) {
      this.updateSinkDsProperties(val, 'format')
      if (val === 'raw') {
        this.$set(this.form, 'rawCharset', 'utf-8')
        this.updateSinkDsProperties('utf-8', 'raw_charset')
      } else {
        this.form.rawCharset = undefined
        this.updateSinkDsProperties(undefined, 'raw_charset')
      }
    },
    handleCopyUrl() {
      const clipboard = new ClipboardJS('.copy-icon')
      const _this = this
      clipboard.on('success', function () {
        _this.$sdc.toast('复制成功')
      })
      clipboard.on('error', function () {
        _this.$sdc.toast('复制失败')
      })
    },
    // 下载功能
    async handleExport() {
      let response = await this.$confirm('确定下载数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).catch(() => {})
      if (response !== 'confirm') return
      let path = this.form.downloadUrl.split('/').slice(-4).join('/')
      this.downloads(path)
    },
    downloads(path) {
      const params = { url: path }
      downloadFile(params).then(res => {
      }).catch(e => {
        if (e.response) {
          return this.$message.error(e.message)
        } else {
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = this.form.downloadUrl.replace('/esb/', '/sso/')
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
        }
      })
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      if (this.curDsConnector === 'starrocks' && this.$refs['datastruct']) {
        validRes = this.$refs['datastruct'].subscribePartialUpdateValidate()
      }
      return validRes
    }
  }
}
</script>
<style lang="less">
 @import "~assets/css/bids-subscribe-output.less";
</style>

/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-15 18:51:50
 */
import {
  Avatar, Button, Breadcrumb, BreadcrumbItem, Backtop,
  Dropdown, DropdownMenu, DropdownItem, Icon, Input, Menu,
  MenuItem, Submenu, Select, Option, Tooltip, Table, TableColumn, 
  Pagination, PageHeader, Form, FormItem, Radio, RadioGroup,
  Checkbox, CheckboxGroup, InputNumber, MessageBox, Message, DatePicker,
  Switch, Col, Popover, Tag, Divider, Dialog, Tabs, TabPane, Card, Step, Steps,
  Autocomplete, Tree, Loading, RadioButton, Badge, Link, Drawer, <PERSON><PERSON>, <PERSON>
} from 'element-ui'
import locale from 'element-ui/lib/locale'
import zh from 'element-ui/lib/locale/lang/zh-CN'
import en from 'element-ui/lib/locale/lang/en'

export default {
  components: [
    Avatar,
    Button,
    Breadcrumb,
    BreadcrumbItem,
    Backtop,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    Icon,
    Input,
    Menu,
    MenuItem,
    Submenu,
    Select,
    Option,
    Tooltip,
    Table,
    TableColumn,
    Pagination,
    PageHeader,
    Form,
    FormItem,
    Radio,
    RadioGroup,
    Checkbox,
    CheckboxGroup,
    InputNumber,
    DatePicker,
    Switch,
    Col,
    Popover,
    Tag,
    Divider,
    Dialog,
    Tabs,
    TabPane,
    Card,
    Step,
    Steps,
    Autocomplete,
    Tree,
    Loading.directive,
    RadioButton,
    Badge,
    Link,
    Drawer,
    Alert,
    Row
  ],
  install(Vue) {
    Vue.prototype.$message = Message
    Vue.prototype.$msgbox = MessageBox
    Vue.prototype.$confirm = MessageBox.confirm
    Vue.prototype.$alert = MessageBox.alert
    Vue.prototype.$loading = Loading.service
  },
  locale,
  langs: {
    zh,
    en
  }
}

/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-12-30 20:27:15
 */
import { http, httpFlink, httpScheduler, httpFrontRepo, httpJobservice } from 'utils/http'
import api from 'config/api.conf'

export const queryUserInfo = () => http.get(api.bidsplus_config.common.queryUserInfo)
export const queryApps = (params) => http.get(api.bidsplus_config.common.queryApps, { params })

export const queryDataSources = (params) => http.get(
  api.bidsplus_config.common.queryDataSources.replace('{appKey}', params.appKey || 'null').replace('{dsId}', params.dsId || 'null')
)
export const queryDsPassWord = (params) => http.get(
  api.bidsplus_config.common.queryDsPassWord.replace('{dsPasswordKey}', params.dsPasswordKey || 'null'), { loading: true }
)
export const testDataSourceById = (params) => http.get(api.bidsplus_config.common.testDataSourceById.replace('{datasourceId}', params.dsId), { loading: true })
export const testDataSource = (data) => http.post(api.bidsplus_config.common.testDataSource, { params: data })
export const queryDataSourceRefer = (params) => http.get(api.bidsplus_config.common.queryDataSourceRefer.replace('{datasourceId}', params.dsId), { loading: true })
export const addDataSource = (data) => http.post(api.bidsplus_config.common.addDataSource, { params: data })
export const editDataSource = (params, data) => http.post(api.bidsplus_config.common.editDataSource.replace('{datasourceId}', params.dsId), { params: data })
export const deleteDataSource = (params) => http.del(api.bidsplus_config.common.deleteDataSource.replace('{datasourceId}', params.dsId))
export const scanDsTable = (params, data) => http.post(
  api.bidsplus_config.common.scanDsTable.replace('{datasourceId}', params.dsId), { params: data }
)
export const scanBySQL = (params, data) => http.post(
  api.bidsplus_config.common.scanBySQL.replace('{datasourceId}', params.dsId), { params: data, loading: true, timeout: 1000 * 120 }
)
export const uploadTableSql = (data) => http.post(api.bidsplus_config.common.uploadTableSql, { params: data })
export const startJobNow = (data) => http.post(api.bidsplus_config.common.startJobNow, { params: data, loading: true })
export const runJob = (params) => http.post(api.bidsplus_config.common.runJob.replace('{jobName}', params.jobName))
export const killJob = (params) => http.post(api.bidsplus_config.common.killJob.replace('{jobName}', params.jobName))
export const restartJob = (params, data) => http.post(api.bidsplus_config.common.restartJob.replace('{jobInstId}', params.jobInstId), { params: data })
export const pauseJob = (params) => http.post(api.bidsplus_config.common.pauseJob.replace('{jobName}', params.jobName))
export const resumeJob = (params) => http.post(api.bidsplus_config.common.resumeJob.replace('{jobName}', params.jobName))
export const queryJobState = (data) => http.post(api.bidsplus_config.common.queryJobState, { params: data })
export const queryJobInfo = (params) => http.post(api.bidsplus_config.common.queryJobInfo.replace('{jobName}', params.jobName))
export const queryJobLog = (params, data) => httpFlink.get(api.bidsplus_executor.queryJobLog.replace('{jobName}', params.jobName), { params: data })
export const queryJobShapshot = (params) => http.get(api.bidsplus_config.common.queryJobShapshot.replace('{configId}', params.configId))
export const queryJobTableData = (params, data) => http.post(
  api.bidsplus_config.common.queryJobTableData.replace('{jobName}', params.jobName), { params: data }
)
export const queryJobTableDataCount = (params) => http.get(api.bidsplus_config.common.queryJobTableDataCount.replace('{jobName}', params.jobName))
export const parseSinkTableStructure = (params, data) => http.post(api.bidsplus_config.common.parseSinkTableStructure.replace('{category}', params.category), { params: data, loading: true })

export const queryScheduleLog = (params) => httpScheduler.get(api.dolphinscheduler.queryScheduleLog, { params: params })
export const queryInstances = (params, data) => http.post(
  api.bidsplus_config.common.queryInstances.replace('{configId}', params.configId).replace('{version}', params.version), { params: data }
)
export const genEncryptionKey = (params) => http.get(api.bidsplus_config.common.genEncryptionKey.replace('{encryptionWay}', params.encryptionWay))
export const checkCodeExist = (params) => http.get(api.bidsplus_config.common.checkCodeExist.replace('{appKey}', params.appKey).replace('{interfaceCode}', params.interfaceCode))
export const queryAppsInConfig = (params) => http.get(api.bidsplus_config.common.queryAppsInConfig.replace('{configType}', params.configType).replace('{kind}', params.kind))

export const getSchemaList = () => httpFrontRepo.get(api.frontRepo.getSchemaList)

export const queryTotalInstance = (data) => http.post(api.bidsplus_config.common.queryTotalInstance, { params: data, loading: true })
export const queryJobsBatchRestart = (data) => http.post(api.bidsplus_config.common.queryJobsBatchRestart, { params: data, loading: true })
export const getScheduleInfo = (params) => http.get(api.bidsplus_config.common.getScheduleInfo, { params: params, loading: true })
export const queryTenantLst = (params) => http.post(api.bidsplus_config.common.queryTenantLst, { params: params })
export const queryAllTables = (params) => http.get(api.bidsplus_config.common.queryAllTables, { params: params, loading: false })
export const queryTableColumns = ({ datasourceId, tablename }) => http.post(api.bidsplus_config.common.queryTableColumns.replace('{datasourceId}', datasourceId).replace('{tablename}', tablename), { loading: false })

export const downloadFile = (params) => http.get(
  api.bidsplus_config.common.downloadFile.replace('{appkey}/{interfaceCode}/{version}/latest', params.url), { loading: true, responseType: 'blob' }
)
export const getUser = (params) => http.get(api.bidsplus_config.common.getUser, { params: params })
export const getAppKmsToken = (params) => http.get(api.bidsplus_config.common.getAppKmsToken.replace(/{configId}/, params.configId), { loading: true })
export const querySecretKeysList = (params) => http.get(api.bidsplus_config.common.querySecretKeysList.replace(/{configId}/, params.configId), { loading: true })
export const reqEncryptionKey = (params, encryptionWay) => http.post(api.bidsplus_config.common.reqEncryptionKey.replace(/{encryptionWay}/, encryptionWay), { params, loading: true })
export const getWhiteList = () => http.get(api.bidsplus_config.common.getWhiteList)

export const queryJobserviceJobLog = (params) => httpJobservice.get(api.jobservice.queryJobLog, { params })
export const queryJobserviceScheduleLog = (params) => httpJobservice.get(api.jobservice.queryScheduleLog, { params })

// 同步-获取PUB的同步记录列表
export const querySyncPubRecordList = (params) => http.post(api.bidsplus_config.sync.querySyncPubRecordList, { params, loading: true })
// 同步-获取SUB的同步记录列表
export const querySyncSubRecordList = (params) => http.post(api.bidsplus_config.sync.querySyncSubRecordList, { params, loading: true })
// 同步-预校验
export const querySyncPreImport = (params) => http.post(api.bidsplus_config.sync.querySyncPreImport, { params, loading: true })
// 同步-同步
export const querySyncImport = (params) => http.post(api.bidsplus_config.sync.querySyncImport, { params, loading: true })

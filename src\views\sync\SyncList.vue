<template>
  <div class="sync-list-page">
    <div class="sync-env">
      <span>同步记录 (当前环境：{{ env }})</span>
      <el-button class="sync-button" type="primary" size="medium" @click="createSync">新建同步</el-button>
    </div>
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="发布同步" name="publish">
        <data-publish-list ref="publish" @view="handleView"></data-publish-list>
      </el-tab-pane>
      <el-tab-pane label="订阅同步" name="subscribe">
        <data-subscribe-list ref="subscribe" @view="handleView"></data-subscribe-list>
      </el-tab-pane>
    </el-tabs>
    
    <create-sync-dialog ref="createSyncDialog" @confirm="openPreCheck"></create-sync-dialog>
    <pre-check-dialog ref="preCheckDialog" @confirm="openResult" @view="handleView"></pre-check-dialog>
    <sync-result-dialog ref="syncResultDialog" @view="handleView"></sync-result-dialog>
    <sync-detail-dialog ref="syncDetailDialog"></sync-detail-dialog>
  </div>
</template>

<script>
import DataPublishList from './data-publish-list'
import DataSubscribeList from './data-subscribe-list'
import CreateSyncDialog from './create-sync-dialog.vue'
import PreCheckDialog from './pre-check-dialog.vue'
import SyncResultDialog from './sync-result-dialog.vue'
import SyncDetailDialog from './sync-detail-dialog.vue'

export default {
  name: 'sync-list-page',
  components: {
    DataPublishList,
    DataSubscribeList,
    CreateSyncDialog,
    PreCheckDialog,
    SyncResultDialog,
    SyncDetailDialog
  },
  data() {
    return {
      activeTab: 'publish'
    }
  },
  computed: {
    env() {
      return {
        loc: '本地',
        dev: '测试',
        uat: 'UAT',
        prd: '生产'
      }[process.env.VUE_APP_BUILD_ENV]
    }
  },
  activated() {
    this.$refs[this.activeTab].fetchData() // 回到页面时重新加载数据
  },
  methods: {
    handleClick(tab) {
      this.$refs[tab.name].fetchData() // 切换tag时重新加载数据
    },
    createSync() {
      this.$refs.createSyncDialog.show()
    },
    openPreCheck() {
      this.$refs.preCheckDialog.show()
    },
    openResult() {
      this.$refs.syncResultDialog.show()
    },
    handleView() {
      this.$refs.syncDetailDialog.show()
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/sync-list.less";
</style>

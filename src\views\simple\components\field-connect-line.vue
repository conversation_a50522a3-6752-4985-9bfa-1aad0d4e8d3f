<template>
  <div class="connect-row">
    <div v-if="field.mappingFieldKey" class="field-connect-line" @click="deleteConnect(field)">
      <div class="circle-outer"><div class="circle-inner" /></div>
      <span class="line" />
      <span class="delete-line" />
      <i
        v-if="!disabled && !isCanCancelMapping(field)"
        class="delete-btn el-icon-error"
      />
      <span class="line" />
      <span class="triangle-right" />
      <div class="circle-outer"><div class="circle-inner" /></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FieldConnectLine',
  props: {
    field: {
      type: Object,
      default: () => ({})
    },
    // 是否流式集成写入 TDW
    isStreamingIntegrationOfMsgToTdw: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      specialFieldList: ['__binlog_type__', '__binlog_timestamp__']
    }
  },
  methods: {
    // 取消映射
    deleteConnect(field) {
      if (this.disabled
        || this.isStreamingIntegrationOfMsgToTdw
        || this.specialFieldList.includes(field.sourceFieldKey)) {
        return
      }
      this.$emit('delete-connect', field)
    },
    // 根据字段判断是否能取消映射
    isCanCancelMapping(field) {
      if (this.isStreamingIntegrationOfMsgToTdw) {
        return true
      }
      return this.specialFieldList.includes(field.sourceFieldKey)
    }
  }
}
</script>

<style lang="less" scoped>

  .connect-row {
    width: 100%;
  }

  .field-connect-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 12px;

    .line {
      height: 2px;
      background: #85b2f1;
      flex-grow: 1;
      width: calc(50% - 24px);
    }

    .triangle-right {
      width: 0;
      height: 0;
      border-top: 5px solid transparent;
      border-left: 10px solid #85b2f1;
      border-bottom: 5px solid transparent;
    }

    .circle-outer {
      width: 12px;
      height: 12px;
      background: #e6effc;
      border-radius: 50%;
      position: relative;
    }

    .circle-inner {
      width: 8px;
      height: 8px;
      background: #85b2f1;
      border-radius: 50%;
      position: absolute;
      margin: auto;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
    }

    .delete-line {
      height: 2px;
      background: #85b2f1;
      width: 14px;
    }

    .delete-btn {
      color: #f04545;
      font-size: 14px;
      display: none;
    }

    &:hover {
      .circle-inner {
        background: #0f73ff;
      }

      .triangle-right {
        border-left: 10px solid #0f73ff;
      }

      .line {
        background: #0f73ff;
      }

      .delete-line {
        display: none;
      }

      .delete-btn {
        display: block;
      }
    }
  }
</style>

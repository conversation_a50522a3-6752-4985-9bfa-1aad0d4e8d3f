.bids-collect-rule {
  .urlPanel {
    width: 420px;
    position: relative;
    border-radius: 6px;
    line-height: 24px;
    padding: 3px 35px 3px 10px;
    border: 1px solid #dcdcdc;
    word-break:break-all;
    .event-icon.copy-icon {
      position: absolute;
      top: 5px;
      right: 10px;
    }
  }
  .codemirror-wrap {
    .codemirror-wrap-common();
    width: 420px;
    .vue-codemirror {
      width: 420px;
      border: 1px solid #ebeef5;
      .CodeMirror {
        line-height: 16px;
        height: 250px;
      }
    }
  }
  
  .label-required {
    &::before {
      content: '*';
      color: #F81D22;
      margin-right: 4px;
    }
  }
  .el-select {
    width: @base-select-width;
  }
  .custom-input {
    width: 220px;
  }
  .custom-input-body {
    width: 370px;
  }
  .params-wrapper, .header-wrapper {
    width: 400px;
    display: flex;
    flex-wrap: wrap;
    .wrapper {
      display: flex;
      .input-wrapper {
        width: 370px;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
      }
      .btn-wrapper {
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
        .remove-btn {
          color: @color-danger;
        }
      }
    }
  }
}
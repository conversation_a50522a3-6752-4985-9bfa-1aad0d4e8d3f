<template>
  <fragment>
    <div class="page-header">
      <div class="filter">
        <el-form :model="form" :inline="true" size="small">
          <el-form-item label="所属应用">
            <!-- 默认选中有权限的列表的第一个应用 -->
            <el-select v-model="form.appKeys" placeholder="请选择" clearable filterable multiple collapse-tags class="custom-multi-select" @change="handleInput">
              <el-option v-for="item in appOptions" :key="item.appKey" :label="item.label" :value="item.appKey"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="配置名称">
            <el-input v-model="form.interfaceName" placeholder="请输入" clearable @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="配置编码">
            <el-input v-model="form.interfaceCode" placeholder="请输入" clearable @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="调度实例ID">
            <el-input v-model="form.scheduleId" placeholder="请输入" clearable @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="form.scheduleStatus" placeholder="请选择" clearable filterable @change="handleInput">
              <el-option v-for="item in scheduleStatusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <!-- 时间范围默认为最近2天 -->
            <el-date-picker v-model="form.time" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 370px;" @change="handleInput"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="fetchData">刷新</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="page-main">
      <div class="table">
        <el-table ref="table" v-loading="loading" key="subscribeTable" :data="listData" height="100%" :header-cell-style="{background:'#f5f7f9', height: '60px'}" style="width: 100%">
          <el-table-column type="index" :index="(pageIndex-1)*10+1" width="80"></el-table-column>
          <el-table-column prop="appName" label="所属应用" min-width="197"></el-table-column>
          <el-table-column prop="interfaceName" label="配置名称" min-width="197"></el-table-column>
          <el-table-column prop="interfaceCode" label="配置编码" min-width="197"></el-table-column>
          <el-table-column prop="scheduleId" label="调度实例ID" min-width="197"></el-table-column>
          <el-table-column prop="scheduleStatus" label="状态" min-width="197" :formatter="scheduleStatusType"></el-table-column>
          <el-table-column prop="scheduleTime" label="调度周期" min-width="197"></el-table-column>
          <el-table-column prop="beginTime" label="调度开始时间" min-width="197"></el-table-column>
          <el-table-column prop="endTime" label="调度结束时间" min-width="197"></el-table-column>
          <el-table-column fixed="right" label="操作" width="136">
            <template v-slot="{ row }">
              <el-button @click="handleShowTaskLog(row)" type="text" size="small">查看日志</el-button>
              <el-dropdown style="margin-left: 10px;" @command="(command) => handleCommand(command, row)">
                <span class="el-dropdown-link">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="stop" :disabled="row.scheduleStatus === 2">停止</el-dropdown-item>
                  <el-dropdown-item command="restart" :disabled="row['btnLoading']">重跑</el-dropdown-item>
                  <el-dropdown-item command="viewConfig" divided>查看任务配置</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </div>
    
    <bids-log-dialog ref="logDialog" :label="dialogLabel"></bids-log-dialog>
  </fragment>
</template>
<script>
import BidsLogDialog from 'components/bids-log-dialog'
import { Fragment } from 'vue-fragment'
import { pagination, filterInput, formatter } from 'mixins'
import { mapState } from 'vuex'
import { queryTotalInstance, restartJob } from 'services/common.service'
import { batchOpScheduleInstances } from 'services/simple.service'

export default {
  name: 'instance-list',
  mixins: [pagination, filterInput, formatter], // 分页，搜索栏，格式化表格内容
  components: {
    Fragment,
    BidsLogDialog
  },
  props: {
    appOptions: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState(['userInfo']) // 登录用户信息
  },
  data() { 
    return {
      dialogLabel: '',
      form: {
        appKeys: [],
        interfaceName: '',
        interfaceCode: '',
        scheduleId: '',
        scheduleStatus: '',
        time: this.getDefaultTimeRange() // 默认时间为最近两天
      },
      scheduleStatusOptions: [
        { value: 1, label: '运行中' },
        { value: 2, label: '失败' },
        { value: 3, label: '完成' }
      ],
      paginationType: 'remote', // 由服务器实现分页查询
      listData: [],
      loading: false
    } 
  },
  methods: {
    // 获取默认时间范围（最近两天）
    getDefaultTimeRange() {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 2) // 往前推2天
      start.setHours(0, 0, 0, 0) // 设置为当天的 00:00:00

      // 格式化为 yyyy-MM-dd HH:mm:ss 格式
      return [
        this.formatDateTime(start),
        this.formatDateTime(end)
      ]
    },
    // 格式化日期时间为 yyyy-MM-dd HH:mm:ss
    formatDateTime(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      const seconds = String(d.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    setData({ interfaceName, interfaceCode }) {
      this.form.interfaceName = interfaceName
      this.form.interfaceCode = interfaceCode
    },
    fetchData() {
      for (const key in this.form) {
        if (key === 'time') {
          continue
        }
        const val = this.form[key]
        if (val === '' || val.length === 0) {
          delete this.form[key]
        }
      }
      const paginator = { pageSize: this.pageSize, pageIndex: this.pageIndex }
      const content = { ...this.form, startTime: this.form.time[0], endTime: this.form.time[1], simpleMode: true }
      this.loading = true
      queryTotalInstance({ content, paginator }, false).then(res => {
        this.listData = (res.content || []).map(item => ({ btnLoading: false, ...item }))
        this.recordCount = res.paginator.recordCount
        this.loading = false
      }).catch(res => {
        this.$message.error('获取数据失败！原因：' + res.message || '暂无')
        this.loading = false
      })
    },
    handleExcute(row) {
      this.$message.error('无接口...')
    },
    handleShowTaskLog(row) {
      this.dialogLabel = '运行任务ID'
      this.$refs.logDialog.show({ jobName: row.scheduleJobName, jobInstId: row.scheduleId })
    },
    handleReset() {
      this.form = {
        appKeys: [],
        interfaceName: '',
        interfaceCode: '',
        creator: '',
        scheduleStatus: '',
        time: this.getDefaultTimeRange() // 重置时也使用默认时间范围
      }
      this.pageIndex = 1
      this.fetchData()
    },
    scheduleStatusType(row) {
      let type = row.scheduleStatus
      if (!type) return ''
      type = Number.parseInt(type)
      return this.scheduleStatusOptions.find(v => v.value === type).label
    },
    handleCommand(command, row) {
      switch (command) {
        case 'stop':
          this.handleOperate('停止', this.handleStop, row)
          break
        case 'restart':
          this.handleOperate('重跑', this.handleRestart, row)
          break
        case 'viewConfig':
          this.handleViewConfig(row)
          break
        default:
          break
      }
    },
    handleOperate(optType, optFunc, row) {
      this.$confirm('此操作将' + optType + '该任务实例，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        optFunc(row)
      }).catch(() => {})
    },
    handleRestart(row) {
      // 重跑
      const params = { jobInstId: row.scheduleId }
      const postData = { type: 'restart' }
      row.btnLoading = true
      restartJob(params, postData).then(res => {
        this.$message.success('操作成功！')
        row.btnLoading = false
        this.scheduleStatus = 1
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        row.btnLoading = false
      })
    },
    handleViewConfig(row) {
      this.$emit('switch', 'config', { interfaceName: row.interfaceName, interfaceCode: row.interfaceCode })
    },
    handleStop(row) {
      // {
      //    opType:"stop",//枚举值，stop,restart
      //    scheduleIds:["data_5342"]
      // }
      batchOpScheduleInstances({ opType: 'stop', scheduleIds: [row.scheduleId] }).then(res => {
        this.$message.success('操作成功！')
        row.scheduleStatus = 2
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
      })
    }
  }
}
</script>

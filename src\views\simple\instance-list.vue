<template>
  <fragment>
    <div class="page-header">
      <div class="filter">
        <el-form :model="form" :inline="true" size="small">
          <el-form-item label="所属应用">
            <!-- 默认选中有权限的列表的第一个应用 -->
            <el-select v-model="form.appKey" placeholder="请选择" clearable filterable multiple collapse-tags class="custom-multi-select" @change="handleAppChange">
              <el-option v-for="item in appOptions" :key="item.appKey" :label="item.label" :value="item.appKey"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="配置名称">
            <el-input v-model="form.name" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="配置编码">
            <el-input v-model="form.code" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="调度实例ID">
            <el-input v-model="form.creator" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="form.status" placeholder="请选择" clearable filterable>
              <el-option label="1" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <!-- 时间范围默认为最近2天 -->
            <el-date-picker v-model="form.time" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 370px;"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button>重置</el-button>
          </el-form-item>
          <el-form-item>
            <el-button>刷新</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="page-main">
      <div class="table">
        <el-table ref="table" v-loading="loading" key="subscribeTable" :data="tableData" height="100%" :header-cell-style="{background:'#f5f7f9', height: '60px'}" style="width: 100%">
          <el-table-column type="index" :index="(pageIndex-1)*10+1" width="50"></el-table-column>
          <el-table-column prop="appCode" label="所属应用" min-width="197"></el-table-column>
          <el-table-column prop="name" label="配置名称" min-width="197"></el-table-column>
          <el-table-column prop="code" label="配置编码" min-width="197"></el-table-column>
          <el-table-column prop="code" label="调度实例ID" min-width="197"></el-table-column>
          <el-table-column prop="code" label="状态" min-width="197"></el-table-column>
          <el-table-column prop="code" label="调度周期" min-width="197"></el-table-column>
          <el-table-column prop="code" label="调度开始时间" min-width="197"></el-table-column>
          <el-table-column prop="code" label="调度结束时间" min-width="197"></el-table-column>
          <el-table-column fixed="right" label="操作" width="186">
            <template v-slot="{ row }">
              <el-button @click="handleExcute(row)" type="text" size="small">立即执行</el-button>
              <el-button @click="handleViewLog(row)" type="text" size="small">查看日志</el-button>
              <el-dropdown style="margin-left: 10px;">
                <span class="el-dropdown-link">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>停止</el-dropdown-item>
                  <el-dropdown-item>重跑</el-dropdown-item>
                  <el-dropdown-item divided>查看任务配置</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </div>
  </fragment>
</template>
<script>
import { Fragment } from 'vue-fragment'
import { pagination, filterInput, formatter } from 'mixins'
import { mapState } from 'vuex'

export default {
  name: 'instance-list',
  mixins: [pagination, filterInput, formatter], // 分页，搜索栏，格式化表格内容
  components: {
    Fragment
  },
  props: {
    appOptions: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState(['userInfo']) // 登录用户信息
  },
  data() { 
    return {
      form: {},
      paginationType: 'remote', // 由服务器实现分页查询
      tableData: [
        { code: 1 }
      ], // 数据订阅表格数据
      recordCount: 0,
      loading: false
    } 
  },
  methods: {
    fetchData() {
      
    },
    handleExcute(row) {
      
    },
    handleViewLog(row) {
      
    }
  }
}
</script>

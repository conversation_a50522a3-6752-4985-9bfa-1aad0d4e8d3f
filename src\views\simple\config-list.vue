<template>
  <fragment>
    <div class="page-header">
      <div class="filter">
        <el-form :model="form" :inline="true" size="small">
          <el-form-item label="所属应用">
            <el-select v-model="form.appKey" placeholder="请选择" clearable filterable multiple collapse-tags class="custom-multi-select" @change="handleAppChange">
              <el-option v-for="item in appOptions" :key="item.appKey" :label="item.label" :value="item.appKey"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="配置名称">
            <el-input v-model="form.interfaceName" placeholder="请输入" clearable @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="配置编码">
            <el-input v-model="form.interfaceCode" placeholder="请输入" clearable @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.createUser" placeholder="请输入" clearable @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="在线状态">
            <el-select v-model="form.jobStatus" placeholder="请选择" clearable filterable multiple collapse-tags class="custom-multi-select" @change="handleInput">
              <el-option label="草稿" :value="0"></el-option>
              <el-option label="运行中" :value="1"></el-option>
              <el-option label="下线" :value="-1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker v-model="form.createTimeRange" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 370px;" @change="handleTimeRangeChange"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="page-main">
      <div class="table">
        <el-table ref="table" v-loading="loading" key="publishTable" :data="tableData" height="100%" :header-cell-style="{background:'#f5f7f9', height: '60px'}" style="width: 100%">
          <el-table-column type="index" :index="(pageIndex-1)*10+1" width="80"></el-table-column>
          <el-table-column prop="appName" label="所属应用" min-width="197"></el-table-column>
          <el-table-column prop="interfaceName" label="配置名称" min-width="197">
            <template v-slot="{ row }">
              <!-- 查看任务配置 -->
              <el-button type="text" @click="handleView(row)">{{ row.interfaceName }}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="interfaceCode" label="配置编码" min-width="197"></el-table-column>
          <el-table-column prop="interfaceDesc" label="描述" min-width="197"></el-table-column>
          <el-table-column prop="jobVersion" label="发布版本" min-width="197" :formatter="formatJobVersion"></el-table-column>
          <el-table-column prop="jobStatus" label="在线状态" min-width="197">
            <template v-slot="{ row }">
              <span class="bids-blue-dot">{{ getJobStatus(row.jobStatus) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="creatorName" label="创建人" min-width="197">
            <template v-slot="{ row }">
              <BidsUserName :fullName="row.creatorName"></BidsUserName>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="197" :formatter="(row) => formatTime(row.createTime)"></el-table-column>
          <el-table-column prop="lastModifiedTime" label="更新时间" min-width="197" :formatter="(row) => formatTime(row.lastModifiedTime)"></el-table-column>
          <el-table-column fixed="right" label="操作" width="176">
            <template v-slot="{ row }">
              <el-button @click="handleEdit(row)" type="text" size="small">编辑</el-button>
              <!-- 查看实例：点击跳转至任务实例页面，自动带入配置编码查询对应实例 -->
              <el-button @click="handleViewInstance(row)" type="text" size="small">查看实例</el-button>
              <!-- 已下线：仅已下线的任务支持删除和上线,已上线：已上线的任务支持下线，不支持删除,待发布：支持上线，删除 -->
              <el-dropdown style="margin-left: 10px;" @command="(command) => handleCommand(command, row)">
                <span class="el-dropdown-link">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="online" :disabled="row.jobStatus === 1">上线</el-dropdown-item>
                  <el-dropdown-item command="offline" :disabled="row.jobStatus !== 1">下线</el-dropdown-item>
                  <el-dropdown-item command="rollback">回滚</el-dropdown-item>
                  <el-dropdown-item command="run" divided>运行配置</el-dropdown-item>
                  <el-dropdown-item command="version">版本记录</el-dropdown-item>
                  <el-dropdown-item command="delete" divided :disabled="row.jobStatus !== -1">删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </div>
    <RunConfigDialog ref="runConfigDialog" @update="fetchData"></RunConfigDialog>
    <ConfigVersionDialog ref="configVersionDialog" @update="fetchData"></ConfigVersionDialog>
  </fragment>
</template>
<script>
import { Fragment } from 'vue-fragment'
import { pagination, filterInput, formatter } from 'mixins'
import { mapState } from 'vuex'
import { BidsUserName } from 'components'
import RunConfigDialog from './components/run-config-dialog.vue'
import ConfigVersionDialog from './components/config-version-dialog.vue'
import { querySimpleConfigList, releaseSimpleConfig, rollbackSimpleConfig, deleteSimpleConfig } from 'services/simple.service'
import { isEmptyValue } from 'utils/shared'

export default {
  name: 'config-list',
  mixins: [pagination, filterInput, formatter], // 分页，搜索栏，格式化表格内容
  components: {
    Fragment,
    BidsUserName,
    RunConfigDialog,
    ConfigVersionDialog
  },
  props: {
    appOptions: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState(['userInfo']) // 登录用户信息
  },
  data() {
    return {
      form: {
        appKey: [],
        jobStatus: [],
        createTimeRange: []
      },
      loading: false,
      paginationType: 'remote', // 由服务器实现分页查询
      tableData: []
    } 
  },
  methods: {
    fetchData() {
      const paginator = { pageSize: this.pageSize, pageIndex: this.pageIndex } // 分页查询的参数
      const postData = { paginator }
      for (const key in this.form) {
        const value = this.form[key]
        if (isEmptyValue(value) || value.length === 0) {
          continue
        }
        postData[key] = value
      }
      this.loading = true
      querySimpleConfigList(postData).then(res => {
        res = res || { paginator: {} }
        this.tableData = res.content || []
        this.recordCount = res.paginator.recordCount || 0
        this.$refs.table.doLayout()
        this.loading = false
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('获取数据发布失败！原因：' + res.message || '暂无')
        this.loading = false
      })
    },
    handleView(row) {
      this.$router.push({ name: 'Config', query: { configId: row.configId, mode: 'view' } })
    },
    handleEdit(row) {
      this.$router.push({ name: 'Config', query: { configId: row.configId, mode: 'edit' } })
    },
    handleViewInstance() {
      this.$emit('switch', 'instance')
    },
    handleCommand(command, row) {
      switch (command) {
        case 'online':
          this.handleOnline(row)
          break
        case 'offline':
          this.handleOffline(row)
          break
        case 'rollback':
          this.handleRollback(row)
          break
        case 'run':
          this.handleRun(row)
          break
        case 'version':
          this.handleVersion(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
        default:
          break
      }
    },
    handleDelete(row) {
      this.$confirm(`删除后对应任务数据无法恢复，请谨慎操作。`, `确定删除任务配置「${row.interfaceName}」吗？`, { type: 'warning' }).then(() => {
        deleteSimpleConfig({ id: row.configId }).then(res => {
          this.$message.success('操作成功！')
          this.fetchData()
        }).catch(res => {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
      }).catch(() => {})
    },
    handleOnline(row) {
      this.fetchRelease(row, 'ONLINE')
    },
    handleOffline(row) {
      this.fetchRelease(row, 'OFFLINE')
    },
    fetchRelease(row, releaseState) {
      releaseSimpleConfig({ id: row.configId, releaseState }).then(res => {
        this.$message.success('操作成功！')
        this.fetchData()
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
      })
    },
    handleRollback(row) {
      this.$confirm(`确定回滚「${row.interfaceName}」到「V${row.jobVersion - 1}版本」吗？`, '提示', { type: 'warning' }).then(() => {
        rollbackSimpleConfig({ id: row.configId, version: row.jobVersion - 1 }).then(res => {
          this.$message.success('操作成功！')
          this.fetchData()
        }).catch(res => {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
      }).catch(() => {})
    },
    handleRun(row) {
      this.$refs.runConfigDialog.show(row)
    },
    handleVersion(row) {
      this.$refs.configVersionDialog.show(row)
    },
    handleTimeRangeChange(val) {
      this.form.createTimeRange = val?.map(item => item.toISOString())
      this.handleAppChange()
    },
    handleReset() {
      this.form = {
        appKey: [],
        jobStatus: [],
        createTimeRange: []
      }
      this.handleAppChange()
    },
    formatJobVersion(row) {
      return `V${row.jobVersion}`
    },
    formatTime(utcDateString) {
      const date = new Date(utcDateString)
      return date.toLocaleString()
    },
    getJobStatus(status) {
      return {
        '0': '草稿',
        '1': '运行中',
        '-1': '下线'
      }[status]
    }
  }
}
</script>

<style lang="less">
.custom-multi-select {
  /* 设置选项列表的样式 */
  .el-select-dropdown__item {
    white-space: normal;  /* 允许换行 */
    word-break: break-all; /* 长单词换行 */
    line-height: 1.5;      /* 行高调整 */
  }

  /* 多选标签的样式 */
  .el-tag {
    max-width: 110px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>

/*
 * @Description: 分页查询的属性和方法，分在服务器实现分页查询和一次性拉取数据在前端进行分页展示两种情况
 * @Autor: kenoxia
 * @LastEditTime: 2020-12-31 19:29:36
 */
export default {
  data() {
    return {
      pageIndex: 1,
      pageSize: 10,
      recordCount: 0,
      paginationType: 'local' // remote/local
    }
  },
  watch: {
    'data.length'() {
      this.handleCurrentChange(this.pageIndex)
    }
  },
  mounted() {
    if (this.paginationType !== 'remote') {
      this.$nextTick(() => {
        this.handleCurrentChange(this.pageIndex)
      })
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val
      if (this.paginationType === 'remote') {
        this.fetchData()
      } else {
        if (Math.ceil(this.data.length / val) >= this.pageIndex) {
          this.handleCurrentChange(this.pageIndex)
        }
      }
    },
    handleCurrentChange(val) {
      this.pageIndex = val
      if (this.paginationType === 'remote') {
        this.fetchData()
      } else {
        const begin = (val - 1) * this.pageSize
        const end = Math.min(this.data.length, val * this.pageSize)
        this.tableData = this.data.slice(begin, end)
      }
    }
  }
}

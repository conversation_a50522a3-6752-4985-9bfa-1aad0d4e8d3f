<template>
  <div class="encryption-key-dialog">
    <el-dialog :title="title" :visible.sync="visible" @closed="handleClose" width="600px">
      <div class="content">
        <div v-if="configId">
          <div class="filter">
            <el-input size="medium" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          </div>
          <div class="tree-area">
            <el-tree class="tree" ref="tree" :data="keyList" :props="defaultProps" :filter-node-method="filterNode" @node-click="handleNodeClick">
            </el-tree>
          </div>
        </div>
        <div v-else>
          <el-form ref="form" :model="form" label-width="55px" label-position="left" size="medium" @submit.native.prevent>
            <el-form-item :label="encryptionWay === 'RSA' ? '公钥' : '密钥'">
              <el-input v-model="form.encryptionPublicKey" :placeholder=" `请输入${encryptionWay === 'RSA' ? '公钥' : '密钥'}，如为空，默认后端生成`"></el-input>
            </el-form-item>
            <el-form-item label="私钥" v-if="encryptionWay === 'RSA'">
              <el-input v-model="form.encryptionPrivateKey" placeholder="请输入私钥，如为空，默认后端生成"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" @click="visible = false">取消</el-button>
        <el-button size="medium" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { querySecretKeysList, reqEncryptionKey } from 'services/common.service'
export default {
  name: 'encryption-key-dialog',
  props: {
    encryptionWay: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      filterText: '',
      keyList: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      configId: '',
      encryptionPublicKey: '',
      form: {}
    }
  },
  watch: {
    filterText(val) {
      this.$refs['tree'].filter(val)
    }
  },
  computed: {
    title() {
      return this.configId ? '选择密钥key' : '密钥明文'
    }
  },
  methods: {
    show(configId) {
      this.visible = true
      this.configId = configId
      this.getData()
    },
    getData() {
      const params = { configId: this.configId }
      querySecretKeysList(params).then(res => {
        this.keyList = res.map(item => ({ label: item }))
      }).catch(err => {
        this.$message.error('操作失败！原因：' + err.message || '暂无')
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleClose() {
      Object.assign(this._data, this.$options.data())
    },
    handleNodeClick(data) {
      this.encryptionPublicKey = data.label
    },
    handleConfirm() {
      if (this.configId) {
        if (!this.encryptionPublicKey) {
          this.$message.error('未选择密钥key')
          return
        }
        this.$emit('handleChoose', this.encryptionPublicKey)
        this.handleClose()
      } else {
        if (this.encryptionWay === 'RSA') {
          const values = Object.values(this.form)
          if (values.length === 1 && values[0]) {
            this.$message.error('公钥，私钥不能只填其一')
            return
          }
        }
        const params = { ...this.form }
        const encryptionWay = this.encryptionWay
        reqEncryptionKey(params, encryptionWay).then(res => {
          const { encryptionPublicKey, encryptionPrivateKey } = res
          this.$emit('handleChoose', encryptionPublicKey, encryptionPrivateKey)
          this.handleClose()
        }).catch(err => {
          this.$message.error('操作失败！原因：' + err.message || '暂无')
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.encryption-key-dialog {
  .content {
    .filter {
      margin-bottom: 20px;
    }
    .tree {
      height: 300px;
      overflow-y: scroll;
    }
  }
}
</style>

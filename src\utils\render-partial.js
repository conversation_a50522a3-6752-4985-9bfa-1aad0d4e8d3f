/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-23 14:54:04
 */
/**
 * 按需加载
 */
import { render } from 'sdc-vue'
import { ele, sdc, echarts } from 'plugins'
// 应用配置
import App from 'views/app'
import router from '../router'
import store from '../store'
import env from '../config/env.conf'
import i18n from '../locales/i18n'
// 插件配置
import 'plugins/svg/'
import VueCodemirror from 'vue-codemirror'
import 'codemirror/lib/codemirror.css'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
require('codemirror/addon/edit/matchbrackets')
require('codemirror/addon/selection/active-line')
require('codemirror/mode/sql/sql')
require('codemirror/mode/javascript/javascript')
require('codemirror/addon/hint/show-hint')
require('codemirror/addon/hint/sql-hint')
require('codemirror/addon/hint/javascript-hint')

export default render(App, {
  router,
  store,
  env,
  i18n,
  plugins: [ele, sdc],
  theme: 'skyblue',
  lazy: {
    preLoad: 1,
    loading: require('assets/img/loading/loading-bubbles.svg')
  },
  init: Vue => {
    // 初始化...
    Vue.prototype.$echarts = echarts
    Vue.use(VXETable)
    Vue.use(VueCodemirror, {
      options: {
        tabSize: 4,
        mode: 'text/x-sql',
        indentWithTabs: true,
        smartIndent: true,
        lineNumbers: true,
        matchBrackets: true,
        cursorHeight: 1,
        line: true,
        hintOptions: {
          completeSingle: false
        }
      }
    })
  }
})

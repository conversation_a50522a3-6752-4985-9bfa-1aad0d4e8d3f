<template>
  <el-form :model="form" :rules="rules" :label-width="labelWidth" :disabled="['read', 'edit'].includes(pageStatus)">
    <el-form-item label="采集的库名" prop="database">
      <el-select size="medium" v-model="form.database" placeholder="请选择">
        <el-option v-for="(item, index) in databaseOptions" :key="index" :label="item.name" :value="item.id"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="采集的操作" prop="collectOpts">
      <el-checkbox-group v-model="form.collectOpts">
        <el-checkbox label="insert">插入</el-checkbox>
        <el-checkbox label="update">更新</el-checkbox>
        <el-checkbox label="delete">删除</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'binlog-form-part',
  props: {
    labelWidth: String,
    pageStatus: String
  },
  data() {
    return {
      form: { collectOpts: [] },
      rules: {
        database: [{ required: true, message: '请选择采集的库名', trigger: 'change' }],
        collectOpts: [{ type: 'array', required: true, message: '请选择采集的操作', trigger: 'change' }]
      },
      databaseOptions: []
    }
  }
}
</script>
<style lang="less" scoped>
  @import "~assets/css/bids-collect-rule.less";
</style>

<!--
 * @Description: 导入JSON弹窗，解析JSON串得到数据的字段
 * @Autor: kenoxia
 * @LastEditTime: 2020-12-31 19:16:57
-->
<template>
  <div class="import-json-dialog">
    <el-dialog title="导入JSON" :visible.sync="visible" :before-close="handleClose" width="500px">
      <el-tabs v-model="importJsonType" @tab-click="handleTabClick">
        <el-tab-pane label="JSON" name="JSON">
          <el-input type="textarea" v-model="jsonData" resize="none" :rows="10" @input="handleInput"/>
        </el-tab-pane>
        <el-tab-pane label="JSON-SCHEMA" name="Jschema">
          <el-input type="textarea" v-model="jsonSchemaData" resize="none" :rows="10" @input="handleInput"/>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <i class="el-icon-error" v-if="formatError" style="color:#f81d22;float:left;"/>
        <el-button @click="visible = false" size="medium">取消</el-button>
        <el-button size="medium" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
const GenerateSchema = require('generate-schema/src/schemas/json.js')

export default {
  name: 'import-json-dialog',
  data() {
    return {
      importJsonType: 'JSON',
      visible: false,
      jsonData: '',
      jsonSchemaData: '',
      formatError: false,
      curData: null
    }
  },
  methods: {
    show() {
      this.visible = true
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    handleInput(text) {
      if (text === '') {
        this.formatError = false
        this.curData = null
        return
      }
      try {
        const obj = JSON.parse(text)
        this.formatError = false
        this.curData = obj
      } catch (e) {
        this.formatError = true
        this.curData = null
      }
    },
    handleTabClick(tab) {
      const text = tab.name === 'JSON' ? this.jsonData : this.jsonSchemaData
      this.handleInput(text)
    },
    handleConfirm() {
      if (!this.curData) {
        this.$message.error('JSON格式有误！')
        return
      }
      if (this.importJsonType === 'JSON') {
        this.curData = GenerateSchema(this.curData)
      }
      this.$emit('confirm', this.curData)
      this.visible = false
    }
  }
  
}
</script>

<!--
 * @Description: 构建数据发布页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-15 15:42:48
-->
<template>
  <div class="publish-build">
    <div class="page-header">
     <el-page-header @back="goBack" :content="pageStatus === 'clone&build' ? '构造待发布的数据（克隆版本）' : '构造待发布的数据'"></el-page-header>
    </div>
    <el-tabs class="config-tabs" v-model="tabValue" type="card" tab-position="right" @tab-click="handleTabClick">
      <el-tab-pane
        :key="item.name"
        v-for="item in currentTabs"
        :label="item.label"
        :name="item.name"
        :lazy="true">
      </el-tab-pane>
      <div class="config-box">
        <bids-base-info ref="baseInfo" :page-status="pageStatus" v-show="tabValue === 'baseInfo'"></bids-base-info>
        <bids-collect-rule ref="dataInput" :page-status="pageStatus" v-show="tabValue === 'dataInput'"></bids-collect-rule>
        <bids-publish-output ref="dataOutput" :page-status="pageStatus" v-show="tabValue === 'dataOutput'"></bids-publish-output>
        <bids-other-setting ref="setting" :page-status="pageStatus" configType="publish" v-show="tabValue === 'setting'"></bids-other-setting>
      </div>
    </el-tabs>
    <div class="config-footer">
      <bids-global-tips-bar></bids-global-tips-bar>
      <div class="buttons">
        <el-button type="primary" size="medium" @click="handleNext" v-show="tabValue !== 'setting'">下一步</el-button>
        <el-button type="primary" size="medium" @click="handleSave" v-if="currentTabs.find(item => item.name === 'setting')">保存</el-button>  
      </div>
    </div>
  </div>  
</template>

<script>
import { BidsBaseInfo, BidsCollectRule, BidsPublishOutput, BidsOtherSetting, BidsGlobalTipsBar } from 'components'
import { mapState, mapMutations } from 'vuex'
import { queryData, queryDrafts, saveDrafts, updateDrafts } from 'services/publish.service'
import { emitter } from 'mixins'

export default {
  components: {
    BidsBaseInfo,
    BidsCollectRule,
    BidsPublishOutput,
    BidsOtherSetting,
    BidsGlobalTipsBar
  },
  mixins: [emitter],
  computed: mapState({
    configData: state => state.currentPublishConfig
  }),
  data() {
    return {
      saved: false,
      pageStatus: 'build',
      currentTabs: [
        { name: 'baseInfo', label: '基本信息' }
      ],
      addableTabs: [
        { name: 'baseInfo', label: '基本信息' },
        { name: 'dataInput', label: '数据采集' },
        { name: 'dataOutput', label: '数据输出' },
        { name: 'setting', label: '发布配置' }
      ],
      tabValue: 'baseInfo'
    }
  },
  created() {
    this.INIT_PUSHLISH_CONFIG()
    if (this.$route.query.configId) {
      this.fetchData()
    }
  },
  beforeRouteLeave(to, from, next) {
    if (this.saved) {
      next()
      return
    }
    this.$confirm('离开后不会保存当前数据，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      next()
    }).catch(() => {
      next(false)
    })
  },
  methods: {
    ...mapMutations(['INIT_PUSHLISH_CONFIG', 'SET_PUSHLISH_CONFIG']),
    goBack() {
      this.$router.back()
    },
    fetchData() {
      this.currentTabs = this.addableTabs
      if (this.$route.query.mode === 'cloning') {
        // 针对点击克隆跳转到该页面的情况，此时应查询的是生效版本的配置信息
        this.pageStatus = 'clone&build'
        const params = { configId: this.$route.query.configId, version: this.$route.query.version }
        queryData(params).then(res => {
          res[0] && this.SET_PUSHLISH_CONFIG(res[0])
          this.currentTabs.forEach(tab => {
            this.$refs[tab.name].updatePageData()
          })
        }).catch(res => {
          this.$message.error('获取该版本失败！原因：' + res.message || '暂无')
        })
      } else {
        // 从草稿页面点击编辑进来，查询该草稿的配置
        const postData = [{ configId: this.$route.query.configId, version: 'v0' }]
        queryDrafts(postData).then(res => {
          res[0] && this.SET_PUSHLISH_CONFIG(res[0])
          res[0] && res[0].latestVersion && (this.pageStatus = 'clone&build')
          this.currentTabs.forEach(tab => {
            this.$refs[tab.name].updatePageData()
          })
        }).catch(res => {
          this.$message.error('获取该草稿失败！原因：' + res.message || '暂无')
        })
      }
    },
    handleNext() {
      if (!this.beforeLeave(null, this.tabValue)) {
        return
      }
      const index = this.currentTabs.findIndex(item => item.name === this.tabValue)
      if (index === this.currentTabs.length - 1) {
        this.currentTabs.push(this.addableTabs[index + 1])
      }
      this.tabValue = this.currentTabs[index + 1].name
      this.$refs[this.tabValue].updatePageData()
    },
    beforeLeave(activeName, oldActiveName) {
      if (this.$refs[oldActiveName] && !this.$refs[oldActiveName].formValidate()) {
        this.$message.error('请按要求填写完整表单项！')
        return false
      }
      return true
    },
    handleTabClick(tab) {
      this.$refs[tab.name].updatePageData()
    },
    handleSave() {
      let valid = true
      this.addableTabs.forEach(tab => {
        valid = valid && this.$refs[tab.name].formValidate()
      })
      if (!valid) {
        this.$message.error('请按要求填写完整表单项！')
        return
      }
      const { cache, ...postData } = this.configData
      const params = { configId: this.configData.configId, version: this.configData.version }
      const operateDrafts = params.configId && this.$route.query.mode !== 'cloning' ? updateDrafts : saveDrafts
      operateDrafts(params, postData).then(res => {
        this.saved = true
        this.$message.success('保存成功！')
        setTimeout(() => { 
          this.$router.push({ name: 'PublishAdd' })
        }, 100)
      }).catch(res => {
        this.saved = false
        this.$message.error('保存失败！原因：' + res.message || '暂无')
      })
    }
  }
}
</script>

<style lang="less">
 @import "~assets/css/publish-build.less";
</style>

<!--
 * @Description: crontab组件，以UI界面形式选择调度周期并生成对应的cron表达式
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-30 17:44:42
-->
<template>
  <div class="bids-crontab">
    周期类型：
    <el-radio-group v-model="scheduleUnit" size="medium" @change="handleTypeChange">
      <el-radio-button v-for="(item, index) in radios" :key="index" :label="item.label">{{item.text}}</el-radio-button>
    </el-radio-group>
    <div class="content">
      <span>{{prependText}}</span>
      <span v-if="scheduleUnit === 'Week'">
        <el-select size="small" v-model="weekNum" class="el_select--multi" placeholder="周几" multiple collapse-tags @change="handleValueChange(weekNum, 5)">
          <el-option v-for="item in weekOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </span>
      <span v-if="scheduleUnit === 'Month'">
        <el-select size="small" v-model="dayNum" class="el_select--multi" placeholder="几号" multiple collapse-tags @change="handleValueChange(dayNum, 3)">
          <el-option v-for="item in MonthOptions" :key="item" :value="item" :label="item+'号'"></el-option>
        </el-select>
      </span>
      <span v-if="scheduleUnit !== 'Hour' && scheduleUnit !== 'Min'">
        <el-select size="small" v-model="hourNum" placeholder="几点" class="el_select--multi" multiple collapse-tags @change="handleValueChange(hourNum, 2)">
          <el-option v-for="item in hourOptions" :key="item" :value="item" :label="item+'点'"></el-option>
        </el-select>
      </span>
      <span v-if="scheduleUnit !== 'Min'">
        <el-select size="small" v-model="minuteNum" placeholder="几分" @change="handleValueChange(minuteNum, 1)">
          <el-option v-for="item in minuteOptions" :key="item" :value="item" :label="item+'分'"></el-option>
        </el-select>
      </span>
      <span v-if="scheduleUnit === 'Min'">
        <el-select size="small" v-model="startMinuteTime" placeholder="几分" @change="handleValueChange(startMinuteTime, 1)">
          <el-option v-for="item in startMinuteOptions" :key="item" :value="item" :label="item+'分'"></el-option>
        </el-select>
        <span style="margin:0 10px;">开始，每隔</span>
        <el-select size="small" v-model="stepMinuteNum" placeholder="几分钟" class="el_select--multi" @change="handleValueChange(stepMinuteNum, 1)">
          <el-option v-for="item in stepMinuteOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </span>
      <span>{{appendText}}</span>
    </div>
     <el-divider>当前cron表达式</el-divider>
     <div class="cron-area">
      <span v-for="(item, index) in cronArray" :key="index" class="cron-item">
        <div>{{cronLabels[index]}}</div>
        <el-tooltip :content="cronArray[index].toString()" :disabled="cronArray[index].toString().length < 6" placement="top">
          <div class="cron-text">{{cronArray[index]}}</div>
        </el-tooltip>
        <!-- <el-input v-model="cronArray[index]"></el-input> -->
      </span>
     </div>
     <div class="btns-footer">
      <el-button size="small" type="primary" @click="handleConfirm">确定</el-button>
      <el-button size="small" @click="handleCancel">取消</el-button>
     </div>
  </div>
</template>

<script>
import { DataType } from 'sdc-core'

export default {
  name: 'bids-crontab',
  props: {
    unit: String,
    expression: String
  },
  computed: {
    prependText() {
      if (!this.scheduleUnit) return ''
      if (this.scheduleUnit !== 'Min') {
        return '每' + this.radios.find(item => item.label === this.scheduleUnit).text
      } else {
        return '从'
      }
    }
  },
  data() {
    return {
      scheduleUnit: '',
      radios: [ { text: '分钟', label: 'Min' }, { text: '小时', label: 'Hour' },
        { text: '天', label: 'Day' }, { text: '周', label: 'Week' }, { text: '月', label: 'Month' }
      ],
      minuteNum: '0',
      startMinuteTime: '0',
      stepMinuteNum: '*', // 分钟周期下的选择框值
      hourNum: [0],
      weekNum: [],
      dayNum: [],
      startMinuteOptions: [...Array(29 - 0 + 1)].map((e, i) => 0 + i + ''),
      minuteOptions: [...Array(59 - 0 + 1)].map((e, i) => 0 + i + ''), // 使用string
      hourOptions: [...Array(23 - 0 + 1)].map((e, i) => 0 + i + ''),
      weekOptions: [{ label: '周一', value: 'MON' }, { label: '周二', value: 'TUE' }, { label: '周三', value: 'WED' }, 
        { label: '周四', value: 'THU' }, { label: '周五', value: 'FRI' }, { label: '周六', value: 'SAT' }, { label: '周日', value: 'SUN' }
      ],
      MonthOptions: [...Array(28 - 1 + 1)].map((e, i) => 1 + i + ''),
      cronArray: ['0', '0', '0', '*', '*', '?', '*'], // 当前值 [秒 分 时 日 月 周 年]
      stepMinuteOptions: [
        { label: '5分钟', value: '5' }, { label: '10分钟', value: '10' }, 
        { label: '20分钟', value: '20' }, { label: '30分钟', value: '30' }
      ],
      cronLabels: ['秒', '分', '时', '日', '月', '周', '年'],
      appendText: '执行一次',
      cronByType: { // 各周期类型预设值
        Min: ['0', '0/5', '*', '*', '*', '?', '*'],
        Hour: ['0', '0', '*', '*', '*', '?', '*'],
        Day: ['0', '0', '0', '*', '*', '?', '*'],
        Week: ['0', '0', '0', '?', '*', 'MON', '*'],
        Month: ['0', '0', '0', '1', '*', '?', '*']
      }
    }
  },
  methods: {
    handleValueChange(value, index) {
      if (!DataType.isArray(value)) {
        if (index === 1 && this.scheduleUnit === 'Min') {
          this.cronArray[index] = `${this.startMinuteTime}/${this.stepMinuteNum}`
        } else {
          this.cronArray[index] = value
        }
      } else {
        this.cronArray[index] = value.join(',')
      }
    },
    handleTypeChange(val) {
      this.cronArray = this.cronByType[val]
      this.setTimeNum(this.cronArray, val)
    },
    setTimeNum(cronArray, unit) {
      if (unit === 'Min') {
        this.startMinuteTime = cronArray[1].split('/')[0]
        this.stepMinuteNum = cronArray[1].split('/')[1]
      } else {
        this.minuteNum = cronArray[1]
      }
      this.hourNum = cronArray[2].split(',').filter(item => item !== '')
      this.dayNum = cronArray[3] === '*' ? [1] : cronArray[3].split(',').filter(item => item !== '')
      this.weekNum = cronArray[5] === '?' ? ['MON'] : cronArray[5].split(',').filter(item => item !== '')
    },
    handleConfirm() {
      const cron = this.cronArray.join(' ')
      const obj = { scheduleAtCron: cron, scheduleUnit: this.scheduleUnit }
      this.$emit('input', cron)
      this.$emit('confirm', obj)
    },
    handleCancel() {
      this.$emit('cancel')
    },
    setCronTab(unit, expression) {
      if (unit === this.scheduleUnit && expression === this.cronArray.join(' ')) return
      this.scheduleUnit = unit || 'Day'
      if (expression) {
        this.cronByType[this.scheduleUnit] = expression.split(' ')
      }
      this.cronArray = this.cronByType[this.scheduleUnit]
      this.setTimeNum(this.cronArray, this.scheduleUnit)
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/bids-crontab.less";
</style>

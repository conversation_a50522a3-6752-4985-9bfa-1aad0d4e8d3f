<!--
 * @Description: 查看数据订阅页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-15 15:42:35
-->
<template>
  <div class="subscribe-view">
    <div class="page-header">
     <el-page-header @back="goBack" content="查看数据订阅"></el-page-header>
    </div>
    <div>
      <el-tabs class="version-tabs" v-model="currentVersion" type="card" @tab-click="handleVersionChange">
        <el-tab-pane v-for="item in data" :key="item.version" :name="item.version">
          <span slot="label" class="verson-tab-label">
            <span class="label-text">{{item.version}}</span>
            <i style="color:#0ad0b6;" class="el-icon-success" v-if="item.jobStatus === 1"></i>
            <i style="color:#ACACAC;" class="el-icon-remove" v-else></i>
          </span>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="version-info-panel">
      <div class="info-row">
        <span class="text-item">
          <span>当前版本：</span>
          <span>{{currentData.version}}</span>
        </span>
        <span class="text-item">
          <span>当前状态：</span>
          <span><el-tag v-if="currentData.version" effect="dark" size="small" :type="currentData.jobStatus === 1 ? 'success' : 'info'">{{currentData.jobStatus === 1 ? "运行中" : "已下线"}}</el-tag></span>
        </span>
        <span class="text-item">
          <span>初版创建人：</span>
          <span>{{currentData.creatorName}}</span>
        </span>
        <span class="text-item">
          <span>最近更新时间：</span>
          <span>{{currentData.lastModifiedTime}}</span>
        </span>
      </div>
    </div>
    <el-tabs class="config-tabs" v-model="tabValue" type="card" tab-position="right" @tab-click="handleConfigTabClick">
      <el-tab-pane
        :key="item.name"
        v-for="item in currentTabs"
        :label="item.label"
        :name="item.name"
        :lazy="true">
      </el-tab-pane>
      <div class="config-box">
        <bids-base-info ref="baseInfo" :page-status="pageStatus" configType="subscribe" v-show="tabValue === 'baseInfo'"></bids-base-info>
        <bids-subscribe-rule ref="subscribeRule" :page-status="pageStatus" v-show="tabValue === 'subscribeRule'"></bids-subscribe-rule>
        <bids-subscribe-output ref="subscribeOutput" :page-status="pageStatus" v-show="tabValue === 'subscribeOutput'"></bids-subscribe-output>
        <bids-before-after-event ref="beforeAfterEvent" :page-status="pageStatus" v-show="tabValue === 'beforeAfterEvent'"></bids-before-after-event>
        <bids-other-setting ref="setting" :page-status="pageStatus" configType="subscribe" v-show="tabValue === 'setting'"></bids-other-setting>
      </div>
    </el-tabs>
  </div>
</template>
<script>
import { BidsBaseInfo, BidsSubscribeRule, BidsSubscribeOutput, BidsBeforeAfterEvent, BidsOtherSetting } from 'components'
import { queryData } from 'services/subscribe.service'
import { mapState, mapMutations } from 'vuex'

export default {
  components: {
    BidsBaseInfo,
    BidsSubscribeRule,
    BidsSubscribeOutput,
    BidsBeforeAfterEvent,
    BidsOtherSetting
  },
  computed: mapState({
    configData: state => state.currentSubscribeConfig
  }),
  data() {
    return {
      pageStatus: 'read',
      data: [],
      tabValue: 'baseInfo',
      currentTabs: [
        { name: 'baseInfo', label: '基本信息' },
        { name: 'subscribeRule', label: '订阅规则' },
        { name: 'subscribeOutput', label: '数据输出' },
        { name: 'beforeAfterEvent', label: '前置后置' },
        { name: 'setting', label: '订阅配置' }
      ],
      currentVersion: '',
      currentData: {}
    }
  },
  created() {
    this.INIT_SUBSCRIBE_CONFIG()
    this.fetchData()
  },
  methods: {
    ...mapMutations(['INIT_SUBSCRIBE_CONFIG', 'SET_SUBSCRIBE_CONFIG']),
    goBack() {
      this.$router.back()
    },
    fetchData() {
      const params = { configId: this.$route.query.configId }
      queryData(params).then(res => {
        if (res.length > 0) {
          this.data = res
          this.currentData = res.find(item => item.jobStatus === 1) || res[res.length - 1]
          this.currentVersion = this.currentData.version
          this.SET_SUBSCRIBE_CONFIG(this.currentData)
          this.currentTabs.forEach(tab => {
            this.$refs[tab.name].updatePageData()
          })
        }
      }).catch(res => {
        this.$message.error('获取数据发布失败！原因：' + res.message || '暂无')
      })
    },
    handleVersionChange(tab, event) {
      this.currentData = this.data.find(item => item.version === tab.name)
      this.SET_SUBSCRIBE_CONFIG(this.currentData)
      this.currentTabs.forEach(tab => {
        this.$refs[tab.name].updatePageData()
      })
    },
    handleConfigTabClick(tab) {
      this.$refs[tab.name].updatePageData()
    }
  }
}
</script>
<style lang="less">
  @import "~assets/css/subscribe-view.less";
</style>


.home {
  padding: 0 @page-padding-horizontal;
  padding-top: 20px;
  max-width: @page-max-width;
  .page-header {
    display: flex;
    justify-content: space-between;
    height: @page-header;
    align-items: center;
    border-bottom: 1px solid #dcdcdc;
    .header-left {
      .filter {
        width: 360px;
      }
    }
  }
  .page-main {
    padding: @page-main-padding-vertical 0;
    height: calc(100vh - 170px - @page-header);
    min-height: 200px;
    .table {
      height: calc(100% - 70px);
    }
    .pagination {
      margin-top: 24px;
    }
  }
  .el-input .el-select{
    width: 110px;
  }
}

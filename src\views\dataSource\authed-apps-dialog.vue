<!--
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-07-27 11:34:07
-->
<template>
  <div class="authed-apps-dialog">
    <el-dialog title="将该数据源授权给其他应用" :before-close="handleClose" :visible.sync="visible" :close-on-click-modal="false">
      <el-form :model="form" label-width="120px" label-position="left">
        <el-form-item label="数据源所属应用" size="small">
          <span>{{form.dsAppName || form.dsAppKey}}</span>
        </el-form-item>
        <el-form-item label="数据源" size="small">
          <span>{{`(${form.dsType})${form.dsName}`}}</span>
        </el-form-item>
        <el-form-item label="授权应用" size="medium">
          <el-select style="width:100%" filterable v-model="form.authedApps" multiple :appLoading="appLoading">
            <el-option
              v-for="item in appOptions"
              :key="item.appKey" 
              :label="item.appLabel" 
              :value="item.appKey">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleConfirm" size="medium" type="primary">授权</el-button>
        <el-button @click="visible = false" size="medium">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { queryApps } from 'services/common.service'
export default {
  data() {
    return {
      visible: false,
      form: {},
      appOptions: [],
      appLoading: false
    }
  },
  methods: {
    show(data) {
      this.visible = true
      this.form = data
      queryApps({ type: 'all' }).then(res => {
        this.appLoading = false
        this.appOptions = res.map(item => ({
          appKey: item.name,
          appName: item.label,
          appLabel: item.name + '（' + item.label + '）'
        }))
      }).catch(res => {
        this.appOptions = []
        this.appLoading = false
        this.$message.error('获取应用下拉列表失败！原因：' + res.message || '暂无')
      })
    },
    handleConfirm() {
      
    },
    handleClose(done) {
      this.$emit('close')
      done()
    }
  }
}
</script>

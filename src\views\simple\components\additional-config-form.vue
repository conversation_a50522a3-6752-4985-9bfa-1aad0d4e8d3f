<template>
  <el-form class="base-width" ref="form" :model="form" :rules="rules" label-width="94px" size="small">
    <section>
      <div class="base-config-title">读写批次配置</div>
      <el-form-item label="读写批次" prop="fetchSize">
        <span v-if="isView">{{ form.fetchSize }}</span>
        <el-input v-model.number="form.fetchSize" placeholder="请输入" :disabled="isOnlineEdit" v-else></el-input>
      </el-form-item>
      <el-form-item label="写入批次" prop="splitSize">
        <span v-if="isView">{{ form.splitSize }}</span>
        <el-input v-model.number="form.splitSize" placeholder="请输入" :disabled="isOnlineEdit" v-else></el-input>
      </el-form-item>
    </section>
    <section>
      <div class="base-config-title">加密配置</div>
      <el-form-item label="是否加密" prop="enableEncryption">
        <span v-if="isView">{{ form.enableEncryption ? '是' : '否' }}</span>
        <el-radio-group v-model="form.enableEncryption" :disabled="isEdit" v-else>
          <el-radio :label="true">加密</el-radio>
          <el-radio :label="false">不加密</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="form.enableEncryption">
        <el-form-item label="加密算法" prop="encryptionWay">
          <div style="display: flex;">
            <span v-if="isView">{{ form.encryptionWay }}</span>
            <el-select v-model="form.encryptionWay" filterable placeholder="请选择" style="width: 100%;" :disabled="isEdit" @change="handleEncryptionWayChange" v-else>
              <el-option label="RSA" value="RSA"></el-option>
              <el-option label="AES" value="AES"></el-option>
              <el-option label="MD5" value="MD5"></el-option>
              <el-option label="SHA128" value="SHA128"></el-option>
              <el-option label="SHA256" value="SHA256"></el-option>
            </el-select>
            <el-button style="margin-left:20px;" @click="handleGenEncryptionKey" :disabled="isEdit" v-show="!isView && ['RSA', 'AES'].includes(form.encryptionWay)">密钥生成</el-button>
          </div>
        </el-form-item>
        <el-form-item :label="form.encryptionWay === 'RSA' ? '公钥key' : '密钥key'" prop="encryptionPublicKey" v-if="['RSA', 'AES'].includes(form.encryptionWay)">
          <span v-if="isView">{{ form.encryptionPublicKey }}</span>
          <el-input :value="form.encryptionPublicKey" :disabled="isEdit" v-else></el-input>
        </el-form-item>
        <el-form-item label="私钥key" prop="encryptionPrivateKey" v-if="form.encryptionWay === 'RSA'">
          <span v-if="isView">{{ form.encryptionPrivateKey }}</span>
          <el-input v-model="form.encryptionPrivateKey" :disabled="isEdit" v-else></el-input>
        </el-form-item>
      </template>
    </section>
    <encryption-key-dialog ref="encryptionKeyDialog" :encryptionWay="form.encryptionWay" @handleChoose="handleChoose"></encryption-key-dialog>
  </el-form>
</template>

<script>
import EncryptionKeyDialog from 'components/bids-datastruct-form-part/encryption-key-dialog'

export default {
  components: { EncryptionKeyDialog },
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    },
    isOnlineEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        fetchSize: 2000,
        splitSize: 2000,
        enableEncryption: false,
        encryptionWay: '',
        encryptionPublicKey: '',
        encryptionPrivateKey: ''
      },
      rules: {
        fetchSize: [
          { required: true, message: '请输入读写批次', trigger: 'blur' },
          { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' }
        ],
        splitSize: [
          { required: true, message: '请输入写入批次', trigger: 'blur' },
          { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' }
        ],
        encryptionWay: [
          { required: true, message: '请选择加密算法', trigger: 'change' }
        ],
        encryptionPublicKey: [{ required: true, message: '请输入密钥key', trigger: 'change' }],
        encryptionPrivateKey: [{ required: true, message: '请输入私钥', trigger: 'change' }]
      }
    }
  },
  methods: {
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    setData(data) {
      this.form = {
        fetchSize: data.fetchSize,
        splitSize: data.splitSize,
        enableEncryption: data.enableEncryption,
        encryptionWay: data.encryptionWay,
        encryptionPublicKey: data.encryptionPublicKey,
        encryptionPrivateKey: data.encryptionPrivateKey
      }
    },
    getSubData() {
      return { 
        fetchSize: this.form.fetchSize,
        splitSize: this.form.splitSize,
        enableEncryption: this.form.enableEncryption,
        encryptionWay: this.form.encryptionWay,
        encryptionPublicKey: this.form.encryptionPublicKey,
        encryptionPrivateKey: this.form.encryptionPrivateKey
      }
    },
    handleGenEncryptionKey() {
      if (!this.form.encryptionWay) {
        this.$message.warning('请先选择加密算法！')
        return
      }
      this.$refs['encryptionKeyDialog'].visible = true
    },
    chooseEncryptionPublicKey() {
      this.$refs['encryptionKeyDialog'].show(this.configData.configId)
    },
    handleEncryptionWayChange(val) {
      if (this.form.encryptionPublicKey) {
        this.form.encryptionPublicKey = ''
      }
      if (this.form.encryptionPrivateKey) {
        this.form.encryptionPrivateKey = ''
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleChoose(encryptionPublicKey, encryptionPrivateKey) {
      this.form.encryptionPublicKey = encryptionPublicKey
      if (encryptionPrivateKey) {
        this.form.encryptionPrivateKey = encryptionPrivateKey
      }
    }
  }
}
</script>

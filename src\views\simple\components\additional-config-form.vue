<template>
  <el-form class="base-width" ref="form" :model="form" :rules="rules" label-width="94px" size="small">
    <section>
      <div class="base-config-title">读写批次配置</div>
      <el-form-item label="读写批次" prop="fetchSize">
        <span v-if="isView">{{ form.fetchSize }}</span>
        <el-input v-model="form.fetchSize" placeholder="请输入" v-else></el-input>
      </el-form-item>
      <el-form-item label="写入批次" prop="splitSize">
        <span v-if="isView">{{ form.splitSize }}</span>
        <el-input v-model="form.splitSize" placeholder="请输入" v-else></el-input>
      </el-form-item>
    </section>
    <section>
      <div class="base-config-title">加密配置</div>
      <el-form-item label="是否加密" prop="enableEncryption">
        <span v-if="isView">{{ form.enableEncryption ? '是' : '否' }}</span>
        <el-radio-group v-model="form.enableEncryption" :disabled="isEdit" v-else>
          <el-radio :label="true">加密</el-radio>
          <el-radio :label="false">不加密</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="加密算法" prop="encryptionWay" v-if="form.enableEncryption">
        <span v-if="isView">{{ form.encryptionWay }}</span>
        <el-select v-model="form.encryptionWay" filterable placeholder="请选择" style="width: 100%;" :disabled="isEdit" v-else>
          <el-option label="AES" value="AES"></el-option>
        </el-select>
      </el-form-item>
    </section>
  </el-form>
</template>

<script>
export default {  
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        fetchSize: '',
        splitSize: '',
        enableEncryption: false,
        encryptionWay: ''
      },
      rules: {
        fetchSize: [
          { required: true, message: '请输入读写批次', trigger: 'blur' },
          { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' }
        ],
        splitSize: [
          { required: true, message: '请输入写入批次', trigger: 'blur' },
          { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' }
        ],
        encryptionWay: [
          { required: true, message: '请选择加密算法', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    setData(data) {
      this.form = {
        fetchSize: data.fetchSize,
        splitSize: data.splitSize,
        enableEncryption: data.enableEncryption,
        encryptionWay: data.encryptionWay
      }
    },
    getSubData() {
      return { ...this.form }
    }
  }
}
</script>

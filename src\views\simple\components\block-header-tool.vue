<template>
  <bids-block-header :title="title">
    <template>
      <el-button type="text" size="small" @click="handleClick">
        {{ value ? "收起" : "展开" }}
       </el-button>
    </template>
  </bids-block-header>
</template>

<script>
import { Bids<PERSON><PERSON>Header } from 'components'

export default {
  components: {
    BidsBlockHeader
  },
  props: {
    value: {
      type: Boolean,
      default: true,
      required: true
    },
    title: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      expand: false
    }
  },
  methods: {
    handleClick() {
      this.$emit('input', !this.value)
    }
  }
}
</script>

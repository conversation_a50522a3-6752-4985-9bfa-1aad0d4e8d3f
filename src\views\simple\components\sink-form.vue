<template>
  <div class="data-destination-container">
    <div class="destination-title">数据目的地</div>
    <div class="destination-form">
      <el-form ref="form" :model="form" :rules="rules" label-width="109px" size="small" :disabled="isEdit">
        <el-form-item label="数据类型" prop="dsType">
          <span v-if="isView">{{ form.dsType }}</span>
          <el-select v-model="form.dsType" filterable :disabled="isEdit" @change="handleDsTypeChange" v-else>
            <el-option v-for="item in dsTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据源" prop="dsId">
          <span v-if="isView">{{ getDsLabel(form.dsId) }}</span>
          <el-select v-model="form.dsId" filterable :disabled="isEdit" @change="handleDsIdChange" v-else>
            <el-option v-for="item in options" :key="item.dsId" :label="item.label" :value="item.dsId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据表" prop="tableName">
          <span v-if="isView">{{ form.tableName }}</span>
          <el-input v-model="form.tableName" placeholder="请输入" :disabled="isEdit" @input="() => debounceTableNameChange()" v-else style="width: 207px;"></el-input>
        </el-form-item>
        <el-form-item label="主键冲突策略">
          <span v-if="isView">{{ form.overrideStrategy === 'RENAME' ? '重命名模式' : '追加模式' }}</span>
          <el-select v-model="form.overrideStrategy" disabled v-else>
            <el-option label="重命名模式" value="RENAME"></el-option>
            <el-option label="追加模式" value="APPEND"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="写入模式">
          <span v-if="isView">{{ form.pkCollisionStrategy === 'OVERRIDE' ? '覆盖' : '忽略' }}</span>
          <el-select v-model="form.pkCollisionStrategy" disabled v-else>
            <el-option label="覆盖" value="OVERRIDE"></el-option>
            <el-option label="忽略" value="IGNORE"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { queryTableColumns } from 'services/common.service.js'
import { DataEvent } from 'sdc-core'

export default {
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    dataSourceOptions: {
      type: Array,
      default: () => []
    },
    dsTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        dsType: '',
        dsId: '',
        dsName: '',
        tableName: '',
        overrideStrategy: 'RENAME',
        pkCollisionStrategy: 'OVERRIDE'
      },
      rules: {
        dsType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ],
        dsId: [
          { required: true, message: '请选择数据源', trigger: 'change' }
        ],
        tableName: [
          { required: true, message: '请选择数据表', trigger: 'change' }
        ]
      },
      debounceTableNameChange: DataEvent.debounce(this.handleTableNameChange, 300)
    }
  },
  computed: {
    options() {
      if (!this.form.dsType) return []
      return this.dataSourceOptions.filter(item => item.dsType === this.form.dsType)
    }
  },
  methods: {
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    setData(data) {
      this.form = {
        dsType: data.properties.dsType,
        dsId: data.properties.dsId,
        dsName: data.properties.dsName,
        tableName: data.properties.tableName,
        overrideStrategy: data.overrideStrategy,
        pkCollisionStrategy: data.pkCollisionStrategy
      }
      this.handleTableNameChange()
    },
    getSubData() {
      return {
        overrideStrategy: this.form.overrideStrategy,
        pkCollisionStrategy: this.form.pkCollisionStrategy,
        properties: {
          dsId: this.form.dsId,
          dsType: this.form.dsType,
          dsName: this.form.dsName,
          tableName: this.form.tableName
        }
      }
    },
    getDsLabel(dsId) {
      return this.dataSourceOptions.find(item => item.dsId === dsId)?.label
    },
    handleDsTypeChange() {
      this.form.dsId = ''
      this.form.dsName = ''
      this.form.tableName = ''
    },
    handleDsIdChange(value) {
      this.form.dsName = this.dataSourceOptions.find(item => item.dsId === value).dsName

      this.form.tableName = ''
    },
    handleTableNameChange() {
      if (this.form.dsId && this.form.tableName) {
        queryTableColumns({ datasourceId: this.form.dsId, tablename: this.form.tableName }).then(res => {
          const data = res.map(item => ({
            sinkFieldKey: item.name,
            sinkFieldType: item.type,
            sinkFieldSeq: item.seq
          }))
          this.$emit('updateSinkFields', data)
        }).catch(() => {
          this.$emit('updateSinkFields', [])
          this.$message.error('获取数据表字段失败！')
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.data-destination-container {
  width: 600px;
  .destination-title {
    height: 40px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    border: 1px solid #d8d8d8;
    background: #f5f7f9;
    color: #333333;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    padding-left: 35px;
  }
  .destination-form {
    border: 1px solid #d8d8d8;
    border-top: 0;
    padding-top: 16px;
    height: 378px;
  }
}
</style>

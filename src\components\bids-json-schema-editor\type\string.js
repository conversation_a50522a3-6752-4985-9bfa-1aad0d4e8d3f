const value = {
  default: null,
  minLength: undefined,
  maxLength: undefined,
  pattern: null,
  format: null
}
const attr = {
  default: {
    name: '默认值',
    type: 'string'
  },
  minLength: {
    name: '最小字符数',
    type: 'integer'
  },
  maxLength: {
    name: '最大字符数',
    type: 'integer'
  },
  pattern: {
    name: '正则表达式',
    type: 'string'
  },
  format: {
    name: '格式',
    type: 'array',
    enums: ['date', 'date-time', 'email', 'hostname', 'ipv4', 'ipv6', 'uri']
  }
}
const wrapper = { value, attr }
export default wrapper

<template>
  <div class="pre-check-dialog">
    <el-dialog title="预校验" :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" width="1002px" top="6vh">
      <p style="margin-bottom: 20px;">以下同步授权，同步将造成目标环境授权改动，详情如下(勾选点击确定则继续同步，不勾选点击确定则继续同步其余授权，点击取消则取消该次同步)</p>

      <el-table :data="tableData" style="width: 100%" height="500" :header-cell-style="{ background: '#f5f7f9', height: '60px' }">
        <el-table-column type="selection" width="48" align="center"></el-table-column>
        <template v-if="type === 'publish'">
          <el-table-column label="发布名称" prop="name" width="188" show-overflow-tooltip></el-table-column>
          <el-table-column label="发布编码" prop="code" width="183" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属应用" prop="app" width="172" show-overflow-tooltip></el-table-column>
          <el-table-column label="发布版本" prop="version" width="134">
            <template> template </template>
          </el-table-column>
        </template>

        <template v-if="type === 'subscribe'">
          <el-table-column label="订阅名称" prop="name" width="188" show-overflow-tooltip></el-table-column>
          <el-table-column label="订阅编码" prop="code" width="183" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属应用" prop="app" width="172" show-overflow-tooltip></el-table-column>
          <el-table-column label="订阅版本" prop="version" width="134">
            <template> template </template>
          </el-table-column>
        </template>

        <el-table-column label="校验结果" prop="result" width="114"></el-table-column>
        <el-table-column label="详情" width="115">
          <template v-slot="{ row }">
            <el-button @click="handleView(row)" type="text" size="small">查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="medium">取消</el-button>
        <el-button size="medium" type="primary" @click="handleConfirm">确认同步</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { pagination } from 'mixins'

export default {
  name: 'pre-check-dialog',
  mixins: [pagination], // 分页
  props: {
    initData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      type: 'publish',
      visible: false,
      paginationType: 'remote', // 由服务器实现分页查询
      tableData: [
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 }
      ],
      multipleSelection: []
    }
  },
  methods: {
    show() {
      this.fetchData()
      this.visible = true
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    // 调用同步接口
    handleConfirm() {
      Promise.resolve().then(() => {
        this.$confirm('同步完成，请查看同步结果', '提示', { type: 'success' }).then(() => {
          this.visible = false
          this.$emit('confirm', {})
        })
      })
    },
    fetchData() {},
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleView(row) {
      this.$emit('view', row)
    }
  }
}
</script>

<style lang="less" scoped>
.pre-check-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px 20px 10px 20px;
  }
  .pagination {
    margin-top: 20px;
  }
}
</style>

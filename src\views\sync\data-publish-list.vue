<template>
  <fragment>
    <div class="page-header">
      <div class="filter">
        <el-form :model="form" :inline="true" size="small">
          <el-form-item label="所属应用">
            <el-select v-model="form.appCode" placeholder="请选择" clearable filterable>
              <el-option label="1" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发布名称/编码">
            <el-input v-model="form.code" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.creator" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button>重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="page-main">
      <div class="table">
        <el-table ref="table" v-loading="loading" key="publishTable" :data="tableData" height="100%" :header-cell-style="{background:'#f5f7f9', height: '60px'}" style="width: 100%">
          <el-table-column type="index" :index="(pageIndex-1)*10+1" width="50"></el-table-column>
          <el-table-column
            v-for="(item, index) in tableColumn" :key="index"
            :prop="item.prop"
            :label="item.label"
            :formatter="item.formatter"
            :min-width="item.width"
            :show-overflow-tooltip="true">
          </el-table-column>
          <el-table-column prop="creator" label="创建人" min-width="197">
            <template>
              <BidsUserName user="yiwuhe"></BidsUserName>
            </template>
          </el-table-column>
          <el-table-column prop="opereator" label="操作人" min-width="197">
            <template>
              <BidsUserName user="yiwuhe"></BidsUserName>
            </template>
          </el-table-column>
          <el-table-column prop="syncTime" label="同步时间" min-width="197">
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="163">
            <template v-slot="{ row }">
              <el-button @click="handleView(row)" type="text" size="small">同步详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </div>
  </fragment>
</template>
<script>
import { Fragment } from 'vue-fragment'
import { pagination, filterInput, formatter } from 'mixins'
import { queryAllData } from 'services/publish.service'
import { mapState } from 'vuex'
import { BidsUserName } from 'components'

export default {
  name: 'data-publish-list',
  mixins: [pagination, filterInput, formatter], // 分页，搜索栏，格式化表格内容
  components: {
    Fragment,
    BidsUserName
  },
  computed: {
    ...mapState(['userInfo']) // 登录用户信息
  },
  data() {
    return {
      form: {},
      loading: false,
      paginationType: 'remote', // 由服务器实现分页查询
      tableData: [], // 数据发布表格数据
      tableColumn: [
        { prop: 'interfaceName', label: '发布名称', width: '187' },
        { prop: 'interfaceCode', label: '发布编码', width: '194' },
        { prop: 'appName', label: '所属应用', width: '166' },
        { prop: 'interfaceDesc', label: '发布版本', width: '131' }
      ]
    } 
  },
  methods: {
    fetchData() {
      // 获取数据发布列表
      const paginator = { pageSize: this.pageSize, pageIndex: this.pageIndex } // 分页查询的参数
      const criteria = [] // 搜索栏的参数
      if (this.criteriaValue && this.criteriaKey) {
        criteria.push({ criteriaKey: this.criteriaKey, criteriaValue: this.criteriaValue })
      }
      const postData = { paginator, criteria }
      this.loading = true
      queryAllData(postData).then(res => {
        res = res || { paginator: {} }
        this.tableData = res.content ? res.content.map(row => ({
          ...row,
          isMine: (row.jobOwners || []).some(item => item.staffId === this.userInfo.staffId), // 该数据发布是否属于登录用户
          schedulePeriodCron: this.formatterScheduleAtCron(row)
        })) : []
        this.recordCount = res.paginator.recordCount || 0
        this.$refs.table.doLayout()
        this.loading = false
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('获取数据发布失败！原因：' + res.message || '暂无')
        this.loading = false
      })
    },
    handleView(row) {
      this.$emit('view', row)
    }
  }
}
</script>

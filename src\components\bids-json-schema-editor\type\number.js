const value = {
  default: null,
  minimum: undefined,
  maximum: undefined,
  exclusiveMinimum: null,
  exclusiveMaximum: null
}
const attr = {
  default: {
    name: '默认值',
    type: 'number'
  },
  minimum: {
    name: '最小值',
    type: 'number'
  },
  maximum: {
    name: '最大值',
    type: 'number'
  },
  exclusiveMinimum: {
    name: '不包含最小值',
    type: 'boolean'
  },
  exclusiveMaximum: {
    name: '不包含最大值',
    type: 'boolean'
  }
}
const wrapper = { value, attr }
export default wrapper

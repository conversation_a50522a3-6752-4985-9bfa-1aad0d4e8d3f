/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-23 16:02:28
 */
/* 应用常量配置 */
export const EVENT_KEYS = {
  'SWITCH_LAYOUT': 'SWITCH_LAYOUT'
}
export const SIDEBAR_MENUS = {
  data: [
    { key: 'home', text: '数据市场', url: '/home', icon: 'el-icon-receiving', pid: '0', level: 1 },
    { 
      key: 'expert', 
      text: '专家模式', 
      icon: 'el-icon-discover', 
      pid: '0', 
      level: 1,
      children: [
        { key: 'publish', text: '我的发布', url: '/publish', icon: 'el-icon-s-promotion', pid: 'expert', level: 2 },
        { key: 'subscribe', text: '我的订阅', url: '/subscribe', icon: 'el-icon-collection', pid: 'expert', level: 2 }
      ]
    },
    { key: 'simple', text: '简单模式', url: '/simple', icon: 'el-icon-share', pid: '0', level: 1 },
    { key: 'statistics', 
      text: '统计分析', 
      // url: '/statistics', 
      icon: 'el-icon-data-analysis', 
      pid: '0', 
      level: 1,
      children: [
        { key: 'statistics', text: '统计分析(旧)', url: '/statistics', icon: 'el-icon-data-analysis', pid: 'statistics', level: 2 },
        { key: 'statisticsNew', text: '统计分析(新)', url: '/statistics/new', icon: 'el-icon-data-analysis', pid: 'statistics', level: 2 }
      ]
    },
    // { key: 'sync', text: '环境同步', url: '/sync', icon: 'el-icon-refresh', pid: '0', level: 1 },
    { key: 'manage', 
      text: '我的管理', 
      icon: 'el-icon-setting', 
      pid: '0', 
      level: 1, 
      children: [
        { key: 'datasource', text: '数据源', url: '/manage/datasource', icon: 'el-icon-tickets', pid: 'manage', level: 2 },
        { key: 'release', text: '发布管理', url: '/manage/release', icon: 'el-icon-upload', pid: 'manage', level: 2 }
      ]
    }
  ]
}

export const SIDEBAR_MENUS_KEY = ['home', 'publish', 'subscribe', 'datasource', 'statistics', 'release']

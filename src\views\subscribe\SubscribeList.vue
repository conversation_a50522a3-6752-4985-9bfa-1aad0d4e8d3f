<!--
 * @Description: 我的订阅页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-13 11:11:15
-->
<template>
  <div class="subscribe-list">
    <div class="page-header">
      <div class="header-left">
        <el-form :inline="true" :model="filterForm"  size="small">
          <el-form-item label="所属应用">
            <!-- <el-input v-model="filterForm.appName" @input="handleInput"></el-input> -->
            <el-select v-model="filterForm.appName" filterable @change="handleAppChange" clearable>
              <el-option v-for="item in appOptions" :key="item.appKey" :value="item.appKey" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订阅名称">
            <el-input v-model="filterForm.interfaceName" @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="filterForm.creatorName" @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filterForm.jobStatus" filterable @change="handleInput" clearable>
              <el-option v-for="([value, label], key) in filter_dt_jobStatus" :key="key" :value="value" :label="label"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button type="primary" size="medium" @click="handleAdd()">新增订阅</el-button>
      </div>
    </div>
    <div class="page-main">
      <div class="table">
        <el-table
          :data="tableData"
          v-loading="loading"
          ref="table"
          height="100%"
          :header-cell-style="{background:'#f5f7f9', height: '60px'}"
          style="width: 100%">
          <el-table-column
            type="index"
            :index="(pageIndex-1)*10+1"
            width="50">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableColumn" :key="index"
            :prop="item.prop"
            :formatter="item.formatter"
            :sortable="item.sortable"
            :label="item.label"
            :min-width="item.minWidth || item.width"
            :show-overflow-tooltip="!item.complete"
            align="center">
          </el-table-column>
          <el-table-column label="运行状态" width="80" align="center">
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="left" @show="handlePopoverShow(scope.row)" :open-delay="200">
                <bids-simple-table :data="exeStatusData" :table-column="statusTableColumn" :loading="popoverLoading"></bids-simple-table>
                <span slot="reference" class="tableCellIcon"><i slot="reference" class="el-icon-reading"></i></span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            width="150">
            <template slot-scope="scope">
              <el-button @click="handleEdit(scope.row)" type="text" size="small">编辑</el-button>
              <el-button @click="handleManageJobs(scope.row)" type="text" size="small">管理实例</el-button>
              <el-button @click="handleDelete(scope.row)" type="text" class="button-text-danger" size="small" :disabled="scope.row.jobStatus !== -1">删除</el-button>
              <!-- <div v-else style="display:inline-block;width:24px;margin-left:10px;"></div> -->
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { pagination, filterInput, formatter } from 'mixins'
import { queryMineData, deleteData } from 'services/subscribe.service'
import { BidsSimpleTable } from 'components'
import { queryJobShapshot, queryAppsInConfig } from 'services/common.service'
import { DataType } from 'sdc-core'
import { mapState } from 'vuex'

export default {
  name: 'subscribe-page',
  components: {
    BidsSimpleTable
  },
  mixins: [pagination, filterInput, formatter], // 外部mixin
  data() { 
    return {
      appOptions: [],
      tableData: [],
      exeStatusData: [],
      paginationType: 'remote',
      loading: false,
      popoverLoading: false,
      tableColumn: [
        { prop: 'appName', label: '所属应用', minWidth: '120' },
        { prop: 'interfaceName', label: '订阅名称', minWidth: '160', complete: true },
        { prop: 'interfaceCode', label: '订阅编码', minWidth: '120', complete: true },
        { prop: 'interfaceDesc', label: '订阅描述', minWidth: '120' },
        { prop: 'version', label: '当前版本', minWidth: '80' },
        // { prop: 'dataFormat', label: '数据类型', minWidth: '80' },
        { prop: 'collectType', label: '接收类型', minWidth: '80', formatter: this.formatterCollectType },
        { prop: 'schedulePeriodCron', label: '订阅周期', minWidth: '110', sortable: true },
        { prop: 'creatorName', label: '创建人', minWidth: '70' },
        { prop: 'jobStatus', label: '状态', minWidth: '70', formatter: this.formatterJobStatus }
      ],
      statusTableColumn: [
        { prop: 'version', label: '版本', width: '100' },
        { prop: 'jobExecutorStatus', label: '最近任务运行状态', width: '200', formatter: this.formatterJobExeState }
      ]
    } 
  },
  computed: {
    ...mapState({
      filter_dt_jobStatus: 'filter_dt_jobStatus'
    })
  },
  activated() {
    this.fetchData()
    const params = { configType: 'SUB', kind: 'MINE' }
    queryAppsInConfig(params).then(res => {
      res && (res = res.map(v => { return { ...v, label: `${v.appKey}（${v.appName}）` } }))
      this.appOptions = res
    })
  },
  methods: {
    fetchData() {
      const paginator = { pageSize: this.pageSize, pageIndex: this.pageIndex }
      const criteria = [] // 搜索栏的参数
      Object.keys(this.filterForm).forEach(key => {
        const value = this.filterForm[key]
        if (value) {
          if (key === 'appName') {
            criteria.push({ criteriaKey: 'appKey', criteriaValue: value, accurate: true })
          } else {
            criteria.push({ criteriaKey: key, criteriaValue: value })
          }
        }
      })
      const postData = { paginator, criteria }
      this.loading = true
      queryMineData(postData).then(res => {
        res = res || { paginator: {} }
        this.tableData = (res.content || []).map(item => ({ ...item, schedulePeriodCron: this.formatterScheduleAtCron(item) }))
        this.recordCount = res.paginator.recordCount || 0
        this.loading = false
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('获取我的数据订阅失败！原因：' + res.message || '暂无')
        this.loading = false
      })
      this.$refs.table.doLayout()
    },
    handleAdd() {
      this.$router.push({ name: 'SubscribeAdd' })
    },
    handleEdit(row) {
      this.$router.push({ path: '/subscribe/edit', query: { configId: row.configId } })
    },
    handleDelete(row) {
      this.$confirm(`此操作将删除数据订阅 ${row.interfaceCode}, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = { configId: row.configId }
        deleteData(params).then(res => {
          this.$message.success('操作成功！')
          if (this.tableData.length === 1 && this.pageIndex > 1) this.pageIndex -= 1
          this.fetchData()
        }).catch(res => {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
      }).catch(() => {})
    },
    handleManageJobs(row) {
      this.$router.push({ path: '/subscribe/jobs', query: { configId: row.configId } })
    },
    handlePopoverShow(row) {
      const params = { configId: row.configId }
      this.exeStatusData = []
      this.popoverLoading = true
      queryJobShapshot(params).then(res => {
        if (DataType.isObject(res)) res = [res]
        this.exeStatusData = res
        this.popoverLoading = false
      }).catch(res => {
        this.popoverLoading = false
        this.$message.error('获取运行状态失败！原因：' + res.message || '暂无')
      })
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/subscribe-list.less";
</style>

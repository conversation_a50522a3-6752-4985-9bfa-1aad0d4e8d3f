/*
 * @Description: sql编辑器的表达式
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-03 16:04:42
 */
import _CodeMirror from 'codemirror'
const CodeMirror = window.CodeMirror || _CodeMirror
export default {
  data() {
    return {
      expressionList: [
        { expression: '{constant#BIDS_BATCH_NUMBER}', explain: '当前批次号' },
        { expression: '{constant#JOB_NAME}', explain: '当前任务名' },
        { expression: '{constant#INTERFACE_CODE}', explain: '该数据发布/订阅编码' },
        { expression: '{constant#INTERFACE_NAME}', explain: '该数据发布/订阅名称' },
        { expression: '{constant#INTERFACE_DESC}', explain: '该数据发布/订阅描述' },
        { expression: '{constant#CORPKEY}', explain: '当前租户' },
        { expression: '{curTimeStamp#yyyy-MM-dd HH:mm:ss}', explain: '对应格式的当前时间' },
        { expression: '{cycleTimeStamp#yyyy-MM-dd HH:mm:ss}', explain: '对应格式的周期时间' },
        { expression: '{curTime(0)#yyyy-MM-dd HH:mm:ss}', explain: '当前时间的前/后|offsetSeconds|秒' },
        { expression: '{cycleTime(0)#yyyy-MM-dd HH:mm:ss}', explain: '周期时间的前/后|offsetSeconds|秒' }
      ]
    }
  },
  computed: {
    // hintOptions() {
    //   const expression = this.expressionList.map(item => item.expression.slice(1, -1))
    //   const tables = {}
    //   expression.forEach(item => tables[item] = [])
    //   return {
    //     completeSingle: false,
    //     tables
    //   }
    // }
    hintOptions() {
      const tables = {}
      // 注入表达式提示
      const expression = this.expressionList.map(item => item.expression.slice(1, -1))
      expression.forEach(item => tables[item] = [])
      // 订阅规则页面的列名提示
      if (this.configType === 'subscribe' && this.dataToBeSub) {
        this.dataToBeSub.forEach(item => {
          this.formatterSinkTableName(item) && (tables[this.formatterSinkTableName(item)] = [])
          item.job.sink.columns && item.job.sink.columns.forEach(col => {
            col.name && (tables[col.name] = [])
          })
        }) 
      } else if (this.pageName === 'bids-publish-output' && this.configData) {
        // 数据输出页面的提示
        let tableName = `PUB_${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}`
        const storageStrategy = this.configData.job?.sink?.storageStrategy
        if (storageStrategy === 'SAVE') {
          if (this.pageStatus === 'build') {
            tableName = `${this.configData.appKey}_${this.configData.interfaceCode}_v1`
          } else if (this.pageStatus === 'clone&build') {
            const latest_version = this.configData.latestVersion?.replace(/(\d+)/, ($1) => Number($1) + 1)
            const table_name = `${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}_${latest_version}`
            tableName = table_name
          } else if (this.pageStatus === 'edit') {
            const version = this.configData.version
            const table_name = `${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}_${version}`
            tableName = table_name
          }
        }
        tables[tableName] = []
        this.configData.job.sources[0].columns && this.configData.job.sources[0].columns.forEach(col => {
          col.name && (tables[col.name] = [])
        })
      }
      return {
        completeSingle: false,
        tables,
        hint: (cmInstance, hintOptions) => {
          // 增强代码提示
          const cursor = cmInstance.getCursor()
          const token = cmInstance.getTokenAt(cursor)
          let inner = CodeMirror.hint.sql(cmInstance, hintOptions) || { from: cursor, to: cursor, list: [] }
          // const curInput = cmInstance.getValue().slice(token.start, token.end) // 去掉当前正在输入字符
          if (token.string === '*') {
            if (this.configType === 'subscribe' && this.dataToBeSub) {
              inner.list = this.dataToBeSub.filter(item => this.formatterSinkTableName(item)).map(item => {
                return `导入表${this.formatterSinkTableName(item)}的全部列`
              })
            } else if (this.pageName === 'bids-publish-output') {
              inner.list = ['导入输入数据表结构的全部列']
            }
          }
          return inner
        }
      }
    }
  },
  methods: {
    handleBeforeChange(cm, changeObj) {
      const text = changeObj.text.map(item => item.trim()).join(' ')
      if (text && text.search(/导入.*表.*的全部列/g) !== -1) {
        let columns = null
        if (this.configType === 'subscribe' && this.dataToBeSub) {
          const data = this.dataToBeSub.find(item => text.indexOf(this.formatterSinkTableName(item)) !== -1)
          columns = data.job.sink.columns
        } else if (this.configType === 'publish' && this.configData) {
          const data = this.configData
          columns = data.job.sources[0].columns
        }
        // const data = this.dataToBeSub.find(item => text.indexOf(this.formatterSinkTableName(item)) !== -1)
        if (columns) {
          const str = columns.map(item => item.name).join(', ')
          changeObj.from.ch = changeObj.from.ch - 1
          changeObj.update(changeObj.from, changeObj.to, [str])
        } else {
          changeObj.cancel()
        }
      }
    }
  }
}

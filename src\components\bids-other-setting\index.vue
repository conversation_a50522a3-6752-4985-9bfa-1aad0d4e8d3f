<!--
 * @Description: 其他配置信息组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-06-08 11:25:37
-->
<template>
  <div class="bids-other-setting">
    <el-form :model="form" ref="form" size="small" :rules="rules" label-width="120px" :disabled="!mildEditable">
      <template v-if="configType === 'subscribe' || configData.job.sink.storageStrategy === 'SAVE'">
        <template v-if="configData.collectType === 1">
          <el-form-item label="调度类型" prop="scheduleStrategy.scheduleType">
            <el-radio-group v-model="form.scheduleStrategy.scheduleType" @change="handleScheduleTypeChange" v-if="mildEditable">
              <el-radio label="OUT">外部触发</el-radio>
              <el-radio label="REPEAT">平台周期性触发</el-radio>
            </el-radio-group>
            <span v-else>{{form.scheduleStrategy.scheduleType === 'REPEAT' ? '平台周期性触发' : '外部触发'}}</span>
          </el-form-item>
          <template v-if="form.scheduleStrategy.scheduleType === 'OUT'">
            <el-form-item label="任务触发接口">
              <div class="urlPanel">
                <span :data-clipboard-text="form.runJobUrl" id="runJobUrl">{{form.runJobUrl}}</span>
                <span style="margin-left:5px;">[POST]</span>
                <div class="event-icon copy-icon runJobUrl" data-clipboard-target="#runJobUrl" @click="handleCopyUrl('.runJobUrl')"><i class="el-icon-document-copy"></i></div>
              </div>
            </el-form-item>
            <el-form-item label="状态查询接口">
              <div class="urlPanel">
                <span :data-clipboard-text="form.getStatusUrl" id="getStatusUrl">{{form.getStatusUrl}}</span>
                <span style="margin-left:5px;">[GET]</span>
                <div class="event-icon copy-icon getStatusUrl" data-clipboard-target="#getStatusUrl" @click="handleCopyUrl('.getStatusUrl')"><i class="el-icon-document-copy"></i></div>
              </div>
            </el-form-item>
          </template>
          <el-form-item label="调度周期" prop="scheduleStrategy.scheduleAtCron" v-if="form.scheduleStrategy.scheduleType === 'REPEAT'">
            <el-popover
              placement="right"
              trigger="click"
              :disabled="['read'].includes(pageStatus)"
              @show="$refs['crontab'].setCronTab(form.scheduleStrategy.scheduleUnit, form.scheduleStrategy.scheduleAtCron)"
              v-model="schedulePopoverVisible"
              v-if="mildEditable">
              <bids-crontab ref="crontab" @confirm="crontabFill" @cancel="schedulePopoverVisible=false"></bids-crontab>
              <el-input v-model="form.scheduleStrategy.scheduleAtCron" slot="reference" readonly class="custom-input"></el-input>
            </el-popover>
            <span v-else>{{form.scheduleStrategy.scheduleAtCron}}</span>
          </el-form-item>
        </template>
        <el-form-item label="开始时间" prop="scheduleStrategy.scheduleBeginTime" v-if="form.scheduleStrategy.scheduleType !== 'OUT' && configData.collectType === 1">
          <el-date-picker
            v-model="form.scheduleStrategy.scheduleBeginTime"
            type="datetime"
            align="center"
            value-format="yyyy-MM-dd HH:mm:ss"
            :disabled="!moderateEditable || form.scheduleStrategy.scheduleType === 'OUT'"
            placeholder="选择日期时间"
            style="margin-right:10px;"
            @change="handleBeginTimeChange"
            v-if="mildEditable">
          </el-date-picker>
          <span v-else>{{form.scheduleStrategy.scheduleBeginTime}}</span>
        </el-form-item>
        <!-- <el-form-item label="超时警告" prop="timeoutStrategy.enabled">
          <template v-if="mildEditable">
            <el-switch v-model="form.timeoutStrategy.enabled" style="margin-right:20px;" 
              @change="(val) => { updateDataValue(val, keyPaths['timeoutStrategy.enabled']) }"/>
            <span>执行超过</span>
            <el-input-number :disabled="!form.timeoutStrategy.enabled" v-model="form.timeoutStrategy.timeoutSeconds" :min="1" 
              size="mini"  style="width:100px;" @change="(val) => { updateDataValue(val, keyPaths['timeoutSeconds']) }"/>
            <span>秒告警</span>
          </template>
          <span v-else>{{form.timeoutStrategy.enabled ? `执行超过 ${form.timeoutStrategy.timeoutSeconds} 秒告警` : '无'}}</span>
        </el-form-item> -->
        <el-form-item label="检查点" prop="checkpoint" v-if="configData.collectType === 2">
          <template v-if="mildEditable">
            <el-select v-model="form.checkpoint.type" placeholder="请选择类型" style="margin-right:20px;" 
              @change="(val) => { updateDataValue(val, keyPaths['checkpoint.type']) }">
              <el-option v-for="(item, index) in checkpointOptions" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <span>间隔</span>
            <el-input-number v-model="form.checkpoint.interval" :min="1" :step="1" size="mini"  style="width:100px;" 
              @change="(val) => { updateDataValue(val*1000, keyPaths['checkpoint.interval']) }"/>
            <span>秒保存检查点</span>
          </template>
          <span v-else>{{`${form.checkpoint.type}，隔 ${form.checkpoint.interval} 秒保存检查点`}}</span>
        </el-form-item>
        <!-- 全量采集 -->
        <!-- <el-form-item label="调度处理" prop="scheduleStrategy.ifScheduleUnfinished" required v-if="configData.collectType === 1">
          <el-radio-group v-model="form.scheduleStrategy.ifScheduleUnfinished" 
            @change="(val) => { updateDataValue(val, keyPaths['ifScheduleUnfinished']) }">
            <el-radio label="STOP_LAST_AND_USE_THIS">停止上次，发起本次</el-radio>
            <el-radio label="KEEP_LAST_AND_IGNORE_THIS">继续上次，忽略本次</el-radio>
          </el-radio-group>
            <span class="remarks-text">备注：若上次调度尚未完成，本次调度如何处理</span>
        </el-form-item> -->
        <!-- 全量采集 -->
        <!-- <el-form-item label="数据行数校验" prop="checkStrategy.enabled" v-if="configData.collectType === 1">
          <template v-if="mildEditable">
            <el-switch v-model="form.checkStrategy.enabled" style="margin-right:20px;"
              @change="(val) => { updateDataValue(val, keyPaths['checkStrategy.enabled']) }"/>
            <el-input-number v-model="form.checkStrategy.minRows" :disabled="!form.checkStrategy.enabled"
              placeholder="最小行数" :min="0" :precision="0" :max="form.checkStrategy.maxRows-1" size="mini"
              @change="(val) => { updateDataValue(val, keyPaths['minRows']) }"/>
            <span> - </span>
            <el-input-number v-model="form.checkStrategy.maxRows" :disabled="!form.checkStrategy.enabled" 
              placeholder="最大行数" :precision="0" :min="form.checkStrategy.minRows+1" size="mini"
              @change="(val) => { updateDataValue(val, keyPaths['maxRows']) }"/>
          </template>
          <span v-else>{{form.checkStrategy.enabled ? `${form.checkStrategy.minRows} - ${form.checkStrategy.maxRows} 行` : '无'}}</span>
        </el-form-item> -->
        <el-form-item label="失败策略" prop="retryStrategy.enabled">
          <template v-if="mildEditable">
            <el-radio-group v-model="form.retryStrategy.enabled" :disabled="radioDisabled" @change="(val) => { updateDataValue(val, keyPaths['retryStrategy.enabled']) }">
              <el-radio :label="true">
                <span>重试</span>
                <el-input-number :disabled="!form.retryStrategy.enabled" :min="1" :precision="0" style="width:100px;" 
                  v-model="form.retryStrategy.attempts" size="mini" @change="(val) => { updateDataValue(val, keyPaths['attempts']) }"/>
                <span>次，每次间隔</span>
                <el-input-number :disabled="!form.retryStrategy.enabled" :min="1" :precision="0" style="width:100px;" 
                  v-model="form.retryStrategy.delay" size="mini" @change="(val) => { updateDataValue(val, keyPaths['delay']) }"/>
                <span>分钟，并告警</span>
              </el-radio>
              <el-radio :label="false">跳过本次调度，并告警</el-radio>
            </el-radio-group>
          </template>
          <span v-else>{{form.retryStrategy.enabled ? `重试 ${form.retryStrategy.attempts} 次，每次间隔 ${form.retryStrategy.delay} 分钟，并告警` : '跳过本次调度，并告警'}}</span>
        </el-form-item>
        <el-form-item label="资源配置" prop="resources" required>
          <template v-if="mildEditable">
            <span>CPU核数<el-input-number v-model="form.resources.cores" size="mini" :precision="0" :min="1" :max="dt_system.maxCores" @change="(val) => { updateDataValue(val, keyPaths['cores']) }" style="width:100px;"/>，</span>
            <span>内存大小<el-input-number v-model="form.resources.memoryMB" size="mini" :precision="0" :min="1024" :max="dt_system.maxMemoryMB" @change="(val) => { updateDataValue(val, keyPaths['memoryMB']) }"/>M，</span>
            <span>并行度<el-input-number v-model="form.resources.parallelism" :disabled="true" size="mini" :precision="0" :min="1" :max="dt_system.maxParallelism"  @change="(val) => { updateDataValue(val, keyPaths['parallelism']) }" style="width:100px;"/></span>
          </template>
          <span v-else>{{`CPU核数：${form.resources.cores}，内存大小：${form.resources.memoryMB} M`}}</span>
        </el-form-item>
        <el-form-item label="超时阈值" prop="timeoutStrategy.enabled">
          <template v-if="mildEditable">
            <el-switch v-model="form.timeoutStrategy.enabled" style="margin-right:20px;" 
              @change="(val) => { updateDataValue(val, keyPaths['timeoutStrategy.enabled']) }"/>
            <span>执行超过</span>
            <el-input-number :disabled="!form.timeoutStrategy.enabled" v-model="value" v-initSeconds="form.timeoutStrategy.timeoutSeconds" :min="1" 
              size="mini"  style="width:100px;" @change="timeoutSecondsChange" />
            <span>分钟告警</span>
          </template>
          <span v-else>{{form.timeoutStrategy.enabled ? `执行超过 ${formatTimeoutSeconds(form.timeoutStrategy.timeoutSeconds)} 分钟告警` : '无'}}</span>
        </el-form-item>
      </template>
    </el-form>
    <div class="description-panel" v-if="configType === 'publish' && configData.job.sink.storageStrategy !== 'SAVE'" style="right:30%;top:30px;" >
      <div class="question">
        <i class="el-icon-question"></i>
        <span>无需进行发布配置</span>
      </div>
      <div class="answer">
        <div>发布配置用于定义平台执行（数据采集）调度任务的周期及策略</div>
        <div>该数据发布的落地策略为直连源库，不会将数据暂存平台，因此平台不会主动执行任何调度任务，故无需进行发布配置</div>
      </div>
    </div>
    <div class="description-panel"  v-else-if="mildEditable">
      <template  v-if="form.scheduleStrategy.scheduleType === 'OUT'">
        <div class="question">
          <i class="el-icon-question"></i>
          <span>调用接口时的身份认证</span>
        </div>
        <div class="answer">
          <span>参考<el-link type="primary" href="http://tapd.oa.com/HR_Platform/markdown_wikis/show/#1220394402000327171" target="_blank">身份认证规范</el-link></span>
        </div>
      </template>
      <template v-else>
        <div class="question">
          <i class="el-icon-question"></i>
          <span>开始时间</span>
        </div>
        <div class="answer">
          <span>对于增量的发布/订阅，若开始时间在上线（提交）时间的10分钟以内，平台会默认在上线时间的10分钟后执行任务</span>
        </div>
      </template>
      <template>
        <div class="question">
          <i class="el-icon-question"></i>
          <span>资源配置</span>
        </div>
        <div class="answer">
          <span>资源有限，请按实际需求配置，否则可能因OOM而导致任务失败</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
import { editable } from 'mixins'
import { convertDateToCron } from 'utils/shared'
import BidsCrontab from 'components/bids-crontab'
import ClipboardJS from 'clipboard'
import { conf } from 'utils'

export default {
  name: 'bids-other-setting',
  components: {
    BidsCrontab
  },
  mixins: [editable],
  props: {
    pageStatus: String,
    configType: String, // 读取和修改对应的Vuex数据（发布publish/订阅subscribe）
    currentData: {
      type: Object,
      default: () => ({})
    }
  },
  directives: {
    initSeconds: {
      bind (el, { value: seconds }, vnode) {
        const mins = seconds < 60 ? 1 : (seconds / 60)
        vnode.data.model.callback(mins)
      },
      componentUpdated (el, { value: seconds }, vnode) {
        const mins = seconds < 60 ? 1 : (seconds / 60)
        vnode.data.model.callback(mins)
      }
    }
  },
  computed: {
    ...mapState(['userInfo', 'dt_system']),
    configData() {
      const state = this.$store.state
      return this.configType === 'publish' ? state.currentPublishConfig : state.currentSubscribeConfig
    },
    radioDisabled() {
      return this.currentData.jobStatus === 1
    }
  },
  data() {
    const check2Point = (rule, value, callback) => {
      if (!value.type || !value.interval) {
        return callback(new Error('请设置检查点'))
      }
    }
    return {
      form: { 
        scheduleStrategy: { scheduleBeginTime: '', ifScheduleUnfinished: '', scheduleType: '', scheduleAtCron: '', scheduleUnit: '' }, // 调度策略
        timeoutStrategy: { enabled: false, timeoutSeconds: 60 }, // 超时策略
        checkpoint: { enabled: false, interval: 60 }, // 检查点
        checkStrategy: { enabled: false, minRows: 0, maxRows: 1000000 }, // 数据行数校验
        retryStrategy: { enabled: true, attempts: 1, delay: 1 }, // 失败策略,
        resources: { cores: 1, parallelism: 1, memoryMB: 1024 }
      },
      rules: {
        'scheduleStrategy.scheduleBeginTime': [{ required: true, message: '请选择开始时间', trigger: 'change' }],
        'scheduleStrategy.scheduleType': [{ required: true, message: '请选择调度类型', trigger: 'change' }],
        'scheduleStrategy.scheduleAtCron': [{ required: true, message: '请选择调度周期', trigger: 'change' }],
        checkpoint: [{ required: true, validator: check2Point, trigger: 'blur' }],
        'timeoutStrategy.enabled': [{ required: true, message: '请设置超时策略', trigger: 'change' }],
        // 'scheduleStrategy.ifScheduleUnfinished': [{ required: true, message: '请设置调度处理', trigger: 'change' }],
        'checkStrategy.enabled': [{ required: true, message: '请设置数据行校验', trigger: 'change' }],
        'retryStrategy.enabled': [{ required: true, message: '请设置失败策略', trigger: 'change' }]
      },
      checkpointOptions: [
        // { value: 'rocksDB', label: 'RocksDB' }, 
        { value: 'HDFS', label: 'HDFS' }
      ],
      keyPaths: {
        scheduleBeginTime: ['setting', 'scheduleStrategy', 'scheduleBeginTime'],
        scheduleAtCron: ['setting', 'scheduleStrategy', 'scheduleAtCron'],
        scheduleUnit: ['setting', 'scheduleStrategy', 'scheduleUnit'],
        ifScheduleUnfinished: ['setting', 'scheduleStrategy', 'ifScheduleUnfinished'],
        'timeoutStrategy.enabled': ['setting', 'timeoutStrategy', 'enabled'],
        timeoutSeconds: ['setting', 'timeoutStrategy', 'timeoutSeconds'],
        'checkpoint.type': ['setting', 'checkpoint', 'type'],
        'checkpoint.interval': ['setting', 'checkpoint', 'interval'],
        'checkStrategy.enabled': ['setting', 'checkStrategy', 'enabled'],
        minRows: ['setting', 'checkStrategy', 'minRows'],
        maxRows: ['setting', 'checkStrategy', 'maxRows'],
        'retryStrategy.enabled': ['setting', 'retryStrategy', 'enabled'],
        delay: ['setting', 'retryStrategy', 'delay'],
        attempts: ['setting', 'retryStrategy', 'attempts'],
        cores: ['setting', 'resources', 'cores'],
        memoryMB: ['setting', 'resources', 'memoryMB'],
        parallelism: ['setting', 'resources', 'parallelism']
      },
      schedulePopoverVisible: false,
      value: 1
    }
  },
  created() {
    this.updatePageData()
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    updatePageData() {
      const { checkpoint, retryStrategy, checkStrategy, timeoutStrategy, scheduleStrategy, resources } = this.configData.setting
      const urlPart = this.strictEditable ? '{configId}/{version}' : `${this.configData.configId}/${this.configData.version}`
      const runJobUrl = conf('esb').main + `/thirdpart/jobs/${urlPart}/runJob`
      const getStatusUrl = conf('esb').main + `/thirdpart/jobs/${urlPart}/jobStatus`
      // 兼容旧数据
      const { timeoutSeconds } = timeoutStrategy
      timeoutStrategy.timeoutSeconds = timeoutSeconds < 60 ? 60 : timeoutSeconds
      this.form = { 
        checkpoint: { type: checkpoint.type, interval: checkpoint.interval / 1000 }, 
        retryStrategy: { ...retryStrategy }, 
        checkStrategy: { ...checkStrategy }, 
        timeoutStrategy: { ...timeoutStrategy }, 
        scheduleStrategy: { ...scheduleStrategy },
        resources: { ...resources },
        runJobUrl,
        getStatusUrl
      }
    },
    handleBeginTimeChange(val) {
      this.updateDataValue(val, this.keyPaths['scheduleBeginTime'])
      const collectType = this.configData.collectType
      if (collectType === 2) {
        // 增量情况下，需要将开始时间转换成具体cron
        const cron = convertDateToCron(val)
        this.updateDataValue(cron, this.keyPaths['scheduleAtCron'])
        this.updateDataValue('', this.keyPaths['scheduleUnit'])
      }
    },
    updateDataValue(value, keyPath) {
      const payload = { type: this.configType, keyPath, value }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleScheduleTypeChange(value) {
      const payload = { type: this.configType, keyPath: ['setting', 'scheduleStrategy', 'scheduleType'], value }
      this.UPDATE_CONFIG_ITEM(payload)
      if (value === 'OUT') {
        this.form.scheduleStrategy.scheduleBeginTime = ''
        this.handleBeginTimeChange('')
        this.crontabFill({ scheduleAtCron: '', scheduleUnit: '' })
      } else {
        !this.form.scheduleStrategy.scheduleUnit && this.crontabFill({ scheduleAtCron: '0 0 0 * * ? *', scheduleUnit: 'Day' })
      }
    },
    crontabFill(obj) {
      this.form.scheduleStrategy.scheduleAtCron = obj.scheduleAtCron
      this.form.scheduleStrategy.scheduleUnit = obj.scheduleUnit
      this.UPDATE_CONFIG_ITEM({ type: this.configType, keyPath: ['setting', 'scheduleStrategy', 'scheduleAtCron'], value: obj.scheduleAtCron })
      this.UPDATE_CONFIG_ITEM({ type: this.configType, keyPath: ['setting', 'scheduleStrategy', 'scheduleUnit'], value: obj.scheduleUnit })
      this.schedulePopoverVisible = false
    },
    handleCopyUrl(className) {
      const clipboard = new ClipboardJS(className)
      const _this = this
      clipboard.on('success', function () {
        _this.$sdc.toast('复制成功')
      })
      clipboard.on('error', function () {
        _this.$sdc.toast('复制失败')
      })
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    timeoutSecondsChange(mins) {
      const seconds = mins * 60
      this.form.timeoutStrategy.timeoutSeconds = seconds
      this.updateDataValue(seconds, this.keyPaths['timeoutSeconds'])
    },
    formatTimeoutSeconds(seconds) {
      return seconds < 60 ? 1 : (seconds / 60)
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/bids-other-setting.less";
  .suffix-open {
    line-height: 34px;
  }
</style>

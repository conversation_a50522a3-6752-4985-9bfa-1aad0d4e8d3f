<template>
  <div class="dh-virtual-select">
    <vxe-pulldown ref="down" :transfer="true">
      <template #default>
        <el-input
          v-model="val"
          size="mini"
          :disabled="disabled"
          :placeholder="placeholder"
          :clearable="clearable"
          :readonly="!filterable"
          @focus="focusHandle"
          @blur="blurHandle"
          @input="inputHandle"
          @clear="clearHandle"
        ></el-input>
      </template>
      <template #dropdown>
        <div class="dropdown-wrapper">
          <vxe-list v-if="dataList.length !== 0" :height="maxHeight" :data="dataList" :auto-resize="true">
              <template slot-scope="{ items }">
                <div
                  v-for="(item, index) in items"
                  :key="index"
                  class="item"
                  :class="{
                    'is-disabled': item.disabled
                  }"
                  @click="selectItemHandle(item)"
                >
                  <slot :item="item">
                    <span>{{ item[option.label] }}</span>
                  </slot>
                </div>
              </template>
          </vxe-list>
          <div v-else class="is-empty">
            暂无数据
          </div>
        </div>
      </template>
    </vxe-pulldown>
  </div>
</template>

<script>
export default {
  name: 'DhVirtualSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // 下拉数据列表
    list: {
      type: Array,
      default: () => ([])
    },
    // 自定义 option
    option: {
      type: Object,
      default: () => ({
        label: 'label',
        value: 'value'
      })
    },
    // 下拉面板的高度
    height: {
      type: Number,
      default: 200
    },
    // 提示
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否可清除
    clearable: {
      type: Boolean,
      default: false
    },
    // 是否可过滤
    filterable: { 
      type: Boolean,
      default: false
    },
    // 自定义搜索方法
    filterMethod: {
      type: Function,
      default(query, item) {
        return item[this.option.label].indexOf(query) > -1
      }
    }
  },
  data() {
    return {
      val: '',
      dataList: []
    }
  },

  computed: {
    maxHeight() {
      return Math.min(this.dataList.length * 34, this.height)
    }
  },

  watch: {
    value: {
      immediate: true,
      handler(val) {
        console.log('[ val ] >', val)
        this.val = val
      }
    },
    list: {
      immediate: true,
      handler(val) {
        if (val) {
          this.dataList = val
        }
      }
    }
  },

  methods: {
    // 输入框获取焦点
    focusHandle(event) {
      this.$refs.down.showPanel()
      this.$emit('focus', event)
    },
    blurHandle(event) {
      this.$emit('blur', event)
    },
    clearHandle() {
      this.val = ''
      this.$emit('clear')
    },
    // 进行输入
    inputHandle() {
      const { val } = this
      this.dataList = val ? this.list.filter(item => this.filterMethod(val, item)) : this.list
    },
    // 选中数据项
    selectItemHandle(item) {
      if (item.disabled) {
        return
      }
      const value = item[this.option.value]
      this.val = value
      this.$refs.down.hidePanel()
      this.$emit('change', value)
    }
  }
}
</script>

<style lang="less" scoped>
  .dh-virtual-select {
    position: relative;
  }

  .dropdown-wrapper {
    margin: 5px 0;
    padding: 6px 0;
    box-sizing: border-box;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);

    .item {
      font-size: 14px;
      padding: 0 20px;
      position: relative;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #606266;
      height: 34px;
      line-height: 34px;
      box-sizing: border-box;
      cursor: pointer;

      &:hover {
        background: #f5f7fa;
      }

      &.is-disabled {
        color: #c0c4cc;
        background: none;
        cursor: not-allowed;
      }
    }

    .is-empty {
      text-align: center;
      line-height: 34px;
    }
  }
</style>

/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-12-15 11:45:40
 */
const path = require('path')
const resolve = dir => path.join(__dirname, dir)
const svgPath = resolve('./src/assets/svg')
const { title } = require('./package')

module.exports = {
  configureWebpack: {
    name: title,
    entry: ['@babel/polyfill', './src/main.js'],
    resolve: {
      alias: {
        'assets': '@/assets',
        'components': '@/components',
        'config': '@/config',
        'models': '@/models',
        'mixins': '@/mixins',
        'plugins': '@/plugins',
        'services': '@/services',
        'utils': '@/utils',
        'views': '@/views',
        'sdc-core': '@tencent/sdc-core',
        'sdc-vue': '@tencent/sdc-vue',
        'sdc-webui': '@tencent/sdc-webui',
        'sdc-theme': '@tencent/sdc-theme'
      }
    }
  },
  // 添加对svg的自定义解析
  chainWebpack: (config) => {
    config.module.rule('svg').exclude.add(svgPath)
    config.module.rule('icon').test(/\.svg$/)
      .include.add(svgPath).end() // 回退上下文
      .use('svg-sprite-loader').loader('svg-sprite-loader')
      .options({ symbolId: 'icon-[name]' }).end()
  },
  // 指定Runtime + Compile来编译组件
  runtimeCompiler: true,
  assetsDir: '.',
  publicPath: '/bidsplus/',
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [
        path.resolve(__dirname, './src/assets/css/vars.less'),
        path.resolve(__dirname, './src/assets/css/mixins.less')
      ]
    }
  },
  devServer: {
    hot: false,
    host: '127.0.0.1',
    open: true,
    disableHostCheck: true,
    port: 9080,
    proxy: {
      '/hr-bidsplus-config/api': {
        // target: 'http://dev-ntsgw.woa.com/api/sso/hr-bidsplus-config',
        target: 'http://***********:18089', // dingjia
        changeOrigin: true,
        pathRewrite: {
          '/hr-bidsplus-config/api': ''
        },
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          staffId: 78771,
          staffname: 'kunkkawang'
        }
      },
      '/bidsplus-executor/api': {
        target: 'http://dev.ntsgw.oa.com/api/pub/bidsplus-executor/bidsplus',
        changeOrigin: true,
        pathRewrite: {
          '/bidsplus-executor/api': ''
        }
      },
      '/front-repo/api': {
        target: 'http://dev.ntsgw.oa.com/api/sso/bidsplus-frontrepo',
        changeOrigin: true,
        pathRewrite: {
          '/front-repo/api': ''
        }
      },
      '/dolphinscheduler/api': {
        target: 'http://demo.ntsgw.oa.com/dolphinscheduler',
        changeOrigin: true,
        pathRewrite: {
          '/dolphinscheduler/api': ''
        }
      },
      '/hr-data-jobservice/api': {
        target: 'http://demo.ntsgw.oa.com/dolphinscheduler',
        changeOrigin: true,
        pathRewrite: {
          '/hr-data-jobservice/api': ''
        }
      }
    }
  }
}

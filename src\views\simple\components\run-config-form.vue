<template>
  <el-form class="base-width" ref="form" :model="form" :rules="rules" label-width="104px" size="small">
    <el-form-item label="触发方式" prop="scheduleStrategy.scheduleType">
      <span v-if="isView">{{ form.scheduleStrategy.scheduleType === 'REPEAT' ? '调度触发' : '外部触发' }}</span>
      <el-radio-group v-model="form.scheduleStrategy.scheduleType" v-else>
        <el-radio label="OUT">外部触发</el-radio>
        <el-radio label="REPEAT">调度触发</el-radio>
      </el-radio-group>
    </el-form-item>
    <template v-if="form.scheduleStrategy.scheduleType === 'OUT'">
      <!-- runJobUrl另起字段吧 -->
      <el-form-item label="任务触发接口">
        <el-input v-model="form.runJobUrl"></el-input>
      </el-form-item>
      <el-form-item label="状态查询接口">
        <el-input v-model="form.runJobUrl"></el-input>
      </el-form-item>
    </template>
    <el-form-item label="调度周期" prop="scheduleStrategy.scheduleAtCron" v-if="form.scheduleStrategy.scheduleType === 'REPEAT'">
      <span v-if="isView">{{ form.scheduleStrategy.scheduleUnit }}</span>
      <el-popover
        v-else
        placement="right"
        trigger="click"
        @show="$refs['crontab'].setCronTab(form.scheduleStrategy.scheduleUnit, form.scheduleStrategy.scheduleAtCron)"
        v-model="schedulePopoverVisible">
        <bids-crontab ref="crontab" @confirm="crontabFill" @cancel="schedulePopoverVisible=false"></bids-crontab>
        <el-input v-model="form.scheduleStrategy.scheduleAtCron" slot="reference" readonly class="custom-input"></el-input>
      </el-popover>
    </el-form-item>
    
    <el-row :gutter="20">
      <el-col :span="14">
        <el-form-item label="超时策略" prop="timeoutStrategy.action">
          <span v-if="isView">666666</span>
          <el-radio-group v-model="form.timeoutStrategy.action" v-else>
            <el-radio label="wait">等待</el-radio>
            <el-radio label="retry">重试</el-radio>
            <el-radio label="stop">停止</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="超时时长" prop="timeoutStrategy.timeoutMins" v-if="form.timeoutStrategy.action === 'wait'">
          <span v-if="isView">{{ form.timeoutStrategy.timeoutMins }}</span>
          <el-input v-model="form.timeoutStrategy.timeoutMins" v-else></el-input>
        </el-form-item>
        <el-form-item label="重试次数" prop="timeoutStrategy.retryCount" v-if="form.timeoutStrategy.action === 'retry'">
          <span v-if="isView">{{ form.timeoutStrategy.retryCount }}</span>
          <el-input v-model="form.timeoutStrategy.retryCount" v-else></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="14">
        <el-form-item label="失败策略" prop="failureStrategy.action">
          <span v-if="isView">{{ form.failureStrategy.action === 'retry' ? '重试' : '停止' }}</span>
          <el-radio-group v-model="form.failureStrategy.action" v-else>
            <el-radio label="stop">停止</el-radio>
            <el-radio label="retry">重试</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="重试次数" prop="failureStrategy.retryCount" v-if="form.failureStrategy.action === 'retry'">
          <span v-if="isView">{{ form.failureStrategy.retryCount }}</span>
          <el-input v-model="form.failureStrategy.retryCount" v-else></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="资源配置" prop="resources" required>
      <span v-if="isView">
        CPU核数{{form.resources.cores}}，内存大小{{form.resources.memoryMB}}M，并行度{{form.resources.parallelism}}
      </span>
      <template v-else>
        <span>CPU核数<el-input-number v-model="form.resources.cores" size="mini" :precision="0" :min="1" :max="dt_system.maxCores" style="width:100px;"/>，</span>
        <span>内存大小<el-input-number v-model="form.resources.memoryMB" size="mini" :precision="0" :min="1024" :max="dt_system.maxMemoryMB" style="width:100px;"/>M，</span>
        <span>并行度<el-input-number v-model="form.resources.parallelism" disabled size="mini" :precision="0" :min="1" :max="dt_system.maxParallelism" style="width:100px;"/></span>
      </template>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
import BidsCrontab from 'components/bids-crontab'

export default {
  components: {
    BidsCrontab
  },
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        scheduleStrategy: { scheduleBeginTime: '', ifScheduleUnfinished: '', scheduleType: '', scheduleAtCron: '', scheduleUnit: '' }, // 调度策略
        timeoutStrategy: { action: null, timeoutMins: 60, retryCount: 1, retryDelay: 1000 }, // 超时策略
        failureStrategy: { action: null, retryCount: 1, retryDelay: 1000 }, // 失败策略,
        resources: { cores: 1, parallelism: 1, memoryMB: 1024 } // 资源配置
      },
      rules: {
        'scheduleStrategy.scheduleType': [
          { required: true, message: '请选择触发方式', trigger: 'change' }
        ],
        'scheduleStrategy.scheduleAtCron': [
          { required: true, message: '请设置调度周期', trigger: 'change' }
        ],
        'timeoutStrategy.action': [
          { required: true, message: '请选择超时策略', trigger: 'change' }
        ],
        'timeoutStrategy.timeoutMins': [
          { required: true, message: '请输入超时时长', trigger: 'change' }
        ],
        'timeoutStrategy.retryCount': [
          { required: true, message: '请输入重试次数', trigger: 'blur' }
        ],
        'failureStrategy.action': [
          { required: true, message: '请选择失败策略', trigger: 'change' }
        ],
        'failureStrategy.retryCount': [
          { required: true, message: '请输入重试次数', trigger: 'blur' }
        ]
      },
      schedulePopoverVisible: false
    }
  },
  computed: {
    ...mapState(['dt_system'])
  },
  methods: {
    crontabFill(cronTab) {
      this.form.scheduleStrategy.scheduleAtCron = cronTab.scheduleAtCron
      this.form.scheduleStrategy.scheduleUnit = cronTab.scheduleUnit
      this.schedulePopoverVisible = false
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    getSubData() {
      return { ...this.form }
    },
    setData(data) {
      this.form = {}
    }
  }
}
</script>

<style lang="less" scoped>

</style>

<template>
  <el-form class="base-width" ref="form" :model="form" :rules="rules" label-width="104px" size="small">
    <el-form-item label="触发方式" prop="scheduleStrategy.scheduleType">
      <span v-if="isView">{{ form.scheduleStrategy.scheduleType === 'REPEAT' ? '调度触发' : '外部触发' }}</span>
      <el-radio-group v-model="form.scheduleStrategy.scheduleType" @change="handleScheduleTypeChange" v-else>
        <!-- <el-radio label="OUT">外部触发</el-radio> -->
        <el-radio label="REPEAT">调度触发</el-radio>
      </el-radio-group>
    </el-form-item>
    <template v-if="form.scheduleStrategy.scheduleType === 'OUT'">
      <el-form-item label="任务触发接口">
        <div class="urlPanel">
          <span :data-clipboard-text="runJobUrl" id="runJobUrl">{{ runJobUrl }}</span>
          <span style="margin-left:5px;">[POST]</span>
          <div class="event-icon copy-icon runJobUrl" data-clipboard-target="#runJobUrl" @click="handleCopyUrl('.runJobUrl')"><i class="el-icon-document-copy"></i></div>
        </div>
      </el-form-item>
      <el-form-item label="状态查询接口">
        <div class="urlPanel">
          <span :data-clipboard-text="getStatusUrl" id="getStatusUrl">{{getStatusUrl}}</span>
          <span style="margin-left:5px;">[GET]</span>
          <div class="event-icon copy-icon getStatusUrl" data-clipboard-target="#getStatusUrl" @click="handleCopyUrl('.getStatusUrl')"><i class="el-icon-document-copy"></i></div>
        </div>
      </el-form-item>
    </template>
    <el-form-item label="调度周期" prop="scheduleStrategy.scheduleAtCron" v-if="form.scheduleStrategy.scheduleType === 'REPEAT'">
      <span v-if="isView">{{ form.scheduleStrategy.scheduleAtCron }}</span>
      <el-popover
        v-else
        placement="right"
        trigger="click"
        @show="$refs['crontab'].setCronTab(form.scheduleStrategy.scheduleUnit, form.scheduleStrategy.scheduleAtCron)"
        v-model="schedulePopoverVisible">
        <bids-crontab ref="crontab" @confirm="crontabFill" @cancel="schedulePopoverVisible=false"></bids-crontab>
        <el-input v-model="form.scheduleStrategy.scheduleAtCron" slot="reference" readonly class="custom-input"></el-input>
      </el-popover>
    </el-form-item>
    <el-form-item label="开始时间" prop="scheduleStrategy.scheduleBeginTime" v-if="form.scheduleStrategy.scheduleType === 'REPEAT'">
      <span v-if="isView">{{ form.scheduleStrategy.scheduleBeginTime }}</span>
      <el-date-picker
        v-model="form.scheduleStrategy.scheduleBeginTime"
        type="datetime"
        align="center"
        value-format="yyyy-MM-dd HH:mm:ss"
        placeholder="选择日期时间"
        v-else>
      </el-date-picker>
    </el-form-item>
    
    <el-row :gutter="20">
      <el-col :span="14">
        <el-form-item label="超时策略" prop="timeoutStrategy.action">
          <span v-if="isView">{{ getActionLabel(form.timeoutStrategy.action) }}</span>
          <el-radio-group v-model="form.timeoutStrategy.action" v-else>
            <el-radio label="wait">等待</el-radio>
            <el-radio label="retry">重试</el-radio>
            <el-radio label="stop">停止</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="超时时长" prop="timeoutStrategy.timeoutMins" v-if="form.timeoutStrategy.action === 'wait'">
          <span v-if="isView">{{ form.timeoutStrategy.timeoutMins }}</span>
          <el-input v-model.number="form.timeoutStrategy.timeoutMins" v-else></el-input>
        </el-form-item>
        <el-form-item label="重试次数" prop="timeoutStrategy.retryCount" v-if="form.timeoutStrategy.action === 'retry'">
          <span v-if="isView">{{ form.timeoutStrategy.retryCount }}</span>
          <el-input v-model.number="form.timeoutStrategy.retryCount" v-else></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="14">
        <el-form-item label="失败策略" prop="failureStrategy.action">
          <span v-if="isView">{{ form.failureStrategy.action === 'retry' ? '重试' : '停止' }}</span>
          <el-radio-group v-model="form.failureStrategy.action" v-else>
            <el-radio label="stop">停止</el-radio>
            <el-radio label="retry">重试</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="重试次数" prop="failureStrategy.retryCount" v-if="form.failureStrategy.action === 'retry'">
          <span v-if="isView">{{ form.failureStrategy.retryCount }}</span>
          <el-input v-model.number="form.failureStrategy.retryCount" v-else></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="资源配置" prop="resources" required>
      <span v-if="isView">
        CPU核数{{form.resources.cores}}，内存大小{{form.resources.memoryMB}}M，并行度{{form.resources.parallelism}}
      </span>
      <template v-else>
        <span>CPU核数<el-input-number v-model="form.resources.cores" size="mini" :precision="0" :min="1" :max="dt_system.maxCores" style="width:100px;"/>，</span>
        <span>内存大小<el-input-number v-model="form.resources.memoryMB" size="mini" :precision="0" :min="1024" :max="dt_system.maxMemoryMB" style="width:100px;"/>M，</span>
        <span>并行度<el-input-number v-model="form.resources.parallelism" disabled size="mini" :precision="0" :min="1" :max="dt_system.maxParallelism" style="width:100px;"/></span>
      </template>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
import BidsCrontab from 'components/bids-crontab'
import ClipboardJS from 'clipboard'
import { conf } from 'utils'

export default {
  components: {
    BidsCrontab
  },
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        scheduleStrategy: { 
          scheduleBeginTime: '', 
          ifScheduleUnfinished: '', 
          scheduleType: '', 
          scheduleAtCron: '', 
          scheduleUnit: '',
          scheduleDependences: null,
          schedulePeriod: null
        }, // 调度策略
        timeoutStrategy: { action: null, timeoutMins: 60, retryCount: 1, retryDelay: 1000 }, // 超时策略
        failureStrategy: { action: null, retryCount: 1, retryDelay: 1000 }, // 失败策略,
        resources: { cores: 1, parallelism: 1, memoryMB: 1024 } // 资源配置
      },
      rules: {
        'scheduleStrategy.scheduleType': [
          { required: true, message: '请选择触发方式', trigger: 'change' }
        ],
        'scheduleStrategy.scheduleAtCron': [
          { required: true, message: '请设置调度周期', trigger: 'change' }
        ],
        'scheduleStrategy.scheduleBeginTime': [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        'timeoutStrategy.action': [
          { required: true, message: '请选择超时策略', trigger: 'change' }
        ],
        'timeoutStrategy.timeoutMins': [
          { required: true, message: '请输入超时时长', trigger: 'change' }
        ],
        'timeoutStrategy.retryCount': [
          { required: true, message: '请输入重试次数', trigger: 'blur' }
        ],
        'failureStrategy.action': [
          { required: true, message: '请选择失败策略', trigger: 'change' }
        ],
        'failureStrategy.retryCount': [
          { required: true, message: '请输入重试次数', trigger: 'blur' }
        ]
      },
      schedulePopoverVisible: false
    }
  },
  computed: {
    ...mapState(['dt_system']),
    runJobUrl() {
      const urlPart = this.isView ? `${this.$route.query.configId}/v${this.$route.query.version}` : '{configId}/{version}'
      const runJobUrl = conf('esb').main + `/thirdpart/jobs/${urlPart}/runJob`
      return runJobUrl
    },
    getStatusUrl() {
      const urlPart = this.isView ? `${this.$route.query.configId}/v${this.$route.query.version}` : '{configId}/{version}'
      const getStatusUrl = conf('esb').main + `/thirdpart/jobs/${urlPart}/jobStatus`
      return getStatusUrl
    }
  },
  methods: {
    crontabFill(cronTab) {
      this.form.scheduleStrategy.scheduleAtCron = cronTab.scheduleAtCron
      this.form.scheduleStrategy.scheduleUnit = cronTab.scheduleUnit
      this.schedulePopoverVisible = false
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    setData(data) {
      this.form = {
        scheduleStrategy: data.scheduleStrategy,
        timeoutStrategy: data.timeoutStrategy,
        failureStrategy: data.failureStrategy,
        resources: data.resources
      }
    },
    getSubData() {
      return {
        scheduleStrategy: this.form.scheduleStrategy,
        timeoutStrategy: this.form.timeoutStrategy,
        failureStrategy: this.form.failureStrategy,
        resources: this.form.resources
      }
    },
    handleCopyUrl(className) {
      const clipboard = new ClipboardJS(className)
      const _this = this
      clipboard.on('success', function () {
        _this.$sdc.toast('复制成功')
      })
      clipboard.on('error', function () {
        _this.$sdc.toast('复制失败')
      })
    },
    handleScheduleTypeChange(value) {
      if (value === 'OUT') {
        this.form.scheduleStrategy.scheduleBeginTime = ''
        this.form.scheduleStrategy.scheduleAtCron = ''
        this.form.scheduleStrategy.scheduleUnit = ''
      }
    },
    getActionLabel(action) {
      return {
        wait: '等待',
        retry: '重试',
        stop: '停止'
      }[action]
    }
  }
}
</script>

<style lang="less" scoped>
.urlPanel {
  width: 420px;
  position: relative;
  border-radius: 6px;
  line-height: 24px;
  padding: 3px 35px 3px 10px;
  border: 1px solid #dcdcdc;
  word-break:break-all;
  .event-icon.copy-icon {
    position: absolute;
    top: 5px;
    right: 10px;
  }
}
</style>

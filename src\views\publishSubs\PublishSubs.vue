<!--
 * @Description: 数据发布-管理/查看订阅页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-31 15:01:42
-->
<template>
  <div class="publish-subs">
    <div class="page-header">
     <el-page-header @back="goBack" :content="'数据的订阅情况'"></el-page-header>
    </div>
    <div>
      <el-tabs class="version-tabs" v-model="currentVersion" type="card" @tab-click="handleVersionChange">
        <el-tab-pane v-for="item in data" :key="item.version" :name="item.version">
          <span slot="label" class="verson-tab-label">
            <span class="label-text">{{item.version}}</span>
            <i style="color:#0ad0b6;" class="el-icon-success" v-if="item.jobStatus === 1"></i>
            <i style="color:#ACACAC;" class="el-icon-remove" v-else></i>
          </span>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="version-info-panel">
      <div class="info-row">
        <span class="text-item">
          <span>当前版本：</span>
          <span>{{currentData.version}}</span>
        </span>
        <span class="text-item">
          <span>当前状态：</span>
          <span><el-tag v-if="currentData.version" effect="dark" size="small" :type="currentData.jobStatus === 1 ? 'success' : 'info'">{{currentData.jobStatus === 1 ? "运行中" : "已下线"}}</el-tag></span>
        </span>
        <span class="text-item">
          <span>负责/维护人：</span>
          <span>{{formatterOwners(currentData)}}</span>
        </span>
      </div>
      <div class="info-row">
        <span class="text-item">
          <span>所属应用：</span>
          <span>{{currentData.appName}}</span>
        </span>
        <span class="text-item">
          <span>发布名称：</span>
          <span>{{currentData.interfaceName}}</span>
        </span>
        <span class="text-item">
          <span>发布编码：</span>
          <span>{{currentData.interfaceCode}}</span>
        </span>
        <span class="text-item">
          <span>发布描述：</span>
          <span>{{currentData.interfaceDesc}}</span>
        </span>
      </div>
      <div class="info-row">
        <span class="text-item">
          <span>采集类型：</span>
          <span>{{formatterCollectType(currentData)}}</span>
        </span>
        <span class="text-item">
          <span>开始时间：</span>
          <span>{{formatterScheduleBeginTime(currentData)}}</span>
        </span>
        <span class="text-item">
          <span>发布周期：</span>
          <span>{{formatterScheduleAtCron(currentData)}}</span>
        </span>
      </div>
      <div class="operate-item" v-if="currentData.version">
        <el-button icon="el-icon-chat-dot-square" size="mini" v-if="currentData.auth_type === 'update'" @click="handleCommunicate">一键拉群</el-button>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="subsTableData"
        ref="subsTable"
        v-loading="subLoading"
        :header-cell-style="{'border-top': '1px solid #EBEEF5' , height: '60px'}"
        style="width: 100%">
        <el-table-column
          type="index"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in subTableColumn" :key="index"
          :prop="item.prop"
          :label="item.label"
          :formatter="item.formatter"
          :min-width="item.minWidth || item.width"
          :show-overflow-tooltip="true"
          align="center">
          <template v-if="item.prop === 'subInterfaceName'" v-slot="{ row }">
            <a class="anchor" @click="handleGoSub(row)" @contextmenu.prevent.stop="rightClick($event, subscribeUrl(row))">{{ row[item.prop] }}</a>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <bids-popover :rightClickFlag.sync="rightClickFlag" :contextMenu="contextMenu" :clientX="clientX" :clientY="clientY" @handleGoNewTab="handleGoNewTab"></bids-popover>
  </div>
</template>
<script>
import { queryData, querySubs } from 'services/publish.service'
import { formatter, popoverStatus } from 'mixins'
import { BidsPopover } from 'components'

export default {
  mixins: [formatter, popoverStatus],
  components: {
    BidsPopover
  },
  data() {
    return {
      currentVersion: '',
      subsTableData: [],
      data: [],
      currentData: {},
      subLoading: false,
      subTableColumn: [
        // { prop: 'configId', label: '配置ID', minWidth: '70' },
        // { prop: 'version', label: '版本', minWidth: '60' },
        { prop: 'subAppName', label: '所属应用', minWidth: '100' },
        { prop: 'subInterfaceName', label: '订阅名称', minWidth: '120' },
        { prop: 'subInterfaceDesc', label: '订阅描述', minWidth: '120' },
        // { prop: 'subDataSinkFormat', label: '数据类型', minWidth: '80' },
        // { prop: 'subType', label: '接收类型', minWidth: '80', formatter: this.formatterCollectType },
        { prop: 'subBeginTime', label: '开始时间', minWidth: '120' },
        { prop: 'subSchedulePeriod', label: '订阅周期', minWidth: '95', formatter: this.formatterScheduleAtCron },
        { prop: 'subStatus', label: '订阅状态', minWidth: '80', formatter: this.formatterJobStatus },
        { prop: 'subOwners', label: '订阅方负责人', minWidth: '170', formatter: this.formatterOwners }
      ]
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleVersionChange(tab) {
      this.currentData = this.data.find(item => item.version === tab.name)
      this.fetchSubs(this.currentData.configId, this.currentData.version)
    },
    fetchData() {
      const params = { configId: this.$route.query.configId }
      queryData(params).then(res => {
        this.data = res
        if (res.length > 0) {
          this.currentData = res.find(item => item.version === this.currentVersion) || res.find(item => item.jobStatus === 1) || res[res.length - 1]
          this.currentVersion = this.currentData.version
          this.fetchSubs(this.currentData.configId, this.currentData.version)
        }
      }).catch(res => {
        this.data = []
        this.$message.error('获取数据详情失败！')
        this.currentVersion = ''
        this.pubLoading = false
      })
    },
    handleCommunicate() {
      this.$message.info('功能待开发...')
    },
    fetchSubs(configId, version) {
      const params = { configId, version }
      const postData = { criteria: [], paginator: {} }
      this.subLoading = true
      querySubs(params, postData).then(res => {
        res = res || {}
        this.subsTableData = res.content || []
        this.subLoading = false
      }).catch(res => {
        this.subsTableData = []
        this.$message.error('获取数据（' + version + '版本）的订阅情况失败！')
        this.subLoading = false
      })
    },
    handleGoSub(row) {
      this.$router.push({ path: '/subscribe/edit', query: { configId: row.configId } })
    },
    subscribeUrl(row) {
      const { href } = this.$router.resolve({ path: '/subscribe/edit', query: { configId: row.configId } })
      return href
    }
  }
}
</script>
<style lang="less" scoped>
  @import "~assets/css/publish-subs.less";
</style>

.bids-datastruct-form-part{
  .item-right {
    float: right;
    line-height: 1;
  }
  .config-item {
    position: relative;
    margin-top: -12px;
    margin-bottom: 22px;
    .header {
      height: 36px;
      &-left {
        line-height: 36px;
        vertical-align: middle;
      }
      &-right {
        float: right;
      }
    }
    .panel {
      margin-top: 10px;
      height: 400px;
      display: flex;
      border: 1px solid #ebeef5;
      .custom-tabs {
        height: 400px;
        width: 100%;
        .el-tabs__content {
          height: 100%;
          overflow-y: auto;
        }
      }
    }
  }
  .label-required {
    &::before {
      content: '*';
      color: #F81D22;
      margin-right: 4px;
    }
  }
  .scan-table-dialog {
    .el-dialog__body {
      padding: 20px 40px;
    }
    .el-form-item {
      margin-bottom: 15px;
    }
    .el-input {
      width: 300px;
    }
  }
  
  .upload-table-dialog {
    .el-dialog__body {
      padding: 20px 40px;
    }
    .el-form-item {
      margin-bottom: 15px;
    }
    .el-input {
      width: 300px;
    }
  }
}
export default {
  data () {
    return {
      rightClickFlag: false,
      clientX: 0,
      clientY: 0,
      contextMenu: [{ label: '', value: '' }]
    }
  },
  methods: {
    rightClick (e, url) {
      this.clientX = e.clientX
      this.clientY = e.clientY
      this.contextMenu = [{ label: `打开新标签跳转`, value: url }, { label: '关闭', value: 'exit' }]
      this.rightClickFlag = true
    },
    handleGoNewTab(value, type) {
      if (value !== 'exit') {
        window.open(value, '_blank')
      }
      this.rightClickFlag = false
    }
  }
}

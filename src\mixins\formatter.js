/*
 * @Description: 格式化内容, 用于返回单元项的各属性值需经一定的转化显示在Table上
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-30 17:37:56
 */
import { mapState } from 'vuex'
import { isDef } from 'utils/shared'

export default {
  computed: {
    ...mapState(['dt_collectType', 'dt_jobStatus', 'dt_scheduleUnit', 'dt_jobState'])
  },
  methods: {
    formatterCollectType(row) {
      let type = row.collectType || row.subType
      if (!type) return ''
      type = Number.parseInt(type)
      return this.dt_collectType.get(type)
    },
    formatterJobStatus(row) {
      // 发布/订阅状态
      let jobStatus = isDef(row.jobStatus) ? row.jobStatus : row.subStatus
      if (!isDef(jobStatus)) return ''
      jobStatus = Number.parseInt(jobStatus)
      return this.dt_jobStatus.get(jobStatus)
    },
    formatterJobExeState(row) {
      // 最近任务运行状态
      let jobExeState = row.jobExecutorStatus
      return this.dt_jobState[jobExeState]
    },
    formatterDataFormat(row) {
      return row.job.sink.dataFormat
    },
    formatterAbstract(row) {
      const point = []
      row.isPrimary && point.push('主键')
      row.isPartitionerHash && point.push('用于分区hash')
      row.enableEncryption && point.push('加密')
      return point.join('、')
    },
    formatterScheduleBeginTime(row) {
      if (!row || !row.setting || !row.setting.scheduleStrategy) return ''
      return row.setting.scheduleStrategy.scheduleBeginTime || '无'
    },
    formatterStorageStrategy(row) {
      const storageStrategy = row.storageStrategy || (row.job && row.job.sink && row.job.sink.storageStrategy)
      return storageStrategy === 'SAVE' ? '暂存平台' : '直连源库'
    },
    formatterScheduleAtCron(row) {
      if (row.collectType === 2) return '持续'
      const schedulingType = row.schedulingType || (row.setting && row.setting.scheduleStrategy && row.setting.scheduleStrategy.scheduleType)
      if (schedulingType === 'OUT') return '外部触发'
      const storageStrategy = row.storageStrategy || (row.setting && row.setting.sink && row.setting.sink.storageStrategy)
      if (storageStrategy === 'NOT_SAVE') return '无'
      const cron = row.schedulePeriodCron || row.scheduleAtCron || row.subSchedulePeriod || (row.setting && row.setting.scheduleStrategy.scheduleAtCron)
      if (!cron) {
        return '无'
      }
      const cronArr = cron.split(' ')
      const weekNumMap = { MON: '一', TUE: '二', WED: '三', THU: '四', FRI: '五', SAT: '六', SUN: '日' }
      const unitMap = ['每秒', '每分钟', '每时:', '每天', '每月']
      const suffix = ['', '', '', '号', '月']
      let period = ''
      let flag = 0
      if (cronArr[5] !== '?') {
        period = '每' + cronArr[5].split(',').map(item => '周' + weekNumMap[item]).join(',')
        flag = 2
      } else {
        flag = cronArr.indexOf('*')
        if (flag === -1) {
          period = `${cronArr[6]}-${cronArr[4].padStart(2, '0')}-${cronArr[3].padStart(2, '0')} `
          flag = 2
        } else if (flag === 2 && cronArr[1].indexOf('/') !== -1) {
          flag = 0
        } else {
          period = unitMap[flag]
          flag = flag - 1
        }
      }
      if (flag > 0) {
        while (flag > -1) {
          const value = cronArr[flag]
          if (value.indexOf(',') !== -1) {
            period += value.split(',').map(item => item.padStart(2, '0') + suffix[flag]).join(',')
          } else {
            period += cronArr[flag].padStart(2, '0') + suffix[flag]
          }
          if (flag === 2 || flag === 1) {
            period += ':'
          }
          flag = flag - 1
        }
      } else if (flag === 0) {
        period = '每' + (cronArr[1] === '*' ? '' : cronArr[1].split('/')[1]) + '分钟' + '（从' + cronArr[1].split('/')[0] + '分开始）'
      }
      return period
    },
    formatterOwners(row) {
      const owners = row.subOwners || row.owners
      if (!owners) return ''
      return owners.map(item => item.staffName).join(';')
    },
    formatterSinkTableName(row) {
      if (row.job.sink.storageStrategy === 'SAVE') {
        const reg = /^PUB_/i
        if (reg.test(row.job.sources[0].tableName)) {
          return row.job.sink.tableName
        }
        return row.job.sources[0].tableName
      }
      return row.job.sink.tableName
    }
  }
}

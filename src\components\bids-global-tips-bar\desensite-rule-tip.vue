<!--
 * @Description: 脱敏规则提示项组件
 * @Autor: 
 * @LastEditTime: 
-->
<template>
  <div class="desensite-rule-tip">
    <el-popover
      placement="top-start"
      trigger="click"
      @show="() => icon = 'el-icon-arrow-up'"
      @hide="() => icon = 'el-icon-arrow-down'">
        <el-table :data="gridData" border max-height="600">
          <el-table-column v-for="item in tableColumns" :key="item.prop" :label="item.label" :prop="item.prop" :width="item.width" show-overflow-tooltip></el-table-column>
        </el-table>
        <div slot="reference" class="tip-text">脱敏规则<i style="margin-left:5px" :class="icon"/></div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'desensite-rule-tip',
  data() {
    return {
      icon: 'el-icon-arrow-down',
      tableColumns: [
        { label: '规则名称', prop: 'name', width: '180' },
        { label: '规则描述', prop: 'desc', width: '250' },
        { label: '示例', prop: 'example', width: '320' },
        { label: '适用字段类型', prop: 'type', width: '170' },
        { label: '说明', prop: 'explain', width: '205' }
      ],
      gridData: [
        {
          name: '替换星号',
          desc: '除前{1}位和后{0}位外提换成*号',
          example: '张三 --> 张*',
          type: 'STRING',
          explain: ''
        },
        {
          name: '替换星号(邮箱)',
          desc: '除前{1}位和后{1}位外提换成*号',
          example: '<EMAIL> --> e*<EMAIL>',
          type: 'STRING',
          explain: ''
        },
        {
          name: '哈希',
          desc: '将内容替换成内容的哈希值',
          example: '',
          type: 'STRING',
          explain: ''
        },
        {
          name: '替换为固定值',
          desc: '将内容替换成固定值',
          example: '0755-812286 --> 0755 888888',
          type: 'ALL',
          explain: ''
        },
        {
          name: '向上取整',
          desc: '整型数值向上取整',
          example: '79 --> 80',
          type: 'INT/DECIMAL/BIGINT',
          explain: ''
        },
        {
          name: '向下取整',
          desc: '整型数值向下取整',
          example: '779 --> 700',
          type: 'INT/DECIMAL/BIGINT',
          explain: ''
        },
        {
          name: '整型加随机值',
          desc: '整型数值加一个随机值',
          example: '79 --> 83',
          type: 'INT/BIGINT',
          explain: ''
        },
        {
          name: '日期向下取整',
          desc: '替换月或日为1',
          example: '2020-12-01 --> 2020-01-01',
          type: 'TIMESTAMP/DATE',
          explain: ''
        },
        {
          name: '加密',
          desc: 'FPE(格式保留加密算法)',
          example: '0003188 --> 9865422',
          type: 'STRING',
          explain: '只支持加密英文与数值，不支持中文，若string类型长度小于4或数值类型小于100000，不做加密处理，返回原值'
        },
        {
          name: '替换为null',
          desc: '将字段值直接设置为null',
          example: '',
          type: 'ALL',
          explain: ''
        },
        {
          name: '随机替换',
          desc: '随机选取一个输入值替换',
          example: '',
          type: 'ALL',
          explain: ''
        },
        {
          name: '随机数',
          desc: '整型数值替换成随机值',
          example: '12 --> 144',
          type: 'INT/DECIMAL/BIGINT',
          explain: ''
        },
        {
          name: '添加随机天数',
          desc: '将当前Date增加一个随机天数',
          example: '2022-12-01 --> 2022-12-29',
          type: 'DATE',
          explain: ''
        },
        {
          name: '添加随机天数',
          desc: '将当前Timestamp增加一个随机天数',
          example: '2022-12-01 12:21:22--> 2022-12-29 12:21:22',
          type: 'TIMESTAMP',
          explain: ''
        },
        {
          name: '添加随机月数',
          desc: '将当前Date增加一个随机月数',
          example: '2022-01-01 --> 2022-03-01',
          type: 'DATE',
          explain: ''
        },
        {
          name: '添加随机月数',
          desc: '将当前Timestamp增加一个随机月数',
          example: '2022-01-01 12:21:22--> 2022-03-01 12:21:22',
          type: 'TIMESTAMP',
          explain: ''
        },
        {
          name: '名字唯一化脱敏',
          desc: '前两个字母(如果前两个是汉字，则取其首字母) + 取前8位(hash（原字段内容）)',
          example: 'zhangsan --> zhabcdefgh',
          type: 'STRING',
          explain: ''
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.desensite-rule-tip {
  font-family: @font-family-yahei;
  color: @color-text-skyblue;
  margin-left: 20px;
  .tip-text {
    cursor: pointer;
  }
}
</style>

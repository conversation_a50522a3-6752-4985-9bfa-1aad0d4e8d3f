import { mapState } from 'vuex'
import { queryApps } from 'services/common.service'

export default {
  data () {
    return {
      applications: []
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
      subWhiteList: (state) => state.subWhiteList,
      currentSubscribeConfig: (state) => state.currentSubscribeConfig
    }),
    // 白名单权限
    whiteListAuthority() {
      return this.subWhiteList.some(item => item === this.currentSubscribeConfig.appKey)
    },
    // 负责人权限
    ownersAuthority() {
      return this.currentSubscribeConfig.owners.some(item => String(item.staffId) === this.userInfo.staffId)
    },
    subEditable() {
      if (this.whiteListAuthority) return true
      return this.ownersAuthority
    },
    strictEditable () {
      // app权限
      const result = this.applications.some(item => item.appKey === this.configData.appKey)
      if (result) {
        return true
      } 
      return this.subEditable
    }
  },
  created() {
    this.getApps()
  },
  methods: {
    getApps () {
      queryApps().then(res => {
        this.applications = res.map(item => ({
          appKey: item.name
        }))
      }).catch(res => {
        this.applications = []
        this.$message.error('获取应用列表失败！原因：' + res.message || '暂无')
      })
    }
  }
}

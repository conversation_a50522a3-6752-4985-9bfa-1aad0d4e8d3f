.bids-json-schema-editor {
  width: 100%;
  .json-item {
    line-height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .col-caret {
      width: 24px;
      text-align: center;
      i {
        cursor: pointer;
        font-size: 16px;
      }
    }
    .col-name {
      flex-grow: 1;
    }
    .col-required {
      margin-left: 15px;
    }
    .col-type {
      .el-select {
        width: 100px;
      }
      margin-left: 15px;
    }
    .col-title {
      margin-left: 15px;
      .el-input {
        width: 130px;
      }
    }
    .col-description {
      margin-left: 15px;
      .el-input {
        width: 180px;
      }
    }
    .col-toolbar {
      margin-left: 5px;
      width: 90px;
      i {
        font-size: 16px;
        cursor: pointer;
        margin-left: 10px;
      }
    }
  }
  .el-dialog {
    h4 {
      margin-bottom: 10px;
    }
    .el-form-item {
      width: 32%;
      .el-select {
        width: 160px;
      }
    }
    .preview {
      height: 150px;
      line-height: 20px;
      width: 100%;
      border: 1px solid @color-bd;
      border-radius: 4px;
      padding: 10px 10px;
      overflow-y: auto;
    }
  }
}

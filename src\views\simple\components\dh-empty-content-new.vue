<template>
  <div class="dh-empty-content">
    <img class="img" src="~@/assets/img/img-empty-content-new.png">
    <div class="content">
      <span v-if="msg" class="text">{{ msg }}</span>
      <slot />
    </div>
  </div>
</template>

<script>
/**
 * 空内容缺省提示
 */
export default {
  name: 'DhEmptyContentNew',
  props: {
    msg: {
      type: String,
      default: '暂无数据'
    }
  }
}
</script>

<style lang="less" scoped>
.dh-empty-content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #597191;

  .img {
    height: 100px;
    width: auto;
  }

  .content {
    margin-top: 20px;
  }
}
</style>

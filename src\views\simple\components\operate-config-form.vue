<template>
  <el-form class="base-width" ref="form" :model="form" :rules="rules" label-width="104px" size="small">
    <el-form-item label="是否告警" prop="alertStrategy.enable">
      <span v-if="isView">{{ form.alertStrategy.enable ? '开启' : '关闭' }}</span>
      <el-radio-group v-model="form.alertStrategy.enable" :disabled="isOnlineEdit" v-else>
        <el-radio :label="true">开启</el-radio>
        <el-radio :label="false">关闭</el-radio>
      </el-radio-group>
    </el-form-item>
    <template v-if="form.alertStrategy.enable">
      <el-form-item label="告警策略">
        <span>失败超时告警</span>
      </el-form-item>
      <el-form-item label="告警方式" prop="alertStrategy.alertNotifyType">
        <span v-if="isView">{{ getAlertNotifyType(form.alertStrategy.alertNotifyType) }}</span>
        <el-checkbox-group v-model="form.alertStrategy.alertNotifyType" :disabled="isOnlineEdit" v-else>
          <el-checkbox label="QW">企微</el-checkbox>
          <el-checkbox label="QWG">企微群信息</el-checkbox>
          <el-checkbox label="PHONE">电话</el-checkbox>
          <el-checkbox label="EMAIL">邮件</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="企微告警人" prop="alertStrategy.receivers" v-if="form.alertStrategy.alertNotifyType.includes('QW')">
        <span v-if="isView">{{ form.alertStrategy.receivers.map(item => item.staffName).join('，') }}</span>
        <sdc-staff-selector v-model="receivers" multiple clearable size="small" ref="staffSelector" placeholder="请选择" modalClass="bids-sdc-modal--fix" @change="handleReceiversChange" :disabled="isOnlineEdit" v-else/>
      </el-form-item>
      <el-form-item label="企微群chatId" prop="alertStrategy.chatId" v-if="form.alertStrategy.alertNotifyType.includes('QWG')">
        <span v-if="isView">{{ form.alertStrategy.chatId }}</span>
        <el-input v-model="form.alertStrategy.chatId" placeholder="请输入" :disabled="isOnlineEdit" v-else/>
      </el-form-item>
      <el-form-item label="电话告警人" prop="alertStrategy.phoneReceivers" v-if="form.alertStrategy.alertNotifyType.includes('PHONE')">
        <span v-if="isView">{{ form.alertStrategy.phoneReceivers.map(item => item.staffName).join('，') }}</span>
        <sdc-staff-selector v-model="phoneReceivers" multiple clearable size="small" ref="phoneSelector" placeholder="请选择" modalClass="bids-sdc-modal--fix" @change="handlePhoneReceiversChange" :disabled="isOnlineEdit" v-else/>
      </el-form-item>
      <el-form-item label="邮件告警人" prop="alertStrategy.emailReceivers" v-if="form.alertStrategy.alertNotifyType.includes('EMAIL')">
        <span v-if="isView">{{ form.alertStrategy.emailReceivers.map(item => item.staffName).join('，') }}</span>
        <sdc-staff-selector v-model="emailReceivers" multiple clearable size="small" ref="emialSelector" placeholder="请选择" modalClass="bids-sdc-modal--fix" @change="handleEmailReceiversChange" :disabled="isOnlineEdit" v-else/>
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isOnlineEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      receivers: [],
      phoneReceivers: [],
      emailReceivers: [],
      form: {
        alertStrategy: { 
          enable: null, 
          type: 'TIMEOUT_FAILURE', // 失败告警超时
          receiverInfos: [],

          alertNotifyType: [],
          chatId: '',
          receivers: [],
          phoneReceivers: [],
          emailReceivers: []
        }
      },
      schedulePopoverVisible: false
    }
  },
  computed: {
    ...mapState([]),
    rules() {
      if (this.isView) {
        return {}
      }
      return {
        'alertStrategy.enable': [
          { required: true, message: '请选择是否告警', trigger: 'change' }
        ],
        'alertStrategy.alertNotifyType': [
          { required: true, message: '请选择告警方式', trigger: 'change' }
        ],
        'alertStrategy.chatId': [
          { required: true, message: '请输入企微群chatId', trigger: 'blur' }
        ],
        'alertStrategy.receivers': [
          { required: true, message: '请选择企微告警人', trigger: 'change' }
        ],
        'alertStrategy.phoneReceivers': [
          { required: true, message: '请选择电话告警人', trigger: 'change' }
        ],
        'alertStrategy.emailReceivers': [
          { required: true, message: '请选择邮件告警人', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    setData(data) {
      this.form = {
        alertStrategy: {
          enable: data.alertStrategy.enable,
          type: data.alertStrategy.type,
          receiverInfos: data.alertStrategy.receiverInfos,

          alertNotifyType: data.alertStrategy.receiverInfos?.map(item => item.receiveType),
          chatId: data.alertStrategy.receiverInfos?.find(item => item.receiveType === 'QWG')?.chatId,
          receivers: data.alertStrategy.receiverInfos?.find(item => item.receiveType === 'QW')?.receivers,
          phoneReceivers: data.alertStrategy.receiverInfos?.find(item => item.receiveType === 'PHONE')?.receivers,
          emailReceivers: data.alertStrategy.receiverInfos?.find(item => item.receiveType === 'EMAIL')?.receivers
        }
      }
      this.$nextTick(() => {
        this.initStaffSelector()
      })
    },
    initStaffSelector() {
      if (this.form.alertStrategy.receivers) {
        const array = this.form.alertStrategy.receivers.map(item => ({
          StaffName: item.staffName,
          StaffID: item.staffId,
          EngName: item.engName
        }))
        this.$refs.staffSelector.setSelected(array)
      }
      if (this.form.alertStrategy.phoneReceivers) {
        const phoneArray = this.form.alertStrategy.phoneReceivers.map(item => ({
          StaffName: item.staffName,
          StaffID: item.staffId,
          EngName: item.engName
        }))
        this.$refs.phoneSelector.setSelected(phoneArray)
      }
      if (this.form.alertStrategy.emailReceivers) {
        const emailArray = this.form.alertStrategy.emailReceivers.map(item => ({
          StaffName: item.staffName,
          StaffID: item.staffId,
          EngName: item.engName
        }))
        this.$refs.emialSelector.setSelected(emailArray)
      }
    },
    getSubData() {
      return { 
        alertStrategy: {
          enable: this.form.alertStrategy.enable,
          type: this.form.alertStrategy.type,
          receiverInfos: this.form.alertStrategy.alertNotifyType.map(type => {
            const key = {
              QW: 'receivers',
              QWG: 'chatId',
              PHONE: 'phoneReceivers',
              EMAIL: 'emailReceivers'
            }[type]
            const obj = {
              receiveType: type,
              chatId: type === 'QWG' ? this.form.alertStrategy.chatId : '',
              receivers: this.form.alertStrategy[key]
            }
            return obj
          })
        }  
      }
    },
    handleReceiversChange(value) {
      this.form.alertStrategy.receivers = value.map(item => {
        return {
          staffId: item.StaffID,
          staffName: item.StaffName,
          engName: item.EngName
        }
      })
    },
    handlePhoneReceiversChange(value) {
      this.form.alertStrategy.phoneReceivers = value.map(item => {
        return {
          staffId: item.StaffID,
          staffName: item.StaffName,
          engName: item.EngName
        }
      })
    },
    handleEmailReceiversChange(value) {
      this.form.alertStrategy.emailReceivers = value.map(item => {
        return {
          staffId: item.StaffID,
          staffName: item.StaffName,
          engName: item.EngName
        }
      })
    },
    getAlertNotifyType(types) {
      return types.map(type => {
        return {
          QW: '企微',
          QWG: '企微群信息',
          PHONE: '电话',
          EMAIL: '邮件'
        }[type]
      }).join('，')
    }
  }
}
</script>

<style lang="less" scoped>

</style>

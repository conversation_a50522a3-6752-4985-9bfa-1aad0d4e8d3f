<template>
  <el-form class="base-width" ref="form" :model="form" :rules="rules" label-width="94px" size="small">
    <el-form-item label="是否告警" prop="alertStrategy.enable">
      <span v-if="isView">{{ form.alertStrategy.enable ? '开启' : '关闭' }}</span>
      <el-radio-group v-model="form.alertStrategy.enable" v-else>
        <el-radio :label="true">开启</el-radio>
        <el-radio :label="false">关闭</el-radio>
      </el-radio-group>
    </el-form-item>
    <template v-if="form.alertStrategy.enable">
      <el-form-item label="告警策略">
        <span>失败超时告警</span>
      </el-form-item>
      <el-form-item label="告警方式">
        <span v-if="isView">77777</span>
        <el-checkbox-group v-model="form.alertStrategy.alertNotifyType" v-else>
          <el-checkbox label="QW">企微机器人</el-checkbox>
          <el-checkbox label="QWG">企微信息</el-checkbox>
          <el-checkbox label="PHONE">电话</el-checkbox>
          <el-checkbox label="EMAIL">邮件</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="告警人">
        <span v-if="isView">777777</span>
        <sdc-staff-selector v-model="form.alertStrategy.receivers" multiple clearable size="small" ref="staffSelector" placeholder="请选择" v-else/>
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        alertStrategy: { enable: null, alertNotifyType: [], receivers: [] }
      },
      rules: {
        'alertStrategy.enable': [
          { required: true, message: '请选择是否告警', trigger: 'change' }
        ]
      },
      schedulePopoverVisible: false
    }
  },
  computed: {
    ...mapState([])
  },
  methods: {
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    getSubData() {
      return { ...this.form }
    }
  }
}
</script>

<style lang="less" scoped>

</style>

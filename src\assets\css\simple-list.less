
.simple-list-page {
  padding: 0 @page-padding-horizontal;
  padding-top: 20px;
  max-width: @page-max-width;

  .el-tabs__header {
    border-bottom: 1px solid #ddd;
  }

  .simple-title {
    color: #333333;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    margin-bottom: 10px;
    position: relative;
  }

  .create-button {
    position: absolute;
    top: 32px;
    right: 0;
    z-index: 1001;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .filter {
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }
  .page-main {
    padding: 10px 0 @page-main-padding-vertical 0;
    height: calc(100vh - 170px - 65px - 36px);
    min-height: 200px;
    .table {
      height: calc(100% - 70px);
    }
    .pagination {
      margin-top: 24px;
    }
    .el-dropdown-link {
      cursor: pointer;
      color: #3464E0;
      font-size: 12px;
    }
    .el-icon-arrow-down {
      font-size: 12px;
    }
  }
  .el-input .el-select{
    width: 110px;
  }
}

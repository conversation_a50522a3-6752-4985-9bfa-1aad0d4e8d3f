<template>
  <div class="publish-list">
    <div class="page-header">
      <div class="header-left">
        <el-form :inline="true" :model="filterForm"  size="small">
          <el-form-item label="应用编码">
            <el-select v-model="filterForm.appName" filterable @change="handleAppChange" clearable>
              <el-option v-for="item in appOptions" :key="item.appKey" :value="item.appKey" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="落地策略">
            <el-select v-model="filterForm.storageStrategy" @change="handleAppChange" clearable>
              <el-option v-for="item in storageStrategyData" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="配置编码">
            <el-input v-model="filterForm.interfaceCode" clearable placeholder="请输入" @input="handleInput"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button type="primary" size="medium" @click="handleExecutes()">批量执行</el-button>
        <el-button type="primary" size="medium" @click="handleSubJob()">批量执行订阅</el-button>
      </div>
    </div>
    <div class="page-main">
      <div class="table">
        <el-table
          :data="tableData"
          v-loading="loading"
          height="100%"
          ref="table"
          :header-cell-style="{background:'#f5f7f9', height: '60px'}"
          style="width: 100%"
          @selection-change="handleSelectionChange">
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableColumn" :key="index"
            :prop="item.prop"
            :label="item.label"
            :formatter="item.formatter"
            :sortable="item.sortable"
            :sort-method="item.sortMethod"
            :min-width="item.minWidth || item.width"
            :show-overflow-tooltip="!item.complete"
            align="center">
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="150" align="center">
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="left" :open-delay="200">
                <div v-if="scope.row.message && scope.row.message.length > 0" style="text-align: center;">
                  <div v-for="(item, index) in scope.row.message" :key="item + index" style="padding: 8px 0;">{{item}}</div>
                </div>
                <div v-else style="text-align: center;">暂无</div>
                <span slot="reference"><el-button type="text" size="small">查看结果</el-button></span>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { pagination, filterInput, formatter } from 'mixins'
import { queryMineData, batchStart } from 'services/publish.service.js'
import { subJobsbatchStart } from 'services/subscribe.service.js'
import { queryAppsInConfig } from 'services/common.service'
// import { DataType } from 'sdc-core'

export default {
  name: 'release',
  components: {
  },
  mixins: [pagination, filterInput, formatter], // 分页，搜索栏，格式化表格内容
  data() { 
    return {
      appOptions: [],
      loading: false,
      tableData: [], // 数据发布表格数据
      paginationType: 'remote', // 由服务器实现分页查询
      exeStatusData: [], // 运行状态表格数据
      popoverLoading: false, // 运行状态弹出窗加载效果
      tableColumn: [
        { prop: 'appName', label: '应用名称', minWidth: '80' },
        { prop: 'appKey', label: '应用编码', minWidth: '80', complete: true },
        { prop: 'pub', label: '配置类型', minWidth: '80' },
        { prop: 'interfaceCode', label: '配置编码', minWidth: '100', complete: true },
        { prop: 'interfaceDesc', label: '发布描述', minWidth: '120' },
        { prop: 'collectType', label: '采集类型', minWidth: '80', formatter: this.formatterCollectType },
        { prop: 'storageStrategy', label: '落地策略', minWidth: '80', formatter: this.formatterStorageStrategy },
        { prop: 'schedulePeriodCron', label: '发布周期', minWidth: '110', sortable: true },
        { prop: 'creatorName', label: '创建人', minWidth: '80' },
        { prop: 'result', label: '操作结果', minWidth: '70' }
      ],
      statusTableColumn: [
        { prop: 'version', label: '版本', width: '100' },
        { prop: 'jobExecutorStatus', label: '最近任务运行状态', width: '200', formatter: this.formatterJobExeState }
      ],
      storageStrategyData: [
        { id: 'SAVE', name: '暂存平台' },
        { id: 'NOT_SAVE', name: '直连源库' }
      ],
      selection: [],
      SubJobCache: []
    }
  },
  created() {
    this.fetchData() // 回到页面时重新加载数据
    const params = { configType: 'PUB', kind: 'MINE' }
    queryAppsInConfig(params).then(res => {
      res && (res = res.map(v => { return { ...v, label: `${v.appName}(${v.appKey})` } }))
      this.appOptions = res
    })
  },
  methods: {
    fetchData() {
      // 获取我的发布列表
      const paginator = { pageSize: this.pageSize, pageIndex: this.pageIndex } // 分页查询的参数
      const criteria = [] // 搜索栏的参数
      Object.keys(this.filterForm).forEach(key => {
        if (this.filterForm[key]) {
          if (key === 'storageStrategy') {
            criteria.push({ criteriaKey: `jobs.sink.${key}`, criteriaValue: this.filterForm[key], accurate: true })
          } else if (key === 'interfaceCode') {
            criteria.push({ criteriaKey: 'interfaceCode', criteriaValue: this.filterForm[key], accurate: false })
          } else {
            criteria.push({ criteriaKey: 'appKey', criteriaValue: this.filterForm[key], accurate: true })
          }
        }
      })
      const postData = { paginator, criteria }
      this.loading = true
      queryMineData(postData).then(res => {
        res = res || { paginator: {} }
        this.tableData = (res.content || []).map(item => ({ ...item, schedulePeriodCron: this.formatterScheduleAtCron(item), pub: '发布', result: '' }))
        this.recordCount = res.paginator.recordCount || 0
        this.loading = false
        // 刷新数据对比
        this.tablesubjob()
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('获取我的数据发布失败！原因：' + res.message || '暂无')
        this.loading = false
      })
      // this.$refs.table.doLayout()
    },
    handleSelectionChange(val) {
      this.selection = val
    },
    handleExecutes() { 
      if (this.selection.length === 0) {
        return this.$message.error('请勾选数据')
      }
      this.$confirm(`请选择数据批量执行, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = { configIds: this.selection.map(v => v.configId) }
        batchStart(params).then(res => {
          if (!res) return
          this.handlePopoverShow(res)
          this.$message.success('操作成功！')
        }).catch(res => {
          if (res && res.data && res.data.length > 0) {
            this.handlePopoverShow(res.data)
            this.$message.success('操作成功！')
          } else {
            this.$message.error('操作失败！原因：' + res.message || '暂无')
          }
        })
      }).catch(() => {})
    },
    handleSubJob() {
      if (this.selection.length === 0) {
        return this.$message.error('请勾选需要批量的数据！')
      }
      this.$confirm(`请选择何种数据获取方式的订阅?`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '拉取',
        cancelButtonText: '推送',
        cancelButtonClass: 'primary',
        type: 'warning'
      }).then(() => {
        this.subJobExecutes('PULL')
      }).catch(action => {
        if (action === 'cancel') {
          this.subJobExecutes('PUSH')
        }
      })
    },
    subJobExecutes(val) {
      const params = { configIds: this.selection.map(v => v.configId), collectWay: val }
      subJobsbatchStart(params).then(res => {
        if (!res) return
        this.handlePopoverShow(res)
        this.$message.success('操作成功！')
      }).catch(res => {
        if (res && res.data && res.data.length > 0) {
          this.handlePopoverShow(res.data)
          this.$message.success('操作成功！')
        } else {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        }
      })
    },
    // 返回结果处理
    handlePopoverShow(val) {
      let SubJobCache = []
      val = val.map(v => { return { configId: v.configId, version: v.version, success: v.success, message: [v.message] } })
      // 接口返回值有重复，先组合
      if (this.SubJobCache.length === 0) {
        const res = new Map()
        this.SubJobCache = val.filter(a => !res.has(a['configId']) && res.set(a['configId'], 1))
      }
      if (val.length > 0 && this.SubJobCache.length > 0) {
        for (let index = 0; index < this.SubJobCache.length; index++) {
          const element = this.SubJobCache[index]
          for (let h = 0; h < val.length; h++) {
            if (element.configId === val[h].configId) {
              element.success = val[h].success
              element.message.push(...val[h].message)
              element.message = [...new Set(element.message)]
            } else {
              let flag = this.SubJobCache.find(v => v.configId === val[h].configId)
              let estimate = SubJobCache.find(v => v.configId === val[h].configId)
              if (!flag && !estimate) {
                SubJobCache.push(val[h])
              }
            }        
          }
        }
        // 相同的修改，不同的添加
        SubJobCache.push(...this.SubJobCache)
      } else {
        SubJobCache.push(...val)
      }
      this.SubJobCache = SubJobCache
      // console.log(this.SubJobCache)
      this.tablesubjob()
    },
    // 表格数据处理
    tablesubjob() {
      if (!this.SubJobCache) return
      for (let i = 0; i < this.tableData.length; i++) {
        for (let h = 0; h < this.SubJobCache.length; h++) {
          if (this.tableData[i].configId === this.SubJobCache[h].configId) {
            // this.tableData[i].result = this.SubJobCache[h].success ? '成功' : '失败'
            // this.tableData[i].message = this.SubJobCache[h].message
            this.$set(this.tableData[i], 'result', this.SubJobCache[h].success ? '成功' : '失败')
            this.$set(this.tableData[i], 'message', this.SubJobCache[h].message)
          }
        }
      }
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/publish-list.less";
  .primary {
    color: #FFF;
    background-color: #3464E0;
    border-color: #3464E0;
  }
  .primary:hover {
    border-color: #2f5aca;
    color: #FFF;
    background: #2f5aca;
  }
</style>

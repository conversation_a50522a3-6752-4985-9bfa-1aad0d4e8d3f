/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-12-15 12:10:22
 */
import { getDataApi } from 'sdc-core'

export default getDataApi({
  bidsplus_config: {
    publish: {
      queryAllData: '/pub/jobs/query/all',
      queryData: '/pub/jobs/query/{configId}/{version}',
      updateData: '/pub/jobs/update/{configId}/{version}',
      deleteData: '/pub/jobs/delete/{configId}',
      queryBatch: '/pub/jobs/query/batch',
      querySubs: '/pub/jobs/{configId}/{version}/subscriptions',
      queryMineData: '/pub/jobs/query/mine',
      queryDrafts: '/pub/drafts/query',
      saveDrafts: '/pub/drafts/save/{configId}',
      updateDrafts: '/pub/drafts/update/{configId}/{version}',
      removeDrafts: '/pub/drafts/remove',
      submitDrafts: '/pub/drafts/submit',
      setOnline: '/pub/jobs/online',
      setOffline: '/pub/jobs/offline'
    },
    subscribe: {
      queryAllData: '/sub/jobs/query/all',
      queryData: '/sub/jobs/query/{configId}/{version}',
      updateData: '/sub/jobs/update/{configId}/{version}',
      deleteData: '/sub/jobs/delete/{configId}',
      queryMineData: '/sub/jobs/query/mine',
      queryDrafts: '/sub/drafts/query',
      saveDrafts: '/sub/drafts/save/{configId}',
      updateDrafts: '/sub/drafts/update/{configId}/{version}',
      removeDrafts: '/sub/drafts/remove',
      submitDrafts: '/sub/drafts/submit',
      setOnline: '/sub/jobs/online',
      setOffline: '/sub/jobs/offline',
      batchStart: '/pub/jobs/batchStart',
      subJobsbatchStart: '/pub/relatedSubJobs/batchStart'
    },
    common: {
      queryUserInfo: '/query/userinfo',
      queryApps: '/apps/getList', // other
      queryDataSources: '/datasources/query/{appKey}/{dsId}',
      queryDsPassWord: '/datasources/query/{dsPasswordKey}/password',
      testDataSourceById: '/datasources/{datasourceId}/test',
      testDataSource: '/datasources/connection/test',
      queryDataSourceRefer: '/datasources/{datasourceId}/refer',
      addDataSource: '/datasources/add',
      editDataSource: '/datasources/{datasourceId}/edit',
      deleteDataSource: '/datasources/{datasourceId}/delete',
      scanDsTable: '/datasources/{datasourceId}/scan',
      scanBySQL: '/datasources/{datasourceId}/scanbysql',
      uploadTableSql: '/table/upload',
      startJobNow: '/jobs/startJobNow',
      runJob: '/jobs/run/{jobName}',
      killJob: '/jobs/kill/{jobName}',
      restartJob: '/jobs/restart/{jobInstId}',
      pauseJob: '/jobs/pause/{jobName}',
      resumeJob: '/jobs/resume/{jobName}',
      queryJobState: '/jobs/status/query',
      queryJobInfo: '/jobs/{jobName}',
      // queryJobLog: '/jobs/{jobName}/log', // flink
      // queryJobLog: '/tools/logs/{jobName}',
      queryJobShapshot: '/jobs/{configId}/snapshot', // 查询每个version最新运行状态
      queryJobTableData: '/jobs/{jobName}/tabledata',
      queryJobTableDataCount: '/jobs/{jobName}/tabledata/count',
      // queryScheduleLog: '/scheduleLog',
      queryInstances: '/jobs/{configId}/{version}/instances',
      genEncryptionKey: '/encryption/{encryptionWay}/key',
      checkCodeExist: '/check/{appKey}/{interfaceCode}/codeExist',
      queryAppsInConfig: '/statistics/{configType}/{kind}/apps/query',
      // getSchemaList: '/namespace/list',
      parseSinkTableStructure: '/{category}/parseSinkTableStructure',
      getScheduleInfo: '/statistics/mine',
      queryTotalInstance: '/instanceInfos',
      queryJobsBatchRestart: '/jobs/batchRestart',
      queryTenantLst: '/datasources/queryTenantLst',
      queryAllTables: '/datasources/queryAllTables',
      queryTableColumns: '/datasources/queryTableColumns?datasourceId={datasourceId}&tablename={tablename}',
      downloadFile: '/download/{appkey}/{interfaceCode}/{version}/latest',
      getUser: '/query/getUser',
      getAppKmsToken: '/appKmsToken/queryToken?configId={configId}',
      querySecretKeysList: '/appKmsToken/querySecretKeys?configId={configId}',
      reqEncryptionKey: '/encryption/{encryptionWay}/key',
      getWhiteList: '/getWhiteList'
    },
    sync: {
      querySyncPubRecordList: '/sync/pub/pageQueryRecords',
      querySyncSubRecordList: '/sync/sub/pageQueryRecords',
      querySyncPreImport: '/sync/pre-import',
      querySyncImport: '/sync/import'
    },
    simple: {
      querySimpleConfigList: '/jobs/integration/list',
      querySimpleConfigVersions: '/jobs/integration/listVersions',
      releaseSimpleConfig: '/jobs/integration/release?id={id}&releaseState={releaseState}',
      saveSimpleConfig: '/jobs/integration/save',
      rollbackSimpleConfig: '/jobs/integration/rollback?id={id}&version={version}',
      updateOnlineSimpleConfig: '/jobs/integration/updateOnline',
      querySimpleConfigInfo: '/jobs/integration/info',
      deleteSimpleConfig: '/jobs/integration/delete?id={id}'
    }
  },
  bidsplus_executor: {
    queryJobLog: '/tools/logs/{jobName}',
    queryJob: '/jobs/{executorJobName}'
  },
  frontRepo: {
    getSchemaList: '/namespace/list'
  },
  dolphinscheduler: {
    queryScheduleLog: '/scheduleLog'
  },
  jobservice: {
    queryJobLog: '/job/queryFlinkLog',
    queryScheduleLog: '/job/queryLog'
  },
  special: {
    queryPubPageList: '/sync/pub/pageQueryConfigs',
    querySubPageList: '/sync/sub/pageQueryConfigs'
  }
}, {
  multi: true,
  frontRepo: '/front-repo/api',
  bidsplus_config: '/hr-bidsplus-config/api',
  bidsplus_executor: '/bidsplus-executor/api',
  dolphinscheduler: '/dolphinscheduler/api/bids',
  jobservice: '/hr-data-jobservice/api'
})

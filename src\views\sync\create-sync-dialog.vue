<template>
  <div class="create-sync-dialog">
    <el-dialog :title="title" :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" width="1433px" top="6vh">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" size="small">
        <el-form-item label="来源环境" prop="env">
          <el-select v-model="form.env" placeholder="请选择">
            <el-option label="dev" value="测试环境"></el-option>
            <el-option label="uat" value="UAT环境"></el-option>
            <el-option label="prd" value="生产环境"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="同步类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio-button label="publish">发布</el-radio-button>
            <el-radio-button label="subscribe">订阅</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template v-if="form.type === 'publish'">
        <el-form ref="filterForm" :inline="true" :model="filterForm" size="small">
          <el-form-item label="所属应用">
            <el-select v-model="filterForm.app" placeholder="请选择"></el-select>
          </el-form-item>
          <el-form-item label="发布编码">
            <el-input v-model="filterForm.code" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="负责人">
            <el-input v-model="filterForm.creator" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" placeholder="请选择"></el-select>
          </el-form-item>
          <el-form-item>
            <el-button>重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          ref="multipleTable"
          :data="tableData"
          style="width: 100%"
          height="500"
          :key="Date.now()"
          :header-cell-style="{ background: '#f5f7f9', height: '60px' }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="48" align="center" fixed="left"></el-table-column>
          <el-table-column label="发布名称" prop="name" width="188" show-overflow-tooltip></el-table-column>
          <el-table-column label="发布编码" prop="code" width="198" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属应用" prop="app" width="167" show-overflow-tooltip></el-table-column>
          <el-table-column label="发布版本" prop="version" width="347">
            <template> template </template>
          </el-table-column>
          <el-table-column label="类型" prop="type" width="100"></el-table-column>
          <el-table-column label="负责人" prop="operator" width="191">
            <template>
              <BidsUserName user="yiwuhe"></BidsUserName>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="160"></el-table-column>
        </el-table>
      </template>

      <template v-if="form.type === 'subscribe'">
        <el-form ref="filterForm" :inline="true" :model="filterForm" size="small">
          <el-form-item label="所属应用">
            <el-select v-model="filterForm.app" placeholder="请选择"></el-select>
          </el-form-item>
          <el-form-item label="订阅编码">
            <el-input v-model="filterForm.code" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="filterForm.creator" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" placeholder="请选择"></el-select>
          </el-form-item>
          <el-form-item>
            <el-button>重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          ref="multipleTable"
          :data="tableData"
          style="width: 100%"
          height="500"
          :key="Date.now()"
          :header-cell-style="{ background: '#f5f7f9', height: '60px' }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="48" align="center" fixed="left"></el-table-column>
          <el-table-column label="订阅名称" prop="name" width="169" show-overflow-tooltip></el-table-column>
          <el-table-column label="订阅编码" prop="code" width="169" show-overflow-tooltip></el-table-column>
          <el-table-column label="发布名称" prop="pubName" width="169" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属应用" prop="app" width="159" show-overflow-tooltip></el-table-column>
          <el-table-column label="订阅版本" prop="version" width="351">
            <template> template </template>
          </el-table-column>
          <el-table-column label="类型" prop="type" width="100"></el-table-column>
          <el-table-column label="创建人" prop="operator" width="191">
            <template>
              <BidsUserName user="yiwuhe"></BidsUserName>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="160"></el-table-column>
        </el-table>
      </template>

      <div class="pagination" v-if="form.type">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="medium">取消</el-button>
        <el-button size="medium" type="primary" @click="handleStartSync">开始同步</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { pagination } from 'mixins'
import { BidsUserName } from 'components'

export default {
  name: 'create-sync-dialog',
  mixins: [pagination], // 分页，搜索栏，格式化表格内容
  components: {
    BidsUserName
  },
  props: {
    initData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    title() {
      const env = {
        loc: '本地',
        dev: '测试',
        uat: 'UAT',
        prd: '生产'
      }[process.env.VUE_APP_BUILD_ENV]
      return `新建同步（当前环境：${env}）`
    }
  },
  data() {
    return {
      visible: false,
      paginationType: 'remote', // 由服务器实现分页查询
      form: {},
      filterForm: {},
      rules: {
        env: [{ required: true, message: '请选择来源环境', trigger: 'change' }],
        type: [
          { required: true, message: '请选择同步类型', trigger: 'change' }
        ]
      },
      tableData: [
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 },
        { code: 123 }
      ],
      multipleSelection: []
    }
  },
  methods: {
    show() {
      this.fetchData()
      this.visible = true
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    // 调用预校验接口
    handleStartSync() {
      Promise.resolve().then(() => {
        this.visible = false
        // 打开预校验的弹窗, 并传入数据
        this.$emit('confirm', {})
      })
    },
    fetchData() {},
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>

<style lang="less" scoped>
.create-sync-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px 20px 10px 20px;
  }
  .pagination {
    margin-top: 20px;
  }
}
</style>

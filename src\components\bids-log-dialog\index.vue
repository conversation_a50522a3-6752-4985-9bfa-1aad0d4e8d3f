<!--
 * @Description: 查看日志弹窗组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-11-04 10:22:29
-->
<template>
  <div class="bids-log-dialog">
    <el-dialog title="查看日志" :visible.sync="visible" :before-close="handleClose" width="800px" top="10vh" :fullscreen="fullscreen">
      <el-form :inline="true" :model="initData" label-width="100px" class="custom-el-form">
        <el-form-item :label="label">
          <span>{{initData.jobName}}</span>
        </el-form-item>
        <el-form-item label="调度实例ID" v-if="initData.jobInstId">
          <span>{{initData.jobInstId}}</span>
        </el-form-item>
      </el-form>
      <div class="toolbar">
        <!-- <el-tooltip class="item" effect="dark" placement="left" content="刷新日志">
          <el-button icon="el-icon-refresh" size="mini" circle @click="fetchData('refresh')"></el-button>
        </el-tooltip> -->
        <el-tooltip class="item" effect="dark" placement="left" content="到达底部">
          <el-button icon="el-icon-bottom" size="mini" circle @click="toBottom"></el-button>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" placement="left" :content="fullscreen ? '小屏' : '全屏'">
          <el-button icon="el-icon-full-screen" size="mini" circle @click="fullscreen=!fullscreen"></el-button>
        </el-tooltip>
      </div>
      <!-- <el-input
        id="scroll_text"
        type="textarea"
        readonly
        resize="none"
        v-model="logText"
        :style="{ height: fullscreen ? '80vh' : '50vh'}">
      </el-input> -->
      <div class="log-text" id="log-text" v-bind:class="{ 'log-text--fullscreen': fullscreen }">
        <p v-for="(item, index) in logText" :key="index">{{item}}</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <span v-if="this.initData.jobInstId" style="float:left;">
          <el-radio-group v-model="logType" @change="handleLogTypeChange">
            <el-radio label="flink">任务日志</el-radio>
            <el-radio label="scheduler">调度日志</el-radio>
          </el-radio-group>
        </span>
        <el-tooltip class="item" effect="dark" placement="left" content="回到顶部">
          <el-button icon="el-icon-top" size="mini" circle @click="toTop" style="margin-right:20px;"></el-button>
        </el-tooltip>
        <el-button @click="handleClose" size="medium">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { queryJobLog, queryScheduleLog, queryJobserviceJobLog, queryJobserviceScheduleLog } from 'services/common.service'
export default {
  name: 'bids-log-dialog',
  props: {
    label: String
  },
  data() {
    return {
      visible: false,
      logType: 'flink',
      logText: [],
      initData: {},
      fullscreen: false,
      start: 0,
      totalBytes: null,
      timer: null
    }
  },
  methods: {
    show(initData) {
      this.initData = initData || {} // { jobName, jobInstId }
      this.logType = 'flink'
      this.fullscreen = false
      this.visible = true
      this.start = 0
      this.totalBytes = null
      this.logText = []
      this.fetchData()
    },
    handleClose(done) {
      this.$emit('close')
      clearTimeout(this.timer)
      typeof func === 'function' ? done() : (this.visible = false)
    },
    handleLogTypeChange() {
      this.start = 0
      this.totalBytes = null
      this.logText = []
      clearTimeout(this.timer)
      this.fetchData()
    },
    fetchData() {
      // if (mode !== 'refresh') {
      //   this.logText = 'Loading...'
      // }
      if (this.logType === 'flink') {
        this.fetchJobLog()
      } else {
        this.fetchScheduleLog()
      }
    },
    fetchJobLog() {
      if (this.initData.jobInstId?.includes('data')) {
        queryJobserviceJobLog({ jobInstanceId: this.initData.jobInstId }).then(res => {
          this.logText = res
        }).catch(res => {
          this.logText = [res.message.toString()]
        })
      } else {
        queryJobLog({ jobName: this.initData.jobName }, { start: this.start, end: this.start + 20000 }).then(res => {
          this.logText.push(res.logs)
          // 设置下次
          this.start = this.start + res.bytes
          this.totalBytes = res.totalBytes
          if (this.visible && (!this.totalBytes || this.start < this.totalBytes)) {
            const timeout = res.bytes > 0 ? 300 : 1500
            this.timer = setTimeout(this.fetchData, timeout)
          }
        }).catch(res => {
          this.logText = [res.message.toString()]
        })
      }
    },
    fetchScheduleLog() {
      if (this.initData.jobInstId?.includes('data')) {
        queryJobserviceScheduleLog({ jobInstanceId: this.initData.jobInstId }).then(res => {
          this.logText = res
        }).catch(res => {
          this.logText = [res.message.toString()]
        })
      } else {
        queryScheduleLog({ jobInstId: this.initData.jobInstId }).then(res => {
          this.logText = res
        }).catch(res => {
          this.logText = [res.message.toString()]
        })
      }
    },
    toBottom() {
      const textarea = document.getElementById('log-text')
      textarea.scrollTop = textarea.scrollHeight
    },
    toTop() {
      const textarea = document.getElementById('log-text')
      textarea.scrollTop = 0
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/bids-log-dialog.less";
</style>

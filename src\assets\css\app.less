@import "./vars.less";

body {
  font-family: @font-family;
  // user-select: none; /* 禁止用户鼠标在页面上选中文字/图片等 */
  -webkit-tap-highlight-color: transparent; /* webkit是苹果浏览器引擎，tap点击，highlight背景高亮，color颜色，颜色用数值调节 */
  background: @color-bg-body;
  color: @color-text-black;
  font-size: @font-14;
  overflow: hidden;
  /* rem vw/vh */
  // width: 100vw;
}

.header-logo {
  height: 100%;
  margin-left: 20px;
  .logo-icon {
    height: 100%;
    display: inline-block;
    background: url('../img/logo.png') no-repeat center;
  }
}
.header-links {
  img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
  }
  .help {
    cursor: pointer;
  }
  .name {
    margin: 0 15px 0 10px;
  }
  .exit {
    cursor: pointer;
    &:hover {
      color: @color-bg-hover;
    }
    &:active {
      color: @color-bg-light;
    }
  }
}

.sdc-header .header-inner .header-left .logo {
  cursor: default;
}

.button-text-danger {
  color: @color-danger;
  &:hover, &:focus {
    color: @color-deep-orange;
  }
  &:active {
    color: @color-danger;
  }
}

.sdc-content {
  .page-nav {
    width: @custome-sidebar-width;
  }
  .page-container {
    background-color: @color-bg-white;
    border-radius: 4px;
  }
}

.sdc-sidebar {
  width: @custome-sidebar-width;
  .toggle-sidebar {
    width: @custome-sidebar-width;
  }
}

.button-text-other {
  color: @color-theme;
  &:hover, &:focus {
    color: @color-brown;
  }
  &:active {
    color: @color-blue-grey;
  }
}

.el-input-group__prepend {
  background-color: @color-bg-white;
}

.config-item__error {
  position: relative;
  .el-textarea__inner {
    border: 1px solid @color-danger;
  }
  .panel-border {
    border: 1px solid @color-danger!important;
  }
  .error-message {
    color: #f81d22;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
  }
}

.tableCellIcon {
  display: inline-block;
  padding:7px;
  height: 28px;
  line-height: 1;
  font-size: 12px;
  box-sizing: border-box;
  text-align: center;
  border: 1px solid #dcdcdc;
  border-radius: 50%;
  background: #ebf0fc;
  border-color: #aec1f3;
  color: @color-theme;
  &:hover {
    cursor: pointer;
    background: @color-theme;
    border-color: @color-theme;
    color: @color-text-white;
  }
}

.remarks-text {
  color: @color-text-gray;
  margin-left: 20px;
}

.event-icon {
  cursor: pointer;
  &:hover {
    color: @color-theme;
  }
}

/*----分割线文案----*/
.el-divider__text {
  padding: 0 15px;
}

/*----构建提示框----*/
.description-panel {
  // height: 300px;
  width:375px;
  border: 1px solid #eee;
  padding: 15px 15px;
  position: absolute;
  top: 3px;
  right: 3px;
  .question {
    font-weight: bold;
    font-size: 16px;
    color: @color-text-dark;
    font-style: oblique;
    i {
      color: @color-theme;
      margin-right: 10px;
    }
    margin-bottom: 5px;
  }
  .answer {
    padding-left: 26px;
    &+.question {
      margin-top: 10px;
    }
    line-height: 21px;
    .el-link {
      vertical-align: top;
    }
  }
}



/*-重写element tab样式-*/
.config-tabs {
  &.el-tabs--right.el-tabs--card {
    & > .el-tabs__header.is-right {
      margin-left: 0;
      margin-top: 150px;
      .el-tabs__nav {
        border-radius: 0;
        .el-tabs__item {
          &:not(.is-active) {
            color: @color-text-black;
          }
          padding: 0 13px;
        }
        .el-tabs__item.is-active {
          &.el-tabs__item::after {
            content: "";
            position: absolute;
            right: 0;
            top: 13px;
            height: 14px;
            border-right: 4px solid @color-theme;
            box-sizing: border-box;
          }
        }
      }
    }
  }
}

/*----配置栏----*/
.config-box {
  border: 1px solid #E4E7ED;
  min-height: 500px;
  padding: 30px 30px;
  .el-form-item {
    label {
      font-weight: 500;
    }
  }
}
.config-footer {
  position: sticky;
  width: calc(100% - 83px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  bottom: 10px;
  border: 1px solid #E4E7ED;
  z-index: 4;
  background-color: @color-bg-white;
  box-shadow: 0px -1px 3px 0px #E4E7ED;
  .buttons {
    display: flex;
    align-items: center;
    height: 70px;
    padding: 20px;
    .el-button+.el-button {
      margin-left: 20px;
    }
  }
}

/*-重写element tab样式-*/
.button-tabs {
  &.el-tabs{
    & > .el-tabs__header.is-top {
      .el-tabs__nav.is-top {
        border-bottom: none;
        .el-tabs__active-bar {
          display: none;
        }
        .el-tabs__item {
          color: @color-text-black;
          background: @color-bg-white;
          border: 1px solid @color-bd;
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
          border-radius: 16px;
          font-weight: @font-normal;
          padding: 0 14px;
          height: 34px;
          line-height: 34px;
          margin-right: 16px;
          &.is-active {
            color: @color-text-white;
            background: @color-theme;
            // color: @color-theme;
            // background: @color-bg-light;
            border: 1px solid transparent;
          }
        }
      }
    }
  }
}

/*-重写element tab样式-*/
.version-tabs {
  &.el-tabs--card {
    & > .el-tabs__header.is-top {
      .el-tabs__nav {
        margin-left: 25px;
        border-radius: 0;
        .el-tabs__item.is-active {
          &.el-tabs__item::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 25px;
            width: 36px;
            border-top: 4px solid @color-theme;
            box-sizing: border-box;
          }
        }
        .verson-tab-label {
          padding: 0 5px;
          .label-text {
            margin-right: 5px;
            font-size: 16px;
            line-height: 16px;
          }
        }
      }
    }
  }
}

.version-info-panel {
  font-family: @font-family-yahei;
  margin-bottom: 10px;
  padding: 10px 25px;
  width: calc(100% - 83px);
  position: relative;
  // border: 1px solid #E4E7ED;
  .tips {
    position: absolute;
    font-size: 13px;
    top: 0;
    right: 0;
    i {
      margin-right: 5px;
    }
  }
  .el-tag {
    border-radius: 0px;
  }
  .info-row {
    .text-item {
      &+.text-item {
        margin-left: 40px;
      }
    }
    &+.info-row {
      margin-top: 20px;
    }
  }
  
  .operate-item {
    margin-top: 20px;
  }
}
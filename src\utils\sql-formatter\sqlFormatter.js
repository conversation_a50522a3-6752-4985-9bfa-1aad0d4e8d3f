/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-01-26 12:21:20
 */
import StandardSqlFormatter from './languages/StandardSqlFormatter'

export default {
  /**
   * Format whitespaces in a query to make it easier to read.
   *
   * @param {String} query
   * @param {Object} cfg
   *  @param {String} cfg.indent Characters used for indentation, default is '  ' (2 spaces)
   *  @param {Object} cfg.params Collection of params for placeholder replacement
   * @return {String}
   */
  format: (query, cfg) => {
    cfg = cfg || {}
    return new StandardSqlFormatter(cfg).format(query)
  }
}

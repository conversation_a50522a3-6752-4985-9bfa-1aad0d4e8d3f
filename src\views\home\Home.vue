<!--
 * @Description: 数据市场页面
 * @Autor: kenoxia
 * @LastEditTime: 2020-12-31 18:33:23
-->
<template>
  <div class="home">
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="数据发布" name="publish">
        <data-publish-list ref="publish"></data-publish-list>
      </el-tab-pane>
      <el-tab-pane label="数据订阅" name="subscribe">
        <data-subscribe-list ref="subscribe"></data-subscribe-list>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DataPublishList from './data-publish-list'
import DataSubscribeList from './data-subscribe-list'
export default {
  name: 'home-page',
  components: {
    DataPublishList,
    DataSubscribeList
  },
  data() {
    return {
      activeTab: 'publish'
    }
  },
  activated() {
    this.$refs[this.activeTab].fetchData() // 回到页面时重新加载数据
  },
  methods: {
    handleClick(tab) {
      this.$refs[tab.name].fetchData() // 切换tag时重新加载数据
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/home.less";
</style>

<!--
 * @Description: 编辑数据发布页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-14 18:06:55
-->
<template>
  <div class="publish-edit">
    <div class="page-header">
     <el-page-header @back="goBack" content="编辑已发布的数据"></el-page-header>
    </div>
    <div>
      <el-tabs class="version-tabs" v-model="currentVersion" type="card" @tab-click="handleVersionChange">
        <el-tab-pane v-for="item in data" :key="item.version" :name="item.version">
          <span slot="label" class="verson-tab-label">
            <span class="label-text">{{item.version}}</span>
            <i style="color:#0ad0b6;" class="el-icon-success" v-if="item.jobStatus === 1"></i>
            <i style="color:#ACACAC;" class="el-icon-remove" v-else></i>
          </span>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="version-info-panel">
      <div class="tips"><i class="el-icon-info"></i>如要编辑更多配置项，请先将版本下线</div>
      <div class="info-row">
        <span class="text-item">
          <span>当前版本：</span>
          <span>{{currentData.version}}</span>
        </span>
        <span class="text-item">
          <span>当前状态：</span>
          <span><el-tag v-if="currentData.version" effect="dark" size="small" :type="currentData.jobStatus === 1 ? 'success' : 'info'">{{currentData.jobStatus === 1 ? "运行中" : "已下线"}}</el-tag></span>
        </span>
        <span class="text-item">
          <span>初版创建人：</span>
          <span>{{currentData.creatorName}}</span>
        </span>
        <span class="text-item">
          <span>最近更新时间：</span>
          <span>{{currentData.lastModifiedTime}}</span>
        </span>
      </div>
      <div class="operate-item" v-if="currentData.version">
        <el-button icon="el-icon-copy-document" size="mini" @click="handleClone">克隆当前版本/新增版本</el-button>
        <el-divider direction="vertical"></el-divider>
        <el-button icon="el-icon-remove" size="mini" v-if="currentData.jobStatus === 1" @click="handleOnline(false)">下线当前版本</el-button>
        <el-button icon="el-icon-success" size="mini" v-else @click="handleOnline(true)">上线当前版本</el-button>
        <el-divider direction="vertical"></el-divider>
        <el-button size="mini" @click="handleManageJobs">管理实例</el-button>
      </div>
    </div>
    <el-tabs class="config-tabs" v-model="tabValue" type="card" tab-position="right" @tab-click="handleConfigTabClick">
      <el-tab-pane
        :key="item.name"
        v-for="item in currentTabs"
        :label="item.label"
        :name="item.name"
        :lazy="true">
      </el-tab-pane>
      <div class="config-box">
        <bids-base-info ref="baseInfo" :page-status="pageStatus" v-show="tabValue === 'baseInfo'"></bids-base-info>
        <bids-collect-rule ref="dataInput" :page-status="pageStatus" v-show="tabValue === 'dataInput'"></bids-collect-rule>
        <bids-publish-output ref="dataOutput" :page-status="pageStatus" v-show="tabValue === 'dataOutput'"></bids-publish-output>
        <bids-other-setting ref="setting" :page-status="pageStatus" :current-data="currentData" configType="publish" v-show="tabValue === 'setting'"></bids-other-setting>
      </div>
    </el-tabs>
    <div class="config-footer" v-if="currentData.version">
      <bids-global-tips-bar></bids-global-tips-bar>
      <div class="buttons">
        <el-button type="primary" size="medium" @click="handleSave">提交</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { BidsBaseInfo, BidsCollectRule, BidsPublishOutput, BidsOtherSetting, BidsGlobalTipsBar } from 'components'
import { queryData, updateData, setOnline, setOffline } from 'services/publish.service'
import { mapMutations, mapState } from 'vuex'

export default {
  components: {
    BidsBaseInfo,
    BidsCollectRule,
    BidsPublishOutput,
    BidsOtherSetting,
    BidsGlobalTipsBar
  },
  computed: mapState({
    configData: state => state.currentPublishConfig
  }),
  data() {
    return {
      pageStatus: 'edit',
      data: [],
      tabValue: 'baseInfo',
      currentTabs: [
        { name: 'baseInfo', label: '基本信息' },
        { name: 'dataInput', label: '数据采集' },
        { name: 'dataOutput', label: '数据输出' },
        { name: 'setting', label: '发布配置' }
      ],
      currentVersion: '',
      currentData: {}
    }
  },
  created() {
    this.INIT_PUSHLISH_CONFIG()
    this.fetchData()
  },
  methods: {
    ...mapMutations(['SET_PUSHLISH_CONFIG', 'INIT_PUSHLISH_CONFIG', 'UPDATE_CONFIG_ITEM']),
    goBack() {
      this.$router.back()
    },
    fetchData() {
      const params = { configId: this.$route.query.configId }
      queryData(params).then(res => {
        if (res.length > 0) {
          res.some(item => item.auth_type === 'view') && this.$router.push({ path: '/publish/view', query: { configId: this.$route.query.configId } })
          this.data = res
          this.currentData = res.find(item => item.version === this.currentVersion) || res.find(item => item.jobStatus === 1) || res[res.length - 1]
          this.currentVersion = this.currentData.version
          this.SET_PUSHLISH_CONFIG(this.currentData)
          this.currentTabs.forEach(tab => {
            this.$refs[tab.name].updatePageData()
          })
        }
      }).catch(res => {
        this.$message.error('获取数据发布失败！原因：' + res.message || '暂无')
      })
    },
    handleVersionChange(tab, event) {
      this.currentData = this.data.find(item => item.version === tab.name)
      this.currentVersion = this.currentData.version
      this.SET_PUSHLISH_CONFIG(this.currentData)
      this.currentTabs.forEach(tab => {
        this.$refs[tab.name].updatePageData()
      })
    },
    handleConfigTabClick(tab) {
      this.$refs[tab.name].updatePageData()
    },
    handleOnline(val) {
      const opt = val ? '上线' : '下线'
      this.$confirm('确定' + opt + '数据的' + this.currentVersion + '版本？如有修改，请先提交。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const setOption = val ? setOnline : setOffline
        const data = [{ configId: this.$route.query.configId, version: this.currentVersion, lastModifiedTime: this.currentData.lastModifiedTime }]
        this.SET_PUSHLISH_CONFIG(this.currentData) // 修改完界面数据不提交，就进行上下线处理时，还原数据
        setOption(data).then(res => {
          const item = res[0]
          if (item.success) {
            this.fetchData()
            this.$message.success(this.currentVersion + '版本' + opt + '成功！')
          } else {
            this.$message.warning(this.currentVersion + '版本' + opt + '失败！原因：' + item.message)
          }
        }).catch(res => {
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
      }).catch(res => {})
    },
    handleClone() {
      this.$router.push({ name: 'PublishBuild', query: { mode: 'cloning', configId: this.configData.configId, version: this.configData.version } })
    },
    handleSave() {
      let valid = true
      this.currentTabs.forEach(tab => {
        valid = valid && this.$refs[tab.name].formValidate()
      })
      if (!valid) {
        this.$message.error('请按要求填写完整表单项！')
        return
      }
      const params = { configId: this.configData.configId, version: this.configData.version }
      const { cache, ...postData } = this.configData
      updateData(params, postData).then(res => {
        this.$message.success('提交成功！')
        if (this.configData.jobStatus === 1) {
          setTimeout(() => this.$router.back(), 100) 
        } else {
          this.fetchData() // 停留在当前页面时重新获取数据
        }
      }).catch(res => {
        this.$message.error('提交失败！原因：' + res.message || '暂无')
      })
    },
    handleManageJobs() {
      this.$router.push({ path: '/publish/jobs', query: { configId: this.$route.query.configId } })
    }
  }
}
</script>
<style lang="less">
  @import "~assets/css/publish-edit.less";
</style>

<!--
 * @Description: 批量操作结果列表组件, 用于批量操作返回结果不同时，展示具体信息
 * @Autor: kenoxia
 * @LastEditTime: 2020-12-31 19:03:33
-->
<template>
  <div class="bids-batch-result-dialog">
    <el-dialog title="详情" :visible.sync="visible" :before-close="handleClose" width="800px" top="10vh">
      <el-table height="300" :data="tableData">
        <el-table-column label="编码" prop="interfaceCode" align="center" width="200"></el-table-column>
        <el-table-column label="描述" prop="interfaceDesc" align="center" width="200"></el-table-column>
        <el-table-column label="原因" prop="message" align="center" min-width="350"></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'bids-batch-result-dialog',
  data() {
    return {
      visible: false,
      tableData: []
    }
  },
  methods: {
    show(data) {
      this.tableData = data || []
      this.visible = true
    },
    handleClose(done) {
      this.$emit('close')
      done()
    }
  }
}
</script>

<style lang="less">
 @import "~assets/css/bids-batch-result-dialog.less";
</style>

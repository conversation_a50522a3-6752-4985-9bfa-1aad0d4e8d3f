<template>
  <div class="data-source-container">
    <div class="source-title">数据来源</div>
    <div class="source-form">
      <el-form :model="form" :rules="rules" label-width="94px" size="small" ref="form">
        <el-form-item label="数据类型" prop="dsType">
          <span v-if="isView">{{ form.dsType }}</span>
          <el-select v-model="form.dsType" filterable :disabled="isEdit" @change="handleDsTypeChange" v-else>
            <el-option v-for="item in dsTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据源" prop="dsId">
          <span v-if="isView">{{ getDsLabel(form.dsId) }}</span>
          <el-select v-model="form.dsId" filterable :disabled="isEdit" @change="handleDsIdChange" v-else>
            <el-option v-for="item in options" :key="item.dsId" :label="item.label" :value="item.dsId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据表" prop="tableName">
          <span v-if="isView">{{ form.tableName }}</span>
          <el-input v-model="form.tableName" placeholder="请输入" :disabled="isEdit" @input="() => debounceTableNameChange()" v-else style="width: 207px;"></el-input>
        </el-form-item>
        <!-- <el-form-item label="过滤SQL">
          <div class="sql-title">SQL语句</div>
          <codemirror ref="cmEditor" :value="form.sql" :options="{ lineNumbers: false, readOnly: isView || isEdit }" @input="handleSqlChange" @ready="handleCmReady"/>
        </el-form-item> -->
      </el-form>
    </div>
  </div>
</template>

<script>
import { queryTableColumns } from 'services/common.service.js'
import { DataEvent } from 'sdc-core'

export default {
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    dataSourceOptions: {
      type: Array,
      default: () => []
    },
    dsTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        dsType: '',
        dsId: '',
        dsName: '',
        tableName: ''
      },
      rules: {
        dsType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ],
        dsId: [
          { required: true, message: '请选择数据源', trigger: 'change' }
        ],
        tableName: [
          { required: true, message: '请选择数据表', trigger: 'change' }
        ]
      },
      debounceTableNameChange: DataEvent.debounce(this.handleTableNameChange, 300)
    }
  },
  computed: {
    options() {
      if (!this.form.dsType) return []
      return this.dataSourceOptions.filter(item => item.dsType === this.form.dsType)
    }
  },
  methods: {
    handleCmReady(cm) {
      cm.on('keypress', () => {
        if (this.moderateEditable && this.form.eventPlugin) cm.showHint()
      })
    },
    handleSqlChange(value) {
      this.form.sql = value
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    },
    setData(data, sourceMappingSink) {
      this.form = {
        dsType: data.dsType,
        dsId: data.dsId,
        dsName: data.dsName,
        tableName: data['table-name']
      }
      this.handleTableNameChange(sourceMappingSink)
    },
    getSubData() {
      return {
        dsId: this.form.dsId,
        dsType: this.form.dsType,
        dsName: this.form.dsName,
        'table-name': this.form.tableName
      }
    },
    getDsLabel(dsId) {
      return this.dataSourceOptions.find(item => item.dsId === dsId)?.label
    },
    handleDsTypeChange() {
      this.form.dsId = ''
      this.form.dsName = ''
      this.form.tableName = ''
    },
    handleDsIdChange(value) {
      this.form.dsName = this.dataSourceOptions.find(item => item.dsId === value).dsName

      this.form.tableName = ''
    },
    handleTableNameChange(sourceMappingSink) {
      if (this.form.dsId && this.form.tableName) {
        queryTableColumns({ datasourceId: this.form.dsId, tablename: this.form.tableName }).then(res => {
          const data = res.map(item => ({
            sourceFieldKey: item.name,
            sourceFieldType: item.type,
            mappingFieldKey: sourceMappingSink?.find(field => field.sourceFieldKey === item.name)?.mappingFieldKey
          }))
          this.$emit('updateSourceFields', data)
        }).catch(() => {
          this.$emit('updateSourceFields', [])
          this.$message.error('获取数据表字段失败！')
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.data-source-container {
  width: 600px;
  .source-title {
    height: 40px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    border: 1px solid #D8D8D8;
    background: #F5F7F9;
    color: #333333;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    padding-left: 35px;
  }
  .source-form {
    border: 1px solid #D8D8D8;
    border-top: 0;
    padding-top: 16px;
    height: 378px;
    .sql-title {
      display: flex;
      height: 38px;
      width: 470px;
      padding: 8px 16px;
      align-items: center;
      gap: 8px;
      border: 1px solid #DCDCDC;
      background: #f7f7f7;
      font-weight: 600;
      color: #333333;
    }
    ::v-deep .CodeMirror {
      height: 155px;
      width: 470px;
      border: 1px solid #ebeef5;
      line-height: 16px;
    }
  }
}
</style>

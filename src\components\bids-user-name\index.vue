<script>
import BidsEllipsTooltip from '../bids-ellips-tooltip'

export default {
  name: 'BidsUserName',
  components: {
    BidsEllipsTooltip
  },
  props: {
    user: String,
    name: String,
    fullName: String
  },
  computed: {
    enName() {
      if (!this.user && this.fullName) {
        return this.fullName.split('(')[0]
      }
      return this.user
    },
    showName() {
      if (this.fullName) return this.fullName
      let str = this.user || ''
      if (this.name) {
        str = `${str}(${this.name})`
      }
      return str
    }
  }
}
</script>

<template>
  <span class="bids-common__user-name">
    <div>
      <img v-if="enName" :src="enName ? `https://rhrc.woa.com/photo/150/${enName}.png` : ''" :alt="name" />
      <svg v-else class="icon" aria-hidden="true">
        <use xlink:href="#des-micon-morentouxiang"></use>
      </svg>
    </div>
    <bids-ellips-tooltip :text="showName"></bids-ellips-tooltip>
  </span>
</template>

<style lang="less" scoped>
.bids-common__user-name {
  display: flex;
  height: 24px;
  align-items: center;
  div {
    display: block;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    border-radius: 50%;
    border: 1px solid #dcdcdc;
    overflow: hidden;
    flex-shrink: 0;
    img {
      position: relative;
      display: block;
      width: 100%;
      height: 100%;
      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        background: url('data:image/gif;base64,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') no-repeat;
        background-size: 100% 100%;
        z-index: 3;
      }
    }
    .icon{
      width: 100%;
      height: 100%;
    }
  }
  span {
    color: #333333;
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    line-height: 22px;
  }
}
</style>

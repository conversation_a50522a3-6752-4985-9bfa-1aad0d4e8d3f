.bids-subscribe-rule {
  margin-bottom: 20px;
  // display: flex;
  .subscribed-table {
    display: inline-block;
    width: 48%;
    margin-bottom: 18px;
    .el-table {
      border: 1px solid #EBEEF5;
      border-bottom: none;
    }
    margin-right: 20px;
    border-radius: 2px;
    .table-expand {
      font-size: 0;
      .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
      }
      label {
        color: #99a9bf;
      }
    }
    .anchor {
      cursor: pointer;
      &:hover {
        color: @color-primary;
      }
    }
  }
  .field-table {
    display: inline-block;
    width: 48%;
    position: relative;
    .arrow {
      border-width: 8px;
      position: absolute;
      display: block;
      top: 50%;
      width: 0;
      height: 0;
      border-color: transparent;
      border-style: solid;
      left: -8px;
      margin-bottom: 3px;
      border-right-color: #EBEEF5;
      border-left-width: 0;
      &::after {
        content: " ";
        border-width: 8px;
        bottom: -8px;
        left: 1px;
        border-left-width: 0;
        position: absolute;
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        border-right-color: #fff;
      }
    }
  }
  .label {
    margin-bottom: 5px;
    font-weight: 500;
    height: 28px;
    line-height: 28px;
    &.label-required {
      &::before {
        content: '*';
        color: #F81D22;
        margin-right: 4px;
      }
    }
    text-overflow:ellipsis; 
    overflow:hidden;
    white-space:nowrap;
  }
  .config-item {
    display: inline-block;
    flex-grow: 1;
    width: 48%;
    .panel{
      border: 1px solid #ebeef5;
      .codemirror-wrap {
        .codemirror-wrap-common();
        .CodeMirror {
          height: 370px;
        }
      }
      
    }
  }
  .description-panel {
    position: inherit;
    display: inline-block;
    vertical-align: top;
    margin-top: 33px;
    margin-left: 20px;
    width: 48%;
  }
  .drawer-title {
    font-size: 16px;
    color: @color-text-dark;
  }
}

.choose-pub-data {
  // height: calc(100vh - 80px - @page-header - 170px);
  // min-height: 1000px;
  // padding-bottom: @page-main-padding-vertical;
  padding: 10px 30px;
  .search-wrap {
    height: calc(100vh - 200px);
    .wrap-header {
      display: flex;
      justify-content: space-between;
      height: 40px;
      align-items: center;
      .wrap-title {
        font-size: 15px;
        color: @color-text-dark;
        font-weight: 500;
      }
      .filter {
        width: 360px;
      }
    }
    .wrap-table {
      margin-top: 10px;
      height: calc(100% - 120px)
    }
    .pagination {
      margin-top: 24px;
    }
    .el-input .el-select{
      width: 110px;
    }
  }
}

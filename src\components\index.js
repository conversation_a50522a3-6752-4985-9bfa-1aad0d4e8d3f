/*
 * @Description: 业务通用组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-14 19:21:00
 */
export const BidsBaseInfo = require('./bids-base-info').default
export const BidsCollectRule = require('./bids-collect-rule').default
export const BidsPublishOutput = require('./bids-publish-output').default
export const BidsOtherSetting = require('./bids-other-setting').default
export const BidsSubscribeRule = require('./bids-subscribe-rule').default
export const BidsSubscribeOutput = require('./bids-subscribe-output').default
export const BidsBeforeAfterEvent = require('./bids-before-after-event').default
export const BidsEditTable = require('./bids-edit-table').default
export const BidsJsonSchemaEditor = require('./bids-json-schema-editor').default
export const BidsSimpleTable = require('./bids-simple-table').default
export const BidsCrontab = require('./bids-crontab').default
export const BidsGlobalTipsBar = require('./bids-global-tips-bar').default
export const BidsPopover = require('./bids-popover').default
export const BidsDatasourceAddDialog = require('./bids-datasource-add-dialog').default
export const BidsUserName = require('./bids-user-name').default
export const BidsEllipsTooltip = require('./bids-ellips-tooltip').default
export const BidsBlockHeader = require('./bids-block-header').default

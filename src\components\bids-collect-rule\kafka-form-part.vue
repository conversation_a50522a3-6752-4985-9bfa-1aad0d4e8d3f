<!--
 * @Description: 采集类型为kafka时的form表单组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-04-19 20:40:09
-->
<template>
  <el-form :model="form" ref="form" :rules="rules" size="small" :label-width="labelWidth" :disabled="!moderateEditable">
    <el-form-item label="主题名称" prop="topic">
      <el-input v-model="form.topic" @change="(val) => { updateSourcesProperties(val, 'topic') }" class="custom-input" v-if="mildEditable"/>
      <span v-else>{{form.topic}}</span>
    </el-form-item>
    <el-form-item label="消费组ID" prop="properties_group_id" required>
      <span>{{form.properties_group_id}}（平台定义）</span>
    </el-form-item>
    <el-form-item label="数据格式" prop="format">
      <el-select v-model="form.format" @change="handleFormatChange" :disabled="!moderateEditable" v-if="mildEditable">
        <el-option v-for="item in formatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <span v-else>{{this.formatOptions.find(item => item.value === form.format).label}}</span>
    </el-form-item>
    <el-form-item label="原始值charset" prop="raw_charset" v-if="form.format === 'raw'">
      <el-input class="custom-input" v-model="form.raw_charset" @change="(val) => { updateSourcesProperties(val, 'raw_charset') }" :disabled="!moderateEditable" v-if="mildEditable"></el-input>
      <span v-else>{{form.raw_charset}}</span>
    </el-form-item>
    <el-form-item label="消费启动位置" prop="scan_startup_mode">
      <el-select v-model="form.scan_startup_mode" @change="(val) => { updateSourcesProperties(val, 'scan_startup_mode') }" v-if="mildEditable">
        <el-option v-for="item in offsetOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <span v-else>{{form.scan_startup_mode ? offsetOptions.find(item => item.value === form.scan_startup_mode).label : ''}}</span>
    </el-form-item>
    <el-form-item label="各分区启动偏移量" prop="specificOffsets" v-if="form.scan_startup_mode === 'specific-offsets'">
      <el-input v-model="form.specificOffsets" @change="(val) => { updateSourcesProperties(val, 'scan_startup_specific-offsets') }" style="width:470px;"/>
    </el-form-item>
    <el-form-item label="启动时间戳" prop="timestamp" v-else-if="form.scan_startup_mode === 'timestamp'">
      <template v-if="mildEditable">
        <el-date-picker
          v-model="pickerTimestamp"
          value-format="timestamp"
          type="datetime"
          align="center"
          placeholder="选择日期时间"
          @change="() => { updateSourcesProperties(form.timestamp, 'scan_startup_timestamp-millis') }">
        </el-date-picker>
        <el-input-number v-model="millisecond" :min="0" :max="999" :precision="0" 
          @change="() => { updateSourcesProperties(form.timestamp, 'scan_startup_timestamp-millis') }" size="mini" style="width:100px;margin: 0 5px;"/>
        <span>毫秒</span>
      </template>
      <span v-else>{{form.timestamp}}</span>
    </el-form-item>
  </el-form>
</template>
<script>
import { mapMutations, mapState } from 'vuex'
import { editable } from 'mixins'

export default {
  name: 'kafka-form-part',
  mixins: [editable],
  props: {
    labelWidth: String,
    pageStatus: String
  },
  computed: {
    ...mapState({
      configData: state => state.currentPublishConfig
    }),
    millisecond: {
      get() {
        if (!this.form.timestamp) return 0
        return this.form.timestamp % 1000
      },
      set(val) {
        this.form.timestamp = (this.pickerTimestamp || 0) + val
      }
    },
    pickerTimestamp: {
      get() {
        if (!this.form.timestamp) return Date.parse(new Date())
        return this.form.timestamp - this.form.timestamp % 1000
      },
      set(val) {
        this.form.timestamp = val + (this.form.millisecond || 0)
      }
    }
  },
  data() {
    return {
      configType: 'publish',
      form: {},
      offsetOptions: [
        { label: 'latest-offset', value: 'latest-offset' }, { label: 'earliest-offset', value: 'earliest-offset' }, { label: 'group-offsets', value: 'group-offsets' },
        { label: 'timestamp', value: 'timestamp' }, { label: 'specific-offsets', value: 'specific-offsets' }
      ],
      rules: {
        topic: [{ required: true, message: '请输入主题名称' }],
        scan_startup_mode: [
          { required: true, message: '请选择消费启动位置' }
        ],
        specificOffsets: [
          { required: true, message: '请输入各分区启动偏移量' },
          { pattern: /^(partition:)\d+(,offset:)\d+(;\1\d+\2\d+)*;?$/, message: '格式不正确' }
        ],
        format: [{ required: true, message: '请选择数据格式' }],
        timestamp: [{ required: true, message: '请选择启动时间戳' }],
        raw_charset: [{ required: true, message: '请输入原始charset' }]
      },
      formatOptions: [
        { label: 'json', value: 'json' }, { label: 'csv', value: 'csv' }, { label: 'parquet', value: 'parquet' }, { label: 'raw', value: 'raw' }
      ]
    }
  },
  created() {
    this.updatePageData()
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    updatePageData() {
      const { topic, scan_startup_mode, 'scan_startup_specific-offsets': specificOffsets, 'scan_startup_timestamp-millis': timestamp, format } = this.configData.job.sources[0].properties
      const properties_group_id = this.strictEditable ? '配置ID_版本' : `${this.configData.configId}_${this.configData.version}` // 生效页面显示具体值，草稿页面显示样例
      this.form = { scan_startup_mode, specificOffsets, timestamp, properties_group_id, topic: topic, format }
    },
    updateSourcesProperties(value, name) {
      const payload = { type: 'publish', keyPath: ['job', 'sources', 0, 'properties', name], value }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleFormatChange(val) {
      this.updateSourcesProperties(val, 'format')
      if (val === 'raw') {
        this.$set(this.form, 'raw_charset', 'utf-8')
        this.updateSourcesProperties('utf-8', 'raw_charset')
      } else {
        this.form.raw_charset = undefined
        this.updateSourcesProperties(undefined, 'raw_charset')
      }
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    }
  }
}
</script>
<style lang="less" scoped>
 @import "~assets/css/bids-collect-rule.less";
</style>

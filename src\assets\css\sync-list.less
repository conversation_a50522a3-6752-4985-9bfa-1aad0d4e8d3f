
.sync-list-page {
  padding: 0 @page-padding-horizontal;
  padding-top: 20px;
  max-width: @page-max-width;

  .el-tabs__header {
    border-bottom: 1px solid #ddd;
  }

  .sync-env {
    color: #333333;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    margin-bottom: 10px;
    position: relative;
  }
  .sync-button {
    position: absolute;
    top: 32px;
    right: 0;
    z-index: 1001;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .filter {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  .page-main {
    padding: @page-main-padding-vertical 0;
    height: calc(100vh - 170px - 32px - 36px);
    min-height: 200px;
    .table {
      height: calc(100% - 70px);
    }
    .pagination {
      margin-top: 24px;
    }
  }
  .el-input .el-select{
    width: 110px;
  }
}

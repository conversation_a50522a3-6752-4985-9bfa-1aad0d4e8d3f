/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-23 12:15:48
 */
import { getRouter } from 'sdc-vue'

const routes = [
  { path: '', redirect: { name: 'Home' } },
  {
    path: '/home',
    name: 'Home',
    component: () => import('views/home/<USER>'),
    meta: { keepAlive: true, sidebarKey: 'home' }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('views/statistics/Statistics'),
    meta: { keepAlive: true, sidebarKey: 'statistics' }
  },
  {
    path: '/statistics/new',
    name: 'StatisticsNew',
    component: () => import('views/statisticsNew/StatisticsNew'),
    meta: { keepAlive: true, sidebarKey: 'statisticsNew' }
  },
  {
    path: '/publish',
    name: 'PublishList',
    component: () => import('views/publish/PublishList'),
    meta: { keepAlive: true, sidebarKey: 'publish' }
  },
  {
    path: '/publish/add',
    name: 'PublishAdd',
    component: () => import('views/publishAdd/PublishAdd'),
    meta: { sidebarKey: 'publish' }
  },
  {
    path: '/publish/build',
    name: 'PublishBuild',
    component: () => import('views/publishBuild/PublishBuild'),
    meta: { sidebarKey: 'publish' }
  },
  {
    path: '/publish/edit',
    name: 'PublishEdit',
    component: () => import('views/publishEdit/PublishEdit'),
    meta: { sidebarKey: 'publish' }
  },
  {
    path: '/publish/view',
    name: 'PublishView',
    component: () => import('views/publishView/PublishView'),
    meta: { sidebarKey: 'publish' }
  },
  {
    path: '/publish/jobs',
    name: 'PublishJobs',
    component: () => import('views/publishJobs/PublishJobs'),
    meta: { sidebarKey: 'publish' }
  },
  {
    path: '/publish/subs',
    name: 'PublishSubs',
    component: () => import('views/publishSubs/PublishSubs'),
    meta: { sidebarKey: 'publish' },
    props: true
  },
  {
    path: '/subscribe',
    name: 'SubscribeList',
    component: () => import('views/subscribe/SubscribeList'),
    meta: { keepAlive: true, sidebarKey: 'subscribe' }
  },
  {
    path: '/subscribe/add',
    name: 'SubscribeAdd',
    component: () => import('views/subscribeAdd/SubscribeAdd'),
    meta: { sidebarKey: 'subscribe' }
  },
  {
    path: '/subscribe/build',
    name: 'SubscribeBuild',
    component: () => import('views/subscribeBuild/SubscribeBuild'),
    meta: { sidebarKey: 'subscribe' }
  },
  {
    path: '/subscribe/edit',
    name: 'SubscribeEdit',
    component: () => import('views/subscribeEdit/SubscribeEdit'),
    meta: { sidebarKey: 'subscribe' },
    props: true
  },
  {
    path: '/subscribe/jobs',
    name: 'SubscribeJobs',
    component: () => import('views/subscribeJobs/SubscribeJobs'),
    meta: { sidebarKey: 'subscribe' },
    props: true
  },
  {
    path: '/subscribe/view',
    name: 'SubscribeView',
    component: () => import('views/subscribeView/SubscribeView'),
    meta: { sidebarKey: 'subscribe' },
    props: true
  },
  {
    path: '/manage/datasource',
    name: 'DataSource',
    component: () => import('views/dataSource/DataSource'),
    meta: { sidebarKey: 'datasource' },
    props: true
  },
  {
    path: '/manage/release',
    name: 'Release',
    component: () => import('views/release/Release'),
    meta: { sidebarKey: 'release' },
    props: true
  },
  {
    path: '/sync',
    name: 'SyncList',
    component: () => import('views/sync/SyncList'),
    meta: { keepAlive: true, sidebarKey: 'sync' }
  },
  {
    path: '/simple',
    name: 'SimpleList',
    component: () => import('views/simple/SimpleList'),
    meta: { keepAlive: true, sidebarKey: 'simple' }
  },
  {
    path: '/simple/config',
    name: 'Config',
    component: () => import('views/simple/details/Config'),
    meta: { sidebarKey: 'simple' }
  }
]
const options = { base: '/bidsplus' } 
export default getRouter(routes, options)

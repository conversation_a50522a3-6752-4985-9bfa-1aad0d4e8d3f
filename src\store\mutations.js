/*
 * @Description: 更改Vuex的store中状态的方法
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-25 20:40:43
 */
import { APP_TYPES } from './types'
import { DataUtil } from 'sdc-core'
import Vue from 'vue'

export default {
  [APP_TYPES.SET_USERINFO](state, payload) {
    state.userInfo = payload
  },
  [APP_TYPES.INIT_PUSHLISH_CONFIG](state) {
    state.currentPublishConfig = DataUtil.clone(state.defaultPublishConfig)
  },
  [APP_TYPES.SET_PUSHLISH_CONFIG](state, payload) {
    state.currentPublishConfig = payload.cache ? DataUtil.clone(payload) : { cache: {}, ...DataUtil.clone(payload) }
  },
  [APP_TYPES.INIT_SUBSCRIBE_CONFIG](state) {
    state.currentSubscribeConfig = DataUtil.clone(state.defaultSubscribeConfig)
  },
  [APP_TYPES.SET_SUBSCRIBE_CONFIG](state, payload) {
    state.currentSubscribeConfig = payload.cache ? DataUtil.clone(payload) : { cache: {}, ...DataUtil.clone(payload) }
  },
  [APP_TYPES.UPDATE_DATA_TO_BE_SUB](state, payload) {
    state.dataToBeSub = payload
    if (state.dataToBeSub.length > 0) {
      const collectType = state.dataToBeSub.every(item => item.collectType === 1) ? 1 : 2
      state.currentSubscribeConfig.collectType = collectType
      // if (collectType === 2 && state.currentSubscribeConfig.setting.retryStrategy.enabled) {
      //   state.currentSubscribeConfig.setting.retryStrategy.enabled = false
      // }
    }
  },
  /**
   * @description: 根据字段路径增/改配置中的字段值
   * @param {Object} state 状态
   * @param {Object} payload 需增/改字段的信息
   * @param {String} payload.type 字段所属的配置</br>publish-发布</br>subscribe-订阅
   * @param {Array} payload.keyPath 每一级字段key值组成的数组
   * @param {*} value 字段的值
   * @return {void}
   */
  [APP_TYPES.UPDATE_CONFIG_ITEM](state, payload) {
    const { type, keyPath, value } = DataUtil.clone(payload)
    let curData = type === 'subscribe' ? state.currentSubscribeConfig : state.currentPublishConfig
    const lastKey = keyPath.pop()
    keyPath.forEach(key => {
      curData = curData[key]
    })
    // 除去前后空格
    const val = typeof value === 'string' ? value.trim() : value
    if (curData[lastKey] === undefined) {
      Vue.set(curData, lastKey, val)
    } else {
      curData[lastKey] = val
    }
  },
  [APP_TYPES.SET_SUBWHITELIST](state, payload) {
    state.subWhiteList = payload
  }
}

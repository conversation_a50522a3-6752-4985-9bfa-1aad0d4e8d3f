<template>
  <div class="sub-frequency-form-part">
    <el-form :model="form" :label-width="labelWidth" :rules="rules" :disabled="['read'].includes(pageStatus)">
      <div class="custom-label">数据订阅频率<span style="color:#999;">（根据订阅的接口类型，如果包含增量的，则订阅也只能为增量的；除非订阅的都是全量接口，才能是全量订阅）</span></div>
      <el-form-item label="订阅周期方式" prop="scheduleType">
        <el-radio-group v-model="form.scheduleType">
          <el-radio label="dependency">基于依赖周期</el-radio>
          <el-radio label="optional">自选触发周期</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="调度周期" prop="schedulePeriodCron" v-if="form.scheduleType === 'optional'">
        <el-popover
          placement="right"
          trigger="click"
          :disabled="['read'].includes(pageStatus)"
          v-model="schedulePopoverVisible">
          <!-- <vcrontab @hide="schedulePopoverVisible=false" @fill="crontabFill" :expression="form.schedulePeriodCron"></vcrontab> -->
          <bids-crontab @confirm="crontabFill" @cancel="schedulePopoverVisible=false"></bids-crontab>
          <el-input v-model="form.schedulePeriodCron" slot="reference" readonly  size="medium"></el-input>
        </el-popover>
      </el-form-item>
      <el-form-item label="依赖周期范围" prop="dependencyCycle" v-if="form.scheduleType === 'dependency'">
        <el-select size="medium" v-model="form.dependencyCycle" placeholder="请选择">
            <el-option v-for="(item, index) in cycleRangeOptions" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <div class="config-item" v-if="form.scheduleType === 'dependency'">
        <div class="header">
          <span class="header-left">
            <span class="label-required">选择依赖的前置任务<span style="color:#999;">（是否依赖其跑完）</span></span>
          </span>
        </div>
        <el-table
          :data="tableData"
          height="300px"
          :header-cell-style="{background:'#f5f7f9'}"
          style="width: 100%">
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableColumn" :key="index"
            :prop="item.prop"
            :label="item.label"
            :min-width="item.minWidth || item.width"
            :show-overflow-tooltip="true"
            align="center">
          </el-table-column>
        </el-table>
      </div>
    </el-form>
  </div>
</template>
<script>
// import vcrontab from 'vcrontab'
import BidsCrontab from 'components/bids-crontab'
export default {
  name: 'sub-frequency-form-part',
  components: {
    // vcrontab
    BidsCrontab
  },
  props: {
    pageStatus: String,
    labelWidth: String
  },
  data() {
    return {
      form: { scheduleTime: {}, schedulePeriodCron: '0 0 0 * * ? *' },
      cycleRangeOptions: [
        { name: '本小时内', id: 'thisHour' }, { name: '当天内', id: 'thisDay' }, { name: '本周内', id: 'thisWeek' },
        { name: '本月内', id: 'thisMonth' }
      ],
      rules: {
        startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
        scheduleType: [{ required: true, message: '请选择订阅调度周期', trigger: 'change' }],
        dependencyCycle: [{ required: true, message: '请选择依赖周期范围', trigger: 'change' }],
        schedulePeriodCron: [{ required: true, message: '请选择调度周期', trigger: 'blur' }]
      },
      tableData: [{ api: '人员基础信息' }],
      tableColumn: [
        { prop: 'api', label: '接口名称', minWidth: '120' },
        { prop: 'app', label: '所属应用', minWidth: '80' },
        { prop: 'describe', label: '接口描述', minWidth: '180' },
        { prop: 'version', label: '版本', minWidth: '60' },
        { prop: 'apiID', label: '接口编码', minWidth: '120' },
        { prop: 'cycleSetting', label: '周期设置', minWidth: '110' }
      ],
      schedulePopoverVisible: false
    }
  },
  methods: {
    crontabFill(obj) {
      this.form.scheduleStrategy.scheduleAtCron = obj.scheduleAtCron
      this.form.scheduleUnit = obj.scheduleUnit
      this.schedulePopoverVisible = false
    }
  }
}
</script>

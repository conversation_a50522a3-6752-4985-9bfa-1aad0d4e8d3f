<!--
 * @Description: 统计页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-24 16:59:12
-->
<template>
  <div class="statistics">
    <div class="count-time">
      <el-date-picker
        v-model="countTimeRange"
        size="small"
        type="datetimerange"
        :picker-options="pickerOptions"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="fetchCountData(), fetchData('pageIndex')"
        value-format="yyyy-MM-dd HH:mm:ss"
        align="center">
      </el-date-picker>
    </div>
    <div class="count-info">
      <div class="count-chart" ref="pieChart"/>
      <div class="count-table">
        <el-table :data="countData" height="100%" border>
          <el-table-column
            label="编号"
            align="center"
            type="index"
            width="80">
          </el-table-column>
          <el-table-column
            v-for="item in countTableColumn"
            :key="item.prop"
            :label="item.label"
            :prop="item.prop"
            :min-width="item.minWidth"
            :show-overflow-tooltip="true"
            align="center"/>
        </el-table>
      </div>
    </div>
    <el-divider></el-divider>
    <div class="list-wrap">
      <div class="filter-bar">
        <el-form :inline="true" :model="form" size="small">
          <el-form-item label="所属应用">
            <el-select v-model="form.appKeys" multiple clearable collapse-tags @change="fetchData('pageIndex')" filterable>
              <el-option v-for="(item, index) in appOptions" :key="`${item.appKey}${index}`" :value="item.appName" :label="item.appKey"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="配置类型">
            <el-select v-model="form.jobCategory" clearable style="width:100px" @change="fetchData('pageIndex')">
              <el-option v-for="item in jobCategoryOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="配置编码">
            <el-select v-model="form.interfaceCode" clearable @change="fetchData('pageIndex')" filterable>
              <el-option v-for="item in configOptions" :key="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="采集方式">
            <el-select v-model="form.jobCollectWay" clearable @change="fetchData('pageIndex')" filterable>
              <el-option v-for="item in jobCollectWayOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="调度状态">
            <el-select v-model="form.scheduleStatus" clearable style="width:100px" @change="fetchData('pageIndex')">
              <el-option v-for="item in scheduleStatusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="text" style="margin: 0;" @click="newData('pageIndex', false)">查询</el-button>
          </el-form-item>
          <!-- <el-form-item label="时间范围">
            <el-date-picker
              v-model="listTimeRange"
              @change="fetchData('pageIndex')"
              size="small"
              type="datetimerange"
              :picker-options="pickerOptions"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right">
            </el-date-picker>
          </el-form-item> -->
        </el-form>
        <el-popover trigger="hover" placement="top" :content="tip">
          <div slot="reference">
            <el-button size="medium" @click="handleBatchRestart">批量重跑</el-button>
          </div>
        </el-popover>
      </div>
      <div class="list-table">
        <el-table :data="listData" border @selection-change="selectionChange">
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column
            v-for="item in listTableColumn"
            :key="item.prop"
            :label="item.label"
            :prop="item.prop"
            :min-width="item.minWidth"
            :show-overflow-tooltip="false"
            :formatter="item.formatter"
            align="center">
            <template v-slot="{ row }" v-if="item.prop === 'scheduleStatus'">
              <span :class="getScheduleStatusClass(row.scheduleStatus)">{{ scheduleStatusType(row) }}</span>
            </template>
          </el-table-column> 
          <el-table-column
            fixed="right"
            label="查看"
            align="center"
            width="100">
            <template slot-scope="scope">
              <el-button type="text" @click="handleShowTaskLog(scope.row)">任务日志</el-button>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            width="100">
            <template slot-scope="scope">
              <el-button type="text" @click="handleOperate('重跑', handleRestart, scope.row)" :loading="scope.row['btnLoading']">重跑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </div>
    <bids-log-dialog ref="logDialog" :label="dialogLabel"></bids-log-dialog>
  </div>
</template>

<script>
import BidsLogDialog from 'components/bids-log-dialog'
import { pagination, formatter } from 'mixins'
import { restartJob, getScheduleInfo, queryTotalInstance, queryJobsBatchRestart } from 'services/common.service'
import { mapState } from 'vuex'

export default {
  mixins: [pagination, formatter],
  components: {
    BidsLogDialog
  },
  data() {
    return {
      dialogLabel: '',
      paginationType: 'remote',
      countTimeRange: [],
      countData: [],
      countTableColumn: [
        { prop: 'count', label: '数量', minWidth: '60' },
        { prop: 'scheduleStatus', label: '状态', minWidth: '150' }
      ],
      // listTimeRange: [],
      listData: [],
      listTableColumn: [
        // { prop: 'scheduleJobName', label: '调度名称', minWidth: '120' },
        { prop: 'appName', label: '所属应用', minWidth: '80' },
        { prop: 'jobCategory', label: '配置类型', minWidth: '80', formatter: this.formatterJob },
        { prop: 'interfaceCode', label: '配置编码', minWidth: '140' },
        { prop: 'collectType', label: '采集类型', minWidth: '80', formatter: this.formatterCollectType },
        { prop: 'jobCollectWay', label: '采集方式', minWidth: '80', formatter: this.formatterJobCollectWay },
        // { prop: 'scheduleId', label: '调度ID', minWidth: '80' },
        { prop: 'scheduleStatus', label: '调度状态', minWidth: '80', formatter: this.scheduleStatusType },
        { prop: 'beginTime', label: '开始时间', minWidth: '80' },
        { prop: 'endTime', label: '结束时间', minWidth: '80' },
        { prop: 'restartTime', label: '重跑时间', minWidth: '80' }
      ],
      form: {},
      appOptions: [],
      jobCategoryOptions: [
        { value: 'PUB', label: '发布' },
        { value: 'SUB', label: '订阅' }
      ],
      jobCollectWayOptions: [
        { value: 'PUSH', label: '推送' },
        { value: 'PULL', label: '拉取' }
      ],
      configOptions: [],
      scheduleStatusOptions: [
        { value: 1, label: '运行中' },
        { value: 2, label: '失败' },
        { value: 3, label: '成功' }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      selectedRows: []
    }
  },
  computed: {
    ...mapState(['dt_scheduleState']),
    option() {
      return {
        title: {
          text: '实例状态统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '实例状态',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            data: this.countData.map(res => ({ value: res.count, name: res.scheduleStatus }))
          }
        ]
      }
    },
    runningInstanceInfo () {
      return this.countData.find(item => item.scheduleStatus === this.dt_scheduleState[1]) || {}
    },
    tip () {
      return `请选择需要重跑的实例（一次批量重启的实例数最大值为20）`
    }
  },
  created() {
    // const end = new Date()
    // const start = new Date()
    // start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
    // this.countTimeRange = [start, end]
    // this.listTimeRange = [start, end]
    // this.fetchCountData()
    // this.fetchData()
    this.newData()
  },
  mounted() {
    this.draw()
  },
  methods: {
    handleShowTaskLog(row) {
      this.dialogLabel = '运行任务ID'
      this.$refs.logDialog.show({ jobName: row.scheduleJobName, jobInstId: row.scheduleId })
    },
    handleOperate(optType, optFunc, row) {
      this.$confirm('此操作将' + optType + '该任务实例，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        optFunc(row)
      }).catch(() => {})
    },
    handleRestart(row) {
      // 重跑
      const params = { jobInstId: row.scheduleId }
      const postData = { type: 'restart' }
      row.btnLoading = true
      restartJob(params, postData).then(res => {
        this.$message.success('操作成功！')
        row.btnLoading = false
        row.scheduleStatus = 1
        this.fetchCountData()
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        row.btnLoading = false
      })
    },
    fetchData(pageIndex) {
      for (const key in this.form) {
        const val = this.form[key]
        if (val === '' || val.length === 0) {
          delete this.form[key]
        }
      }
      if (pageIndex === 'pageIndex') {
        this.pageIndex = 1
      }
      const paginator = { pageSize: this.pageSize, pageIndex: this.pageIndex }
      // const content = { ...this.form, startTime: this.listTimeRange[0], endTime: this.listTimeRange[1] }
      const content = { ...this.form, startTime: this.countTimeRange[0], endTime: this.countTimeRange[1], dataSchedulePlatform: true }
      queryTotalInstance({ content, paginator }).then(res => {
        this.listData = (res.content || []).map(item => ({ btnLoading: false, ...item }))
        this.recordCount = res.paginator.recordCount
      }).catch(res => {
        this.$message.error('获取数据失败！原因：' + res.message || '暂无')
      })
    },
    fetchCountData() {
      const params = { startTime: this.countTimeRange[0], endTime: this.countTimeRange[1], dataSchedulePlatform: true }
      getScheduleInfo(params).then(res => {
        this.countData = (res.scheduleStatusCounts || []).filter(item => item.scheduleStatus !== '-1').map(item => ({ count: item.count, scheduleStatus: this.dt_scheduleState[item.scheduleStatus] }))
        // 生成所属应用下拉列表
        const appMap = new Map()
        res.configs.forEach(item => {
          appMap.set(item.appKey, item.appName)
        })
        const opt = []
        appMap.forEach((key, value) => { 
          opt.push({ appKey: key, appName: value })
        })
        this.appOptions = opt.sort(this.compare)

        // 生成配置编码下拉列表
        let configOptions = res.configs.map(item => item.interfaceCode)
        configOptions = [...new Set(configOptions)]
        this.configOptions = configOptions.sort(this.compare)
        this.draw()
      }).catch(res => {
        this.$message.error('获取数据失败！原因：' + res.message || '暂无')
      })
    },
    // 比较字母大小，不区分大小写
    compare(a, b) {
      let x = ''
      let y = ''
      if (a.appKey) {
        x = a.appKey.toLowerCase()
        y = b.appKey.toLowerCase()
      } else {
        x = a.toLowerCase()
        y = b.toLowerCase()
      }
      return x < y ? -1 : (x > y ? 1 : 0)
    },
    draw() {
      const pieChart = this.$echarts.init(this.$refs.pieChart)
      pieChart.setOption(this.option)
    },
    newData(pageIndex, refresh = true) {
      if (refresh) {
        var timestamp = new Date().getTime() - 3600 * 1000 * 24 * 1
        var d = new Date() // 创建一个指定的日期对象
        var g = new Date(timestamp) // 创建一个指定的日期对象
        let start = this.formatDate(g) || ''
        let end = this.formatDate(d) || ''
        this.countTimeRange = [start, end]
      }
      // this.listTimeRange = [start, end]
      this.fetchCountData()
      this.fetchData(pageIndex)
    },
    formatDate(now) { 
      var year = now.getFullYear() // 取得4位数的年份
      var month = now.getMonth() + 1 // 取得日期中的月份，其中0表示1月，11表示12月
      var date = now.getDate() // 返回日期月份中的天数（1到31）
      var hour = now.getHours() // 返回日期中的小时数（0到23）
      var minute = now.getMinutes() // 返回日期中的分钟数（0到59）
      var second = now.getSeconds() // 返回日期中的秒数（0到59）
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (date < 10 ? '0' + date : date) + ' ' + (hour < 10 ? '0' + hour : hour) + ':' + (minute < 10 ? '0' + minute : minute) + ':' + (second < 10 ? '0' + second : second)
    },
    scheduleStatusType(row) {
      let type = row.scheduleStatus
      if (!type) return ''
      type = Number.parseInt(type)
      return this.scheduleStatusOptions.find(v => v.value === type).label
    },
    getScheduleStatusClass(status) {
      return {
        1: 'bids-blue-dot',
        2: 'bids-red-dot',
        3: 'bids-green-dot'
      }[Number.parseInt(status)]
    },
    formatterJob(row) {
      if (!row.jobCategory) return ''
      return this.jobCategoryOptions.find(v => v.value === row.jobCategory).label
    },
    formatterJobCollectWay (row) {
      if (row.jobCategory === 'PUB') return ''
      if (!row.jobCollectWay) return ''
      return this.jobCollectWayOptions.find(v => v.value === row.jobCollectWay).label
    },
    handleBatchRestart () {
      // 选中的实例数量
      const selected = this.selectedRows.length
      if (selected === 0) {
        return
      }
      // 运行中的实例数量
      const { count = 0 } = this.runningInstanceInfo
      const max = Number.parseInt(count) + Number.parseInt(selected)
      if (max > 20) {
        this.$message.warning(`当前运行中的实例数：${count}，批量选择的实例数：${selected}，已超出最大允许重启实例数20`)
        return 
      }
      this.$confirm('此操作将重跑勾选的任务实例，是否继续?', '提示', { type: 'warning' })
        .then(() => {
          const params = this.selectedRows.map(row => {
            row.btnLoading = true
            return row.scheduleId
          })
          queryJobsBatchRestart(params).then(res => {
            this.$message.success('操作成功！')
            this.selectedRows.forEach(row => {
              row.scheduleStatus = 1
              row.btnLoading = false
            })
            this.fetchCountData()
          }).catch(err => {
            this.$message.error('操作失败！原因：' + err.message || '暂无')
          })
        })
        .catch(() => {})
    },
    selectionChange (val) {
      this.selectedRows = val
    }
  }
}
</script>

<style lang="less" scoped>
  @import "~assets/css/statistics.less";
</style>

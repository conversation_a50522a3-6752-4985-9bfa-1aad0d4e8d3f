<template>
  <el-popover placement="right" width="150" trigger="manual" v-model="rightClickFlag" class="popover" :style="`left:${clientX}px;top:${clientY}PX`">
    <ul>
      <li v-for="item in contextMenu" :key="item.value" @click="goNewTab(item.value)">{{ item.label }}</li>
    </ul>
  </el-popover>
</template>

<script>
export default {
  name: 'bids-popover',
  data () {
    return {}
  },
  props: {
    rightClickFlag: {
      type: Boolean,
      default: false
    },
    clientX: {
      type: Number
    },
    clientY: {
      type: Number
    },
    contextMenu: {
      type: Array
    }
  },
  methods: {
    goNewTab (value) {
      this.$emit('handleGoNewTab', value)
    }
  }
}
</script>

<style lang="less" scoped>
.popover {
  position: fixed;
  z-index: 500;
  ul {
    // text-align: center;
  }
  li {
    padding: 3px;
  }
  li:hover {
    list-style: none;
    color: #3464e0;
    padding: 3px;
    background-color: #fafafa;
  }
}
</style>

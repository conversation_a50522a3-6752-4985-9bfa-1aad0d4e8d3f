<!--
 * @Description: 可编辑式表格组件，这里用于增删改输入/输出数据的字段
 * @Autor: kenoxia
 * @LastEditTime: 2021-05-17 20:53:18
-->
<template>
  <div class="bids-edit-table">
    <el-table 
      ref="table"
      :data="data"
      :key="tableKey"
      height="100%"
      row-key="bidsRowKey"
      :header-cell-style="{background:'#f5f7f9', height: '60px'}"
      style="width: 100%">
      <el-table-column width="50" align="center" class-name="sort-column" label="拖拽" v-if="canDrag">
        <template>
          <i class="el-icon-d-caret"></i>
        </template>
      </el-table-column>
      <el-table-column
        v-for="item in tableColumn" :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :min-width="item.minWidth || item.width"
        :show-overflow-tooltip="true"
        align="center">
        <template slot="header" v-if="item.edit.type === 'checkbox' && !item.edit.onlyChecked">
           <el-checkbox v-model="headerCheckAll[item.prop].checked" :indeterminate="headerCheckAll[item.prop].indeterminate" @change="(val) => { handleCheckAll(val, item.prop) }"/>
           <span style="margin-left:5px;">{{item.label}}</span>
        </template>
        <template slot-scope="scope">
          <span v-if="item.edit && scope.row.editing">
            <el-input v-if="item.edit.type === 'input'" size="small" v-model="scope.row[item.prop]"/>
            <el-select v-else-if="item.edit.type === 'select'" v-model="scope.row[item.prop]" size="small" placeholder="">
              <el-option v-for="(option, index) in item.edit.options" :key="index" :value="option.value" :label="option.label"></el-option>
            </el-select>
            <el-autocomplete v-else-if="item.edit.type === 'autocomplete'" size="small" v-model="scope.row[item.prop]" :fetch-suggestions="item.edit.suggestions"></el-autocomplete>
            <el-checkbox v-else-if="item.edit.type === 'checkbox'" v-model="scope.row[item.prop]">是</el-checkbox>
            <span v-else-if="item.edit.type === null">{{item.formatter ? item.formatter(scope.row) : scope.row[item.prop]}}</span>
          </span>
          <span v-else>{{item.formatter ? item.formatter(scope.row) : scope.row[item.prop]}}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!disabled"
        label="操作"
        align="center"
        width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.editing">
            <el-button @click="handleSave(scope.$index, scope.row)" type="text" size="small">保存</el-button>
            <el-button @click="handleCancel(scope.$index, scope.row)" type="text" size="small">取消</el-button>
          </span>
          <span v-else>
            <el-button @click="handleEdit(scope.row)" type="text" size="small" v-if="options.includes('modify')">修改</el-button>
            <el-button @click="handleDelete(scope.$index)" type="text" size="small" style="color:#f81d22;" v-if="options.includes('delete')">删除</el-button>
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="type === 'sink' && configType ==='subscribe' && !disabled" label="业务操作" align="center" width="100">
        <template v-slot="{row}">
          <el-button @click="handleDesensite(row)" type="text" size="small">脱敏设置</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 脱敏规则设置弹窗 -->
    <desensite-rule-dialog v-model="showDialog" :configData="configData" :allData="data" :currentRowData="currentRowData" @UpdateDesensite="UpdateDesensite" @emitData="emitData"></desensite-rule-dialog>
  </div>
</template>
<script>
import Sortable from 'sortablejs'
import DesensiteRuleDialog from './desensite-rule-dialog.vue'
export default {
  name: 'bids-edit-table',
  components: {
    DesensiteRuleDialog
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    tableColumn: {
      type: Array,
      default: () => []
    },
    value: {
      type: Array,
      required: true
    },
    customSaveCheck: {
      type: Function
    },
    customSaveRow: {
      type: Function
    },
    options: {
      type: Array,
      default: () => ['drag', 'delete', 'modify']
    },
    type: String,
    configType: String,
    configData: Object
  },
  computed: {
    incrementCols() {
      return this.tableColumn.filter(item => item.edit.increment)
    },
    notNullCols() {
      return this.tableColumn.filter(item => item.notNull) // 不能为空的列
    },
    noRepeatCols() {
      return this.tableColumn.filter(item => item.noRepeat) // 值不能重复的列
    },
    onlyCheckedCols() {
      return this.tableColumn.filter(item => item.edit.type === 'checkbox' && item.edit.onlyChecked) // 只能有一个值为true的列
    },
    headerCheckAll: {
      get() {
        const cols = this.tableColumn.filter(item => item.edit.type === 'checkbox' && !item.edit.onlyChecked)
        let obj = {}
        cols.forEach(item => {
          const checkedCount = this.data.filter(row => row[item.prop]).length
          obj[item.prop] = { checked: checkedCount > 0 && checkedCount === this.data.length, indeterminate: checkedCount > 0 && checkedCount < this.data.length }
        })
        return { ...obj }
      },
      set(val) {
      }
    },
    tableKey() {
      return this.tableColumn.map(item => item.prop).join('_')
    },
    canDrag() {
      return !this.disabled && this.options.includes('drag') 
    }
  },
  data() {
    return {
      adding: false,
      changeByEmit: false,
      data: [],
      originRow: null,
      showDialog: false,
      currentRowData: {}
      // headerCheckAll: { enableEncryption: { checked: false, indeterminate: false } }
    }
  },
  watch: {
    value(val, oldVal) {
      if (this.changeByEmit) {
        // 因组件内部emit事件引发的value值改变
        this.changeByEmit = false
        return
      }
      this.setData()
    },
    tableKey(val, oldVal) {
      if (val !== oldVal && !this.disabled) {
        this.$nextTick(() => this.rowDrop())
      }
    },
    canDrag(val, oldVal) {
      if (val) {
        this.rowDrop()
      }
    }
  },
  created() {
    this.setData()
  },
  mounted() {
    this.canDrag && this.rowDrop()
  },
  methods: {
    setData() {
      this.data = this.value.map((item, index) => ({ editing: false, bidsRowKey: index, ...item }))
    },
    rowDrop() {
      const tbody = this.$refs['table'].$el.querySelector('.el-table__body-wrapper tbody')
      const _this = this
      Sortable.create(tbody, {
        handle: '.sort-column',
        onEnd({ newIndex, oldIndex }) {
          // if (oldIndex === newIndex) return
          const currRow = _this.data.splice(oldIndex, 1)[0]
          _this.data.splice(newIndex, 0, currRow)
          // 重置自增列的值
          const low = Math.min(newIndex, oldIndex)
          const high = Math.max(newIndex, oldIndex)
          _this.incrementCols.forEach(item => {
            for (let index = low; index <= high; index++) {
              _this.data[index][item.prop] = index + 1
            }
          })
          _this.emitData()
        }
      })
    },
    handleSave(index, row) {
      for (const col of this.notNullCols) {
        if (row[col.prop] === '' || row[col.prop] === null || row[col.prop] === undefined) {
          this.$message.warning(`${col.label}不能为空！`)
          return
        }
      }
      for (const col of this.noRepeatCols) {
        if (this.data.filter(item => item[col.prop] === row[col.prop]).length > 1) {
          this.$message.warning(`存在重复的${col.label}！`)
          return
        }
      }
      const isOk = this.customSaveCheck(index, row)
      if (!isOk) return
      // for (const col of this.onlyCheckedCols) {
      //   row[col.prop] === true && this.data.forEach((item, itemIndex) => {
      //     itemIndex !== index && (item[col.prop] = false) // 若保存行的该列值为true，则其他行改为false
      //   })
      // }
      if (this.adding) {
        this.adding = false
      }
      row.editing = !row.editing
      if (this.customSaveRow) {
        this.customSaveRow(row)
      }
      this.emitData()
    },
    handleCancel(index, row) {
      if (this.adding) {
        this.data.pop()
        this.adding = false
      } else {
        this.data.splice(index, 1, this.originRow)
        this.originRow = null
      }
      row.editing = !row.editing
    },
    handleEdit(row) {
      if (this.checkEditingStatus()) return
      this.originRow = { ...row }
      row.editing = !row.editing
    },
    handleDelete(index) {
      if (this.checkEditingStatus()) return
      const deleteRow = this.data.splice(index, 1)[0]
      this.incrementCols.forEach(item => {
        this.data.forEach(row => {
          if (row[item.prop] > deleteRow[item.prop]) {
            row[item.prop] = row[item.prop] - 1
          }
        })
      })
      this.emitData()
    },
    addRow() {
      if (this.checkEditingStatus()) return
      const newRowKey = this.data.length === 0 ? 0 : Math.max(...(this.data.map(item => item.bidsRowKey))) + 1 // 保证增删后的唯一性
      let newRow = { editing: true, bidsRowKey: newRowKey }
      this.tableColumn.forEach(item => {
        if (item.edit.increment) {
          newRow[item.prop] = this.data.length + 1
        }
        if (item.edit.type === 'checkbox') {
          newRow[item.prop] = false
        }
      })
      this.data.push(newRow)
      this.adding = true
      this.$refs['table'].doLayout()
      this.$nextTick(() => {
        const bodyWrapper = this.$refs['table'].$refs.bodyWrapper
        const tableBody = bodyWrapper.childNodes[0]
        bodyWrapper.scrollTop = tableBody.clientHeight
      })
    },
    handleCheckAll(val, prop) {
      this.data.forEach(row => {
        row[prop] = val
      })
      // this.emitData()
    },
    checkEditingStatus() {
      if (this.data.some(item => item.editing === true)) {
        this.$message.warning('请先保存当前编辑项')
        return true
      }
      return false
    },
    emitData() {
      const outData = this.data.map(item => {
        const { editing, bidsRowKey, ...others } = item
        return others
      })
      this.$emit('input', outData)
      this.$emit('change', outData)
      this.changeByEmit = true
    },
    // 脱敏设置事件
    handleDesensite(row) {
      this.showDialog = true
      this.currentRowData = row
    },
    UpdateDesensite(convert) {
      this.currentRowData.convert = convert
      this.emitData()
    }
  }
}
</script>
<style lang="less">
  @import "~assets/css/bids-edit-table.less";
</style>

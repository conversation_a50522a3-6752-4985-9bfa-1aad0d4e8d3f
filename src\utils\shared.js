/*
 * @Description: 公共方法
 * @Autor: kenoxia
 * @LastEditTime: 2021-01-04 17:50:56
 */
export function isDef(val) {
  return val !== undefined && val !== null
}
/**
 * @description: 将日期转换为cron表达式
 * @param {String} dateStr
 * @return {String} cron表达式
 */
export function convertDateToCron(dateStr) {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()
  return `${second} ${minute} ${hour} ${day} ${month} ? ${year}`
}
/**
 * 检测是否为空值
 * @param {string} val 
 * @returns {boolean}
 */
export function isEmptyValue(val) {
  return val === undefined || val === null || val === ''
}

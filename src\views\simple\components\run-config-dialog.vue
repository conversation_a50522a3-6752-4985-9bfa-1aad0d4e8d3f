<template>
  <div class="run-config-dialog">
    <el-dialog title="运行配置" :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" width="651px" top="10vh">
      <el-form ref="form" :model="form" :rules="rules" label-width="104px" size="small">
        <el-form-item label="调度类型" prop="scheduleStrategy.scheduleType">
          <el-radio-group v-model="form.scheduleStrategy.scheduleType" @change="handleScheduleTypeChange">
            <el-radio label="OUT">外部触发</el-radio>
            <el-radio label="REPEAT">调度触发</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="form.scheduleStrategy.scheduleType === 'OUT'">
        <el-form-item label="任务触发接口">
          <div class="urlPanel">
            <span :data-clipboard-text="runJobUrl" id="runJobUrl">{{ runJobUrl }}</span>
            <span style="margin-left:5px;">[POST]</span>
            <div class="event-icon copy-icon runJobUrl" data-clipboard-target="#runJobUrl" @click="handleCopyUrl('.runJobUrl')"><i class="el-icon-document-copy"></i></div>
          </div>
        </el-form-item>
        <el-form-item label="状态查询接口">
          <div class="urlPanel">
            <span :data-clipboard-text="getStatusUrl" id="getStatusUrl">{{getStatusUrl}}</span>
            <span style="margin-left:5px;">[GET]</span>
            <div class="event-icon copy-icon getStatusUrl" data-clipboard-target="#getStatusUrl" @click="handleCopyUrl('.getStatusUrl')"><i class="el-icon-document-copy"></i></div>
          </div>
        </el-form-item>
        </template>
        <el-form-item label="调度周期" prop="scheduleStrategy.scheduleAtCron" v-if="form.scheduleStrategy.scheduleType === 'REPEAT'">
          <el-popover
            placement="right"
            trigger="click"
            @show="$refs['crontab'].setCronTab(form.scheduleStrategy.scheduleUnit, form.scheduleStrategy.scheduleAtCron)"
            v-model="schedulePopoverVisible">
            <bids-crontab ref="crontab" @confirm="crontabFill" @cancel="schedulePopoverVisible=false"></bids-crontab>
            <el-input v-model="form.scheduleStrategy.scheduleAtCron" slot="reference" readonly class="custom-input"></el-input>
          </el-popover>
        </el-form-item>
        <el-form-item label="开始时间" prop="scheduleStrategy.scheduleBeginTime" v-if="form.scheduleStrategy.scheduleType === 'REPEAT'">
          <el-date-picker
            v-model="form.scheduleStrategy.scheduleBeginTime"
            type="datetime"
            align="center"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="资源配置" prop="resources" required>
          <span>CPU核数<el-input-number v-model="form.resources.cores" size="mini" :precision="0" :min="1" :max="dt_system.maxCores" style="width:102px;"/>，</span>
          <span>内存大小<el-input-number v-model="form.resources.memoryMB" size="mini" :precision="0" :min="1024" :max="dt_system.maxMemoryMB" style="width:102px;"/>M，</span>
          <span>并行度<el-input-number v-model="form.resources.parallelism" :disabled="true" size="mini" :precision="0" :min="1" :max="dt_system.maxParallelism" style="width:102px;"/></span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="medium">取消</el-button>
        <el-button size="medium" type="primary" :disabled="row.jobStatus !== 1" @click="handleConfirm">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import BidsCrontab from 'components/bids-crontab'
import ClipboardJS from 'clipboard'
import { conf } from 'utils'
import { updateOnlineSimpleConfig } from 'services/simple.service'

export default {
  name: 'run-config-dialog',
  components: {
    BidsCrontab
  },
  data() {
    return {
      row: {},
      visible: false,
      schedulePopoverVisible: false,
      form: {
        scheduleStrategy: { scheduleBeginTime: '', ifScheduleUnfinished: '', scheduleType: '', scheduleAtCron: '', scheduleUnit: '' }, // 调度策略
        timeoutStrategy: { action: null, timeoutMins: 60, retryCount: 1, retryDelay: 1000 }, // 超时策略
        failureStrategy: { action: null, retryCount: 1, retryDelay: 1000 }, // 失败策略,
        resources: { cores: 1, parallelism: 1, memoryMB: 1024 } // 资源配置
      },
      rules: {
        'scheduleStrategy.scheduleType': [
          { required: true, message: '请选择触发方式', trigger: 'change' }
        ],
        'scheduleStrategy.scheduleAtCron': [
          { required: true, message: '请设置调度周期', trigger: 'change' }
        ],
        'scheduleStrategy.scheduleBeginTime': [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        'timeoutStrategy.action': [
          { required: true, message: '请选择超时策略', trigger: 'change' }
        ],
        'timeoutStrategy.timeoutMins': [
          { required: true, message: '请输入超时时长', trigger: 'change' }
        ],
        'timeoutStrategy.retryCount': [
          { required: true, message: '请输入重试次数', trigger: 'blur' }
        ],
        'failureStrategy.action': [
          { required: true, message: '请选择失败策略', trigger: 'change' }
        ],
        'failureStrategy.retryCount': [
          { required: true, message: '请输入重试次数', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState(['dt_system']),
    runJobUrl() {
      const urlPart = `${this.$route.query.configId}/v${this.$route.query.version}`
      const runJobUrl = conf('esb').main + `/thirdpart/jobs/${urlPart}/runJob`
      return runJobUrl
    },
    getStatusUrl() {
      const urlPart = `${this.$route.query.configId}/v${this.$route.query.version}`
      const getStatusUrl = conf('esb').main + `/thirdpart/jobs/${urlPart}/jobStatus`
      return getStatusUrl
    }
  },
  methods: {
    show(row) {
      this.row = row
      this.initData(row)
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const subData = {
            configId: this.row.configId,
            jobSetting: {
              scheduleStrategy: this.form.scheduleStrategy,
              timeoutStrategy: this.form.timeoutStrategy,
              failureStrategy: this.form.failureStrategy,
              resources: this.form.resources
            }
          }
          updateOnlineSimpleConfig(subData).then(res => {
            this.$message.success('操作成功！')
            this.handleClose()
            this.$emit('update')
          }).catch(res => {
            this.$message.error('操作失败！原因：' + res.message || '暂无')
          })
        }
      })
    },
    initData(row) {
      const { scheduleStrategy, timeoutStrategy, failureStrategy, resources } = row.jobSetting
      this.form = {
        scheduleStrategy,
        timeoutStrategy,
        failureStrategy,
        resources
      }
    },
    crontabFill(cronTab) {
      this.form.scheduleStrategy.scheduleAtCron = cronTab.scheduleAtCron
      this.form.scheduleStrategy.scheduleUnit = cronTab.scheduleUnit
      this.schedulePopoverVisible = false
    },
    handleCopyUrl(className) {
      const clipboard = new ClipboardJS(className)
      const _this = this
      clipboard.on('success', function () {
        _this.$sdc.toast('复制成功')
      })
      clipboard.on('error', function () {
        _this.$sdc.toast('复制失败')
      })
    },
    handleScheduleTypeChange(value) {
      if (value === 'OUT') {
        this.form.scheduleStrategy.scheduleBeginTime = ''
        this.form.scheduleStrategy.scheduleAtCron = ''
        this.form.scheduleStrategy.scheduleUnit = ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.run-config-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px 20px 10px 20px;
  }
  .pagination {
    margin-top: 20px;
  }
  .urlPanel {
    width: 420px;
    position: relative;
    border-radius: 6px;
    line-height: 24px;
    padding: 3px 35px 3px 10px;
    border: 1px solid #dcdcdc;
    word-break:break-all;
  .event-icon.copy-icon {
    position: absolute;
    top: 5px;
    right: 10px;
  }
}
}
</style>

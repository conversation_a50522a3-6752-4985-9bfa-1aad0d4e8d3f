<template>
  <div class="run-config-dialog">
    <el-dialog title="运行配置" :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" width="651px" top="10vh">
      <el-form ref="form" :model="form" :rules="rules" label-width="104px" size="small">
        <el-form-item label="调度类型" prop="scheduleType">
          <el-radio-group v-model="form.scheduleType" @change="handleScheduleTypeChange">
            <el-radio label="OUT">外部触发</el-radio>
            <el-radio label="REPEAT">平台周期性触发</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="form.scheduleType === 'OUT'">
          <el-form-item label="任务触发接口">
            <el-input v-model="form.runJobUrl"></el-input>
          </el-form-item>
          <el-form-item label="状态查询接口">
            <el-input v-model="form.runJobUrl"></el-input>
          </el-form-item>
        </template>
        <el-form-item label="调度周期" prop="scheduleAtCron" v-if="form.scheduleType === 'REPEAT'">
          <el-popover
            placement="right"
            trigger="click"
            @show="$refs['crontab'].setCronTab(form.scheduleUnit, form.scheduleAtCron)"
            v-model="schedulePopoverVisible">
            <bids-crontab ref="crontab" @confirm="crontabFill" @cancel="schedulePopoverVisible=false"></bids-crontab>
            <el-input v-model="form.scheduleAtCron" slot="reference" readonly class="custom-input"></el-input>
          </el-popover>
        </el-form-item>
        <el-form-item label="资源配置" prop="resources" required>
          <span>CPU核数<el-input-number v-model="form.cores" size="mini" :precision="0" :min="1" :max="dt_system.maxCores" style="width:102px;"/>，</span>
          <span>内存大小<el-input-number v-model="form.memoryMB" size="mini" :precision="0" :min="1024" :max="dt_system.maxMemoryMB" style="width:102px;"/>M，</span>
          <span>并行度<el-input-number v-model="form.parallelism" :disabled="true" size="mini" :precision="0" :min="1" :max="dt_system.maxParallelism" style="width:102px;"/></span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="medium">取消</el-button>
        <el-button size="medium" type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import BidsCrontab from 'components/bids-crontab'

export default {
  name: 'run-config-dialog',
  components: {
    BidsCrontab
  },
  data() {
    return {
      visible: false,
      schedulePopoverVisible: false,
      form: {},
      rules: {
      }
    }
  },
  computed: {
    ...mapState(['dt_system'])
  },
  methods: {
    show(row) {
      this.fetchData()
      this.visible = true
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    // 调用同步接口
    handleConfirm() {
      
    },
    fetchData() {},
    crontabFill(cronTab) {
      console.log('%c [ cronTab ]-86', 'font-size:13px; background:pink; color:#bf2c9f;', cronTab)
      this.form.scheduleAtCron = cronTab.scheduleAtCron
      this.schedulePopoverVisible = false
    },
    handleScheduleTypeChange(value) {
      // const payload = { type: this.configType, keyPath: ['setting', ', 'scheduleType'], value }
      // this.UPDATE_CONFIG_ITEM(payload)
      // if (value === 'OUT') {
      //   this.form.scheduleBeginTime = ''
      //   this.handleBeginTimeChange('')
      //   this.crontabFill({ scheduleAtCron: '', scheduleUnit: '' })
      // } else {
      //   !this.form.scheduleUnit && this.crontabFill({ scheduleAtCron: '0 0 0 * * ? *', scheduleUnit: 'Day' })
      // }
    }
  }
}
</script>

<style lang="less" scoped>
.run-config-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px 20px 10px 20px;
  }
  .pagination {
    margin-top: 20px;
  }
}
</style>

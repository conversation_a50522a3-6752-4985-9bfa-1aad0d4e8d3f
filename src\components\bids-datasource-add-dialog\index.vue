<!--
 * @Description: 数据源弹窗组件，用于新增/修改数据源
 * @Autor: kenoxia
 * @LastEditTime: 2021-07-27 19:57:41
-->
<template>
  <div class="bids-datasource-dialog">
    <el-dialog :title="title" :visible.sync="visible" :before-close="handleClose" width="710px" :close-on-click-modal="false">
      <el-form :model="form" size="medium" :rules="rules" label-width="160px" ref="form" v-loading="loading">
        <el-form-item label="数据源所属应用" prop="dsAppKey">
          <!-- <span>{{form.dsAppName || form.dsAppKey}}</span> -->
          <el-select v-model="form.dsAppKey" placeholder="请选择" filterable @change="handleAppChange">
            <el-option v-for="item in dsAppOptions" :key="item.dsAppKey" :label="item.dsAppName" :value="item.dsAppKey"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据源ID" v-if="type === 'update'">
          <span>{{form.dsId}}</span>
        </el-form-item>
        <el-form-item label="数据库类型" prop="dsType">
          <el-select v-model="form.dsType" placeholder="请选择" @change="handleTypeChange" :disabled="type === 'update'">
            <el-option v-for="(item, index) in options" :key="index" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.dsType === 'FrontRepo'" label="Schema名称" prop="dsName">
          <el-select v-model="form.dsName" :loading="schemaLoading" @change="handleDsNameChange">
            <el-option v-for="item in schemaOptions" :key="item.schemaName" :value="item.schemaName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-else :label="form.dsType === 'HTTP' ? '接口名称' : '数据源名称'" prop="dsName">
          <el-input v-model="form.dsName" placeholder="所选应用内名称必须唯一" class="custom-input"></el-input>
        </el-form-item>
        <template v-if="['jdbc', 'elasticsearch-6', 'elasticsearch-7', 'mongo', 'MySqlTenant', 'starrocks'].includes(dt_dsConnector[form.dsType]) && form.dsType !== 'FrontRepo'">
          <el-form-item label="数据源地址" prop="dsUrl">
            <el-input v-model="form.dsUrl" :placeholder="urlPlaceholder"></el-input>
            <el-popover placement="right" width="400" trigger="hover">
              <el-table :data="parameterData">
                <el-table-column width="150" property="key" label="数据库"></el-table-column>
                <el-table-column width="200" property="value" label="参数"></el-table-column>
              </el-table>
              <el-button style="margin-left: 10px;" slot="reference" @click="dsUrlPara">添加优化参数</el-button>
            </el-popover>
          </el-form-item>
        </template>
        <template v-if="dt_dsConnector[form.dsType] === 'filesystem'">
          <el-form-item label="数据集群" prop="dsCluster">
            <el-select v-model="form.dsCluster" placeholder="请选择">
              <el-option v-for="item in clusterOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </template>
        <template v-if="dt_dsConnector[form.dsType] === 'kafka'">
          <el-form-item label="Broker集群地址" prop="dsUrl">
            <el-input v-model="form.dsUrl" placeholder="ip:port,ip:port,ip:port"></el-input>
          </el-form-item>
          <el-form-item label="认证方式" prop="dsAuth">
            <el-select v-model="form.dsAuth" placeholder="请选择" @change="handleDsAuthChange">
              <el-option v-for="item in authOptions" :key="item.value" :value="item.value" :label="item.label" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </template>
        <template
          v-if="(['jdbc', 'elasticsearch-6', 'elasticsearch-7', 'MySqlTenant', 'filesystem', 'starrocks'].includes(dt_dsConnector[form.dsType]) && form.dsType !== 'FrontRepo') || (dt_dsConnector[form.dsType] === 'kafka' && form.dsAuth === 'SASL_PLAINTEXT')">
          <el-form-item label="数据源账号" :prop="['jdbc', 'kafka', 'MySqlTenant', 'filesystem', 'starrocks'].includes(dt_dsConnector[form.dsType]) ? 'dsUserName' : undefined">
            <el-input v-model="form.dsUserName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="数据源密码" :prop="['jdbc', 'kafka', 'filesystem', 'starrocks'].includes(dt_dsConnector[form.dsType]) ? 'dsPassWord' : undefined">
            <el-input v-model="form.dsPassWord" show-password placeholder="请输入" ref="pwdInput" v-if="type === 'add' || inputPwd === true"></el-input>
            <span v-else>
              <el-button type="text" @click="handleUseNewPwd">输入(新)密码</el-button>
            </span>
          </el-form-item>
        </template>
        <template v-if="form.dsType === 'MySqlTenant'">
          <el-form-item v-if="form.dsType === 'MySqlTenant'" label="租户选择SQL" prop="tenantSelectSql">
            <el-input v-model="form.tenantSelectSql" rows="3" type="textarea" resize="none" placeholder="请输入" @input="change($event)" style="width:370px;"></el-input>
          </el-form-item>
          <el-form-item label="默认租户" prop="defaultTenant">
            <el-input v-model="form.defaultTenant" placeholder="请输入" @input="change($event)" style="width:370px;"></el-input>
            <!-- <el-select v-model="form.defaultTenant" placeholder="请选择" @focus="handleTenant" @input="change($event)">
              <el-option v-for="item in tenantOptions" :key="item" :value="item" :label="item"></el-option>
            </el-select> -->
          </el-form-item>
        </template>
        <template v-if="['mongo'].includes(dt_dsConnector[form.dsType])">
          <el-form-item label="数据库名称" prop="database">
            <el-input v-model="form.database" placeholder="请输入" class="custom-input"></el-input>
          </el-form-item>
        </template>
        <template v-if="['http_write'].includes(dt_dsConnector[form.dsType])">
          <el-form-item label="授权应用" size="medium">
            <el-select style="width:370px" filterable v-model="form.authedApps" multiple :appLoading="appLoading">
              <el-option v-for="item in appOptions" :key="item.appKey" :label="item.appLabel" :value="item.appKey">
              </el-option>
            </el-select>
          </el-form-item>
        </template>
        <template v-if="form.dsType === 'COS'">
          <el-form-item label="secret id" prop="secretId">
            <el-input v-model="form.secretId" placeholder="请输入" class="custom-input"></el-input>
          </el-form-item>
          <el-form-item label="secret key" prop="secretKey">
            <el-input v-model="form.secretKey" placeholder="请输入" class="custom-input" ref="pwdInput" v-if="type === 'add' || inputPwd === true"></el-input>
            <span v-else>
              <el-button type="text" @click="handleUseNewPwd">输入(新)secret key</el-button>
            </span>
          </el-form-item>
          <el-form-item label="region" prop="region">
            <el-input v-model="form.region" placeholder="请输入" class="custom-input"></el-input>
          </el-form-item>
          <el-form-item label="bucket" prop="bucket">
            <el-input v-model="form.bucket" placeholder="请输入" class="custom-input"></el-input>
          </el-form-item>
        </template>
        <template v-if="form.dsType === 'HTTP'">
          <el-form-item label="请求类型" prop="method">
            <el-select v-model="form.method" @change="changeMethod">
              <el-option label="GET" value="GET"></el-option>
              <el-option label="POST" value="POST"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="请求地址" prop="dsUrl">
            <el-input v-model="form.dsUrl" :placeholder="urlPlaceholder"></el-input>
          </el-form-item>
          <el-form-item label="请求header" prop="requestHeader">
            <template #label>
              <el-tooltip class="item" effect="dark" :content="tips" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
              请求header
            </template>
            <div class="header-wrapper">
              <div class="wrapper" v-for="(item, index) in form.requestHeader" :key="index">
                <div class="input-wrapper">
                  <el-input v-model="item.key" clearable placeholder="key"></el-input>
                  <el-input v-model="item.value" clearable placeholder="vlaue"></el-input>
                </div>
                <div class="btn-wrapper">
                  <el-button v-if="index === 0" type="text" icon="el-icon-circle-plus-outline" @click="addRow('requestHeader')"></el-button>
                  <el-button v-else class="remove-btn" type="text" icon="el-icon-remove-outline" @click="removeRow('requestHeader', index)"></el-button>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="请求参数" prop="requestParams" v-if="form.method === 'GET'">
            <template #label>
              <el-tooltip class="item" effect="dark" :content="tips" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
              请求参数
            </template>
            <div class="params-wrapper">
              <div class="wrapper" v-for="(item, index) in form.requestParams" :key="index">
                <div class="input-wrapper">
                  <el-input v-model="item.key" clearable placeholder="key"></el-input>
                  <el-input v-model="item.value" clearable placeholder="vlaue"></el-input>
                </div>
                <div class="btn-wrapper">
                  <el-button v-if="index === 0" type="text" icon="el-icon-circle-plus-outline" @click="addRow('requestParams')"></el-button>
                  <el-button v-else class="remove-btn" type="text" icon="el-icon-remove-outline" @click="removeRow('requestParams', index)"></el-button>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="请求body" prop="body">
            <el-input v-model="form.body" rows="3" type="textarea" resize="none" placeholder="{}" style="width:370px;"></el-input>
          </el-form-item>
          <el-form-item label="预置脚本">
            <div class="codemirror-wrapper" v-if="type === 'add' || inputPwd === true">
              <codemirror ref="cmEditor" :value="form.dsPassWord" :options="{readOnly: false, mode: 'text/javascript'}" @input="handleInstructionChange" @ready="handleCmReady" />
            </div>
            <span v-else>
              <el-button type="text" @click="handleUseNewPwd">输出</el-button>
            </span>
          </el-form-item>
          <el-form-item label="是否循环查询">
            <el-switch v-model="form.doCycleQuery"></el-switch>
            <span style="margin-left: 100px;">注：当返回数据列表为空时结束循环</span>
          </el-form-item>
          <el-form-item label="返回成功标识字段">
            <el-input v-model="form.successSign" placeholder="eg:success，如果有多层，用'.'隔开"></el-input>
          </el-form-item>
          <el-form-item label="返回数据标识字段" prop="dataSign">
            <el-input v-model="form.dataSign" placeholder="eg:data，如果有多层，用'.'隔开"></el-input>
          </el-form-item>
        </template>
        <template v-if="form.dsType === 'starrocks'">
          <el-form-item label="HTTP端口" prop="httpPort">
            <el-input v-model="form.httpPort" placeholder="请输入"></el-input>
          </el-form-item>
        </template>
        <el-form-item label="数据源描述" prop="dsDesc">
          <el-input v-model="form.dsDesc" rows="3" type="textarea" resize="none" placeholder="请输入" style="width:370px;"></el-input>
        </el-form-item>
        <template v-if="['MySQL', 'Oracle', 'SQLServer', 'PostgreSQL', 'clickhouse', 'ApacheIgnite', 'MongoDB'].includes(form.dsType)">
          <el-form-item label="代理方式" prop="agentType">
            <el-radio-group v-model="form.agentType">
              <el-radio label="NO_AGENT">无</el-radio>
              <el-radio label="IDC">IDC</el-radio>
              <el-radio label="PUB_NET">公网</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" type="danger" @click="handleDelete" v-if="type === 'update'" :disabled="disabled" :loading="btnLoading">删除</el-button>
        <el-button size="medium" type="primary" @click="handleSubmit" :disabled="disabled" :loading="btnLoading">提交</el-button>
        <el-button size="medium" @click="handleTestConnect" :loading="btnLoading">测试连接</el-button>
        <el-button @click="visible = false" size="medium">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { DataType } from 'sdc-core'
import { addDataSource, testDataSource, editDataSource, deleteDataSource, queryDataSourceRefer, getSchemaList, queryApps } from 'services/common.service'
import { mapState } from 'vuex'

export default {
  name: 'bids-datasource-dialog',
  props: {
    type: {
      type: String // update| add
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data () {
    let checkDsNameRepeat = (rule, value, callback) => {
      if (this.form.dsNameList.includes(value)) {
        callback(new Error('名称已存在'))
      }
      callback()
    }
    let checkUrl = (rule, value, callback) => {
      const reg = this.dsTypeUrlMap[this.form.dsType]
      if (reg && !value.match(reg)) {
        callback(new Error('格式不正确'))
      }
      callback()
    }
    let checkPwd = (rule, value, callback) => {
      if ((this.type === 'add' || this.inputPwd) && !value) {
        callback(new Error('请输入数据源密码'))
      }
      callback()
    }
    let checkSecretKey = (rule, value, callback) => {
      if ((this.type === 'add' || this.inputPwd) && !value) {
        callback(new Error('请输入secret key'))
      }
      callback()
    }
    let checkTenantSelectSql = (rule, value, callback) => {
      setTimeout(() => {
        if (this.type === 'add') {
          let regH = '{[^]+}'
          let regHg = new RegExp(regH)
          if (regHg.test(this.form.tenantSelectSql)) {
            callback(new Error('请填写正确的租户sql'))
          } else {
            callback()
          }
        }
        callback()
      }, 200)
    }
    return {
      dsAppOptions: [],
      form: {},
      disabled: true,
      loading: false,
      btnLoading: false,
      referData: [],
      title: this.type === 'add' ? '新增数据源' : '编辑数据源',
      visible: false,
      inputPwd: false,
      clusterOptions: [
        { value: 'DW', label: 'DW集群' }
      ],
      authOptions: [
        { value: 'NO_SASL', label: '无认证' },
        { value: 'SASL_PLAINTEXT', label: 'SASL_PLAINTEXT' },
        { value: 'SASL_SSL', label: 'SASL_SSL', disabled: true }
      ],
      tenantOptions: [],
      schemaOptions: [],
      appOptions: [],
      appLoading: false,
      schemaLoading: false,
      rules: {
        dsAppKey: [{ required: true, message: '请选择所属应用', trigger: 'change' }],
        dsType: [{ required: true, message: '请选择数据库类型', trigger: 'change' }],
        dsName: [
          { required: true, message: '请输入数据源名称', trigger: 'blur' },
          { validator: checkDsNameRepeat }
        ],
        dsCluster: [{ required: true, message: '请选择数据集群', trigger: 'change' }],
        dsUrl: [
          { required: true, message: '请输入地址', trigger: 'blur' },
          { validator: checkUrl }
        ],
        httpPort: [{ required: true, message: '请输入HTTP端口', trigger: 'blur' }],
        dsAuth: [{ required: true, message: '请选择认证方式', trigger: 'change' }],
        dsUserName: [{ required: true, message: '请输入数据源账号', trigger: 'blur' }],
        dsPassWord: [
          // { require: true, message: '请输入数据源密码', trigger: 'blur' },
          { validator: checkPwd, trigger: 'blur' }
        ],
        dsDesc: [{ required: true, message: '请输入数据源描述', trigger: 'blur' }],
        database: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
        tenantSelectSql: [
          { required: true, message: '请输入租户默认sql', trigger: 'blur' },
          { validator: checkTenantSelectSql, trigger: 'blur' }
        ],
        agentType: [{ required: true, message: '请选择代理方式', trigger: 'change' }],
        secretKey: [
          // { required: true, message: '请输入secret key', trigger: 'blur' },
          { validator: checkSecretKey, trigger: 'blur' }
        ],
        secretId: [{ required: true, message: '请输入secret id', trigger: 'blur' }],
        region: [{ required: true, message: '请输入region', trigger: 'blur' }],
        bucket: [{ required: true, message: '请输入bucket', trigger: 'blur' }],
        method: [{ required: true, message: '请选择请求类型', trigger: 'change' }],
        dataSign: [{ required: true, message: '请输入返回数据字段', trigger: 'blur' }]
      },
      dsTypeUrlMap: {
        MySQL: /^jdbc:mysql:\/\//,
        PostgreSQL: /^jdbc:postgresql:\/\//,
        SQLServer: /^jdbc:sqlserver:\/\//,
        Derby: /^jdbc:derby:\/\//,
        Hive: /^jdbc:hive2:\/\//,
        ES6: /^(http|https):\/\//,
        ES7: /^(http|https):\/\//,
        ESjdbc: /jdbc:(es|elasticsearch):\/\//,
        Kafka: undefined,
        MongoDB: /^mongodb:\/\//,
        Oracle: /^jdbc:oracle:/,
        clickhouse: /^jdbc:clickhouse:\/\//,
        ApacheIgnite: /^jdbc:ignite:thin:\/\//,
        HTTP: /^(http|https):\/\//,
        starrocks: /^jdbc:mysql:\/\//
      },
      dsTypePlaceHolderMap: {
        MySQL: 'jdbc:mysql://',
        PostgreSQL: 'jdbc:postgresql://',
        SQLServer: 'jdbc:sqlserver://',
        Derby: 'jdbc:derby://',
        Hive: 'jdbc:hive2://',
        ES6: 'http(s)://',
        ES7: 'http(s)://',
        ESjdbc: 'jdbc:elasticsearch:// 或 jdbc:es://',
        Kafka: undefined,
        MongoDB: 'mongodb://',
        Oracle: 'jdbc:oracle://',
        clickhouse: 'jdbc:clickhouse://',
        ApacheIgnite: 'jdbc:ignite:thin://',
        HTTP: 'http(s)://',
        starrocks: 'jdbc:mysql://'
      },
      dsPropertiesKey: {
        jdbc: ['dsUrl', 'dsUserName', 'dsPassWord', 'dsPasswordKey', 'agentType'],
        'elasticsearch-6': ['dsUrl', 'dsUserName', 'dsPassWord', 'dsPasswordKey'],
        'elasticsearch-7': ['dsUrl', 'dsUserName', 'dsPassWord', 'dsPasswordKey'],
        filesystem: ['dsCluster', 'dsUserName', 'dsPassWord', 'dsPasswordKey'],
        kafka: ['dsUrl', 'dsAuth', 'dsUserName', 'dsPassWord', 'dsPasswordKey'],
        mongo: ['dsUrl', 'database'],
        'http_write': ['authedApps'],
        MySqlTenant: ['dsUrl', 'dsUserName', 'dsPassWord', 'dsPasswordKey', 'tenantSelectSql', 'defaultTenant'],
        COS: ['secretKey', 'secretId', 'region', 'bucket', 'dsPasswordKey'],
        HTTP: ['dsUrl', 'method', 'body', 'dsPassWord', 'dsPasswordKey', 'requestParams', 'requestHeader', 'doCycleQuery', 'successSign', 'dataSign'],
        starrocks: ['dsUrl', 'dsUserName', 'dsPassWord', 'dsPasswordKey', 'httpPort']
      },
      parameterData: [],
      childNodePara: [],
      dsUrlChanged: false, // [update]态, 监听dsUrl是否改变
      tips: '提示：脚本参数用{{var}}，参考postman使用方法'
    }
  },
  watch: {
    'form.dsUrl': {
      handler (newVal, oldVal) {
        if (this.type === 'add') return
        if (this.dt_dsConnector[this.form.dsType] !== 'mongo') return
        if (newVal !== oldVal) {
          this.dsUrlChanged = true
        }
      }
    },
    visible: {
      handler (val) {
        if (val) {
          this.dsUrlChanged = false
        }
      }
    }
  },
  computed: {
    ...mapState(['dt_dsConnector', 'userInfo', 'dt_db_link_para']),
    urlPlaceholder () {
      return this.dsTypePlaceHolderMap[this.form.dsType] || ''
    }
  },
  methods: {
    getDsAppOptions () {
      queryApps().then(res => {
        this.dsAppOptions = res.map(item => ({
          dsAppKey: item.name,
          dsAppName: item.label
        }))
      }).catch(res => {
        this.dsAppOptions = []
        this.$message.error('获取应用下拉列表失败！原因：' + res.message || '暂无')
      })
    },
    change () {
      this.$forceUpdate()
    },
    // 优化参数
    parameter (val) {
      // const arr1 = Object.values(val)
      // const arr2 = Object.keys(val).map((item, index) => {
      //   return {
      //     key: item,
      //     value: JSON.stringify(arr1[index]).replace(/{|}/g, '').replace(/:/g, '=').replace(/,/g, '&').replace(/"/g, '')
      //   }
      // })
      // this.parameterData = arr2
      // for (let index = 0; index < arr1.length; index++) {
      //   let childNode = arr1[index]
      //   let childVal = []
      //   for (let i in childNode) {
      //     let o = ''
      //     o += `${i}=${childNode[i]}`
      //     childVal.push(o)
      //   }
      //   this.childNodePara.push(...childVal)
      // }
      let obj = {}
      for (const key in val) {
        if (key) {
          let childNode = val[key]
          let childVal = []
          for (let i in childNode) {
            let o = ''
            o += `${i}=${childNode[i]}`
            childVal.push(o)
          }
          obj = {
            key: key,
            value: childVal.join('&')
          }
          this.parameterData.push(obj)
          // this.childNodePara.push(...childVal)
        }
      }
    },
    dsUrlPara () {
      if (!this.form.dsUrl) {
        return this.$message.error('请填写完整再确定优化参数')
      }
      // dsType类型不同筛选数据
      if (['PostgreSQL', 'MySQL'].includes(this.form.dsType)) {
        this.childNodePara = []
        let childNodePara = this.parameterData.find(v => v.key === this.form.dsType).value
        childNodePara = childNodePara.split('&')
        this.childNodePara.push(...childNodePara)
      } else {
        this.childNodePara = []
        return
      }
      if (this.form.dsUrl.split('?')[1]) {
        let urlPara = this.form.dsUrl.split('?')[1].split('&')
        let objPara = {}
        // 合并去重，用户填写优先
        urlPara = this.childNodePara.concat(urlPara)
        for (let index = 0; index < urlPara.length; index++) {
          const element = urlPara[index]
          objPara[element.split('=')[0]] = element.split('=')[1] || ''
        }
        let allPara = []
        for (let i in objPara) {
          let o = ''
          o += `${i}=${objPara[i]}`
          allPara.push(o)
        }
        let halfUrl = this.form.dsUrl.split('?')[0]
        this.form.dsUrl = halfUrl + `?${allPara.join('&')}`
      } else {
        // let joint = this.parameterData.map(v => v.value).join('&')
        let joint = this.childNodePara.join('&')
        if (this.form.dsUrl.substr(this.form.dsUrl.length - 1, 1) === '?') {
          this.form.dsUrl += `${joint}`
        } else {
          this.form.dsUrl += `?${joint}`
        }
      }
    },
    show (data = {}) {
      this.getDsAppOptions()
      this.inputPwd = false
      this.form = data
      this.parameterData = []
      this.parameter(this.dt_db_link_para)
      // if (data && data.tenantSelectSql) {
      //   this.handleTenant()
      // }
      if (this.type === 'update') {
        this.disabled = true
        this.fetchData()
      } else {
        this.disabled = false
      }
      this.visible = true
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
        if (this.$refs.pwdInput) {
          this.$refs.pwdInput._data.passwordVisible = false
        }
      })
    },
    fetchData () {
      const params = { dsId: this.form.dsId }
      queryDataSourceRefer(params).then(res => {
        this.referData = res
        this.disabled = false
      }).catch(res => {
        this.referData = []
        this.$message.error('获取引用详情失败，暂无法编辑！原因：' + res.message || '暂无')
      })
      this.getRemoteppOptions()
      // params = { dsPasswordKey: this.form.dsPasswordKey }
      // params.dsPasswordKey && queryDsPassWord(params).then(res => {
      //   if (res) {
      //     this.$set(this.form, 'dsPassWord', res.dsPassWord)
      //   }
      // }).catch(res => {})
    },
    handleClose (done) {
      this.$emit('close')
      done()
    },
    // 租户选择数据
    // handleTenant() {
    //   if (!(this.form.dsUrl && this.form.dsUserName && this.form.tenantSelectSql && this.form.dsType === 'MySqlTenant')) {
    //     return
    //   }
    //   const { dsUrl, dsUserName, dsPassWord, tenantSelectSql, dsPasswordKey } = this.form
    //   const pasme = { dsUrl, dsPassWord, dsUserName, tenantSelectSql, dsPasswordKey }
    //   queryTenantLst(pasme).then(res => {
    //     this.tenantOptions = res
    //     if (this.type === 'add') {
    //       this.form.defaultTenant = this.tenantOptions[0]
    //     }
    //   }).catch((err) => {
    //     return this.$message.error('获取租户选择数据失败！原因：' + err.message || '暂无')
    //   })
    // },
    handleTypeChange (val) {
      const { dsNameList, dsAppName, dsAppKey, dsType } = this.form
      this.form = { dsNameList, dsAppName, dsAppKey, dsType }
      if (['MySQL', 'Oracle', 'SQLServer', 'PostgreSQL', 'clickhouse', 'ApacheIgnite', 'MongoDB'].includes(dsType)) {
        this.$set(this.form, 'agentType', 'NO_AGENT')
      } else {
        delete this.form.agentType
      }
      if (val === 'MySqlTenant') {
        this.form.tenantSelectSql = 'select {urlColumn} as dbUrl, {passwordColumn} as dbPassword, {userColumn} as dbUser, {corpKeyColumn} as dbCorpKey from {tenant_info} where {corpKeyColumn}=:corpkey'
      }
      if (val === 'FrontRepo') {
        this.schemaLoading = true
        getSchemaList().then(res => {
          this.schemaLoading = false
          this.schemaOptions = res.filter(item => item.appKey === this.form.dsAppKey || item.appName === this.form.dsAppName)
        }).catch(() => {
          this.schemaLoading = false
        })
        if (this.form.dsName) {
          this.form.dsName = ''
        } else if (this.form.dsName === undefined) {
          this.$set(this.form, 'dsName', '')
        }
      } else if (val.startsWith('http_write')) {
        this.getRemoteppOptions()
      } else if (val === 'HTTP') {
        this.$set(this.form, 'requestParams', [{ key: '', value: '' }])
        this.$set(this.form, 'requestHeader', [{ key: '', value: '' }])
      } else if (val === 'starrocks') {
        this.$set(this.form, 'httpPort', '8030')
      }
      this.$refs.form.clearValidate()
    },
    getRemoteppOptions () {
      this.appLoading = true
      queryApps({ type: 'all' }).then(res => {
        this.appLoading = false
        this.appOptions = res.map(item => ({
          appKey: item.name,
          appName: item.label,
          appLabel: item.name + '（' + item.label + '）'
        }))
      }).catch(res => {
        this.appOptions = []
        this.appLoading = false
        this.$message.error('获取应用下拉列表失败！原因：' + res.message || '暂无')
      })
    },
    handleDsNameChange (val) {
      if (this.form.dsType === 'FrontRepo') {
        const option = this.schemaOptions.find(item => item.schemaName === val)
        if (option) {
          const user = option.schemaInfo.users.find(item => item.name.endsWith(this.userInfo.engName)) || option.schemaInfo.users[0]
          this.form.dsUserName = user.name
          this.form.dsUrl = user.url
          this.form.dsPasswordKey = user.passwordKey
        }
      }
    },
    handleUseNewPwd () {
      this.inputPwd = true
    },
    handleDelete () {
      if (this.referData.length > 0) {
        this.$message.error(`该数据源已被${this.referData.length}个管道使用，禁止删除！`)
        return
      }
      const params = { dsId: this.form.dsId }
      this.btnLoading = true
      deleteDataSource(params).then(res => {
        this.$message.success('删除成功！')
        this.btnLoading = false
        const { dsNameList, ...option } = this.form
        this.$emit('delete', option)
        this.visible = false
      }).catch(res => {
        this.btnLoading = false
        this.$message.error('删除失败！原因：' + res.message || '暂无')
      })
    },
    handleTestConnect () {
      let valid = true
      this.$refs.form.validateField(['dsType', 'dsUrl', 'dsUserName', 'dsPassWord', 'dsCluster', 'database', 'agentType', 'secretKey', 'secretId', 'region', 'bucket', 'method', 'httpPort'], (message) => {
        if (message) {
          valid = false
        }
      })
      if (!valid) return
      const { index, dsNameList, dsId, dsAppKey, dsAppName, dsType, dsName, dsDesc, createTime, creator, id, modifyTime, ...others } = this.form
      // 删除不属于该connector的字段
      let postData = { dsType }
      if (dsType === 'MySqlTenant') {
        this.dsPropertiesKey[dsType].forEach(item => {
          postData[item] = others[item]
        })
      } else {
        this.dsPropertiesKey[this.dt_dsConnector[dsType]].forEach(item => {
          postData[item] = others[item]
        })
        if (this.type === 'update' && dsType === 'MongoDB' && !this.dsUrlChanged) {
          postData['dsId'] = dsId
        }
      }
      if (dsType === 'COS') {
        const { dsType, secretId, secretKey, bucket, region, dsPasswordKey } = postData
        postData = {
          dsType,
          dsPassWord: secretKey,
          dsUserName: secretId,
          dsUrl: `${bucket}|${region}`
        }
        // 编辑数据源时传dsPasswordKey
        if (this.type === 'update') {
          postData.dsPasswordKey = dsPasswordKey
        }
      }
      if (dsType === 'HTTP') {
        const { requestHeader, requestParams } = postData
        postData.requestHeader = this.getStringifyData(requestHeader)
        postData.requestParams = this.getStringifyData(requestParams)
        delete postData.dataSign
        delete postData.successSign
      }
      this.btnLoading = true
      testDataSource(postData).then(res => {
        this.$message.success('连接成功！')
        this.btnLoading = false
      }).catch(res => {
        this.$message.error('连接失败！原因：' + res.message || '暂无')
        this.btnLoading = false
      })
    },
    handleSubmit () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            this.submitAdd()
          } else {
            if (this.referData.length > 0) {
              this.$confirm(`该数据源已被${this.referData.length}个管道使用，请确认修改后它们不受影响！`, '提示', {
                confirmButtonText: '确认', cancelButtonText: '取消', type: 'warning'
              }).then(() => {
                this.submitEdit()
              }).catch(() => { })
            } else {
              this.submitEdit()
            }
          }
        } else {
          return false
        }
      })
    },
    submitAdd () {
      const { index, dsNameList, dsAppKey, dsAppName, dsType, dsName, dsDesc, agentType, ...others } = this.form
      // 删除不属于该connector的字段
      let properties = {}
      if (dsType === 'MySqlTenant') {
        this.dsPropertiesKey[dsType].forEach(item => {
          properties[item] = others[item]
        })
      } else {
        this.dsPropertiesKey[this.dt_dsConnector[dsType]].forEach(item => {
          properties[item] = others[item]
        })
      }
      if (dsType === 'COS') {
        const { secretId, secretKey, bucket, region } = properties
        properties = {
          dsUserName: secretId,
          dsPassWord: secretKey,
          dsUrl: `${bucket}|${region}`
        }
      }
      if (dsType === 'HTTP') {
        const { requestHeader, requestParams, doCycleQuery } = properties
        if (doCycleQuery === undefined) {
          properties.doCycleQuery = false
        }
        properties.requestHeader = this.getStringifyData(requestHeader)
        properties.requestParams = this.getStringifyData(requestParams)
      }
      const postData = [{ dsAppKey, dsAppName, dsType, dsName, dsDesc, agentType, dsProperties: properties }]
      this.btnLoading = true
      addDataSource(postData).then(res => {
        this.$message.success('添加成功！')
        this.btnLoading = false
        const option = { dsId: res[0].dsId, dsAppKey, dsAppName, dsType, dsName, dsDesc, agentType, dsProperties: properties }
        this.$emit('submit', option)
        this.visible = false
      }).catch(res => {
        this.btnLoading = false
        this.$message.error('添加失败！原因：' + res.message || '暂无')
      })
    },
    submitEdit () {
      const { index, dsNameList, dsId, dsAppKey, dsAppName, dsType, dsName, dsDesc, createTime, creator, id, modifyTime, agentType, ...others } = this.form
      // 删除不属于该connector(数据库类型)的字段
      let properties = {}
      // this.dsPropertiesKey[this.dt_dsConnector[dsType]].forEach(item => {
      //   if (item !== 'dsPassWord' || (this.inputPwd && others[item])) { // 去除空字符串密码
      //     properties[item] = others[item]
      //   }
      // })
      if (dsType === 'MySqlTenant') {
        this.dsPropertiesKey[dsType].forEach(item => {
          properties[item] = others[item]
        })
      } else {
        this.dsPropertiesKey[this.dt_dsConnector[dsType]].forEach(item => {
          properties[item] = others[item]
        })
      }
      if (dsType === 'COS') {
        const { secretId, secretKey, bucket, region, dsPasswordKey } = properties
        properties = {
          dsUserName: secretId,
          dsPassWord: secretKey,
          dsUrl: `${bucket}|${region}`,
          dsPasswordKey
        }
      }
      if (dsType === 'HTTP') {
        const { requestHeader, requestParams, doCycleQuery } = properties
        if (doCycleQuery === undefined) {
          properties.doCycleQuery = false
        }
        properties.requestHeader = this.getStringifyData(requestHeader)
        properties.requestParams = this.getStringifyData(requestParams)
      }
      const params = { dsId }
      const postData = { dsAppKey, dsAppName, dsType, dsName, dsDesc, modifyTime, agentType, dsProperties: properties }
      this.btnLoading = true
      editDataSource(params, postData).then(res => {
        this.btnLoading = false
        this.$message.success('修改成功！')
        // const { dsNameList, ...option } = this.form
        this.$emit('submit')
        this.visible = false
      }).catch(res => {
        this.btnLoading = false
        this.$message.error('修改失败！原因：' + res.message || '暂无')
      })
    },
    handleDsAuthChange (val) {
      if (val === 'NO_SASL') {
        const { dsUserName, dsPassWord, ...others } = this.form
        this.form = others
      }
    },
    addRow (key) {
      this.form[key].push({ key: '', value: '' })
    },
    removeRow (key, index) {
      this.form[key].splice(index, 1)
    },
    handleInstructionChange (value) {
      this.form.dsPassWord = value
    },
    handleCmReady (cm) {
      cm.on('keypress', () => {
        cm.showHint()
      })
    },
    changeMethod (val) {
      this.$set(this.form, 'body', '')
      this.form.requestParams = [{ key: '', value: '' }]
    },
    getStringifyData (arr) {
      const data = arr.reduce((prev, item) => {
        const { key, value } = item
        if (key && value) {
          prev[key] = value
        }
        return prev
      }, {})
      return DataType.isEmptyObject(data) ? '' : JSON.stringify(data)
    },
    handleAppChange (val) {
      const target = this.dsAppOptions.find(item => item.dsAppKey === val)
      this.form.dsAppName = target.dsAppName
      this.form.dsAppKey = target.dsAppKey
    }
  }
}
</script>
<style lang="less" scoped>
@import "~assets/css/bids-datasource-dialog.less";
</style>

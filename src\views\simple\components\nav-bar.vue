<template>
  <div class="nav-bar" ref="navBar" v-show="value">
    <div class="title-wrap">
      <h4>页面导航</h4>
      <el-button type="text" icon="el-icon-arrow-right" style="color: #606266" @click="$emit('input', !value)"></el-button>
    </div>
    <ul class="nav-wrap">
      <li class="nav-item" v-for="item in navs" :key="item.value" @click="handleGoNav(item.value)">
        <span :class="{ active: item.value === activeNav }">{{ item.label }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      activeNav: 'baseConfig',
      navs: [
        { label: '基础配置', value: 'baseConfig' },
        { label: '同步附加配置', value: 'additionalConfig' },
        { label: '运行配置', value: 'runConfig' },
        { label: '运维配置', value: 'operateConfig' }
      ]
    }
  },
  methods: {
    handleGoNav (value) {
      this.activeNav = value
      this.$emit('goNav', value)
    }
  }
}
</script>

<style lang="less" scoped>
.nav-bar {
  position: fixed;
  top: 300px;
  width: 194px;
  padding: 12px;
  border: 1px solid #CDCDCD;
  border-radius: 4px 0 0 4px;
  background: #FFF;
  z-index: 1001;
  .title-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 12px;
  }
  .nav-wrap {
    padding: 0 16px;
    .nav-item {
      color: #333333;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      height: 22px;
      line-height: 22px;
      margin-bottom: 12px;
      cursor: pointer;
      span {
        padding-left: 11px;
      }
      .active {
        position: relative;
        &::before {
          position: absolute;
          left: 0;
          top: 20%;
          transform: translateY(0, -50%, 0);
          content: " ";
          width: 3px;
          height: 14px;
          background: @color-theme;
        }
      }
    }
  }
}
</style>

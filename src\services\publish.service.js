/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-12-15 12:10:48
 */
import { http } from 'utils/http'
import api from 'config/api.conf'

export const queryAllData = (data) => http.post(api.bidsplus_config.publish.queryAllData, { params: data })
export const queryData = (params) => http.get(
  api.bidsplus_config.publish.queryData.replace('{configId}', params.configId).replace('{version}', params.version || 'null'), { loading: true }
)
export const updateData = (params, data) => http.post(
  api.bidsplus_config.publish.updateData.replace('{configId}', params.configId).replace('{version}', params.version), { params: data, loading: true }
)
export const deleteData = (params) => http.del(api.bidsplus_config.publish.deleteData.replace('{configId}', params.configId), { loading: true })
export const queryBatch = (data) => http.post(api.bidsplus_config.publish.queryBatch, { params: data, loading: true })
export const querySubs = (params, data) => http.post(
  api.bidsplus_config.publish.querySubs.replace('{configId}', params.configId).replace('{version}', params.version), { params: data }
)
export const queryMineData = (data) => http.post(api.bidsplus_config.publish.queryMineData, { params: data })
export const queryInstances = (params, data) => http.post(
  api.bidsplus_config.publish.queryInstances.replace('{configId}', params.configId).replace('{version}', params.version), { params: data }
)
export const queryDrafts = (data) => http.post(api.bidsplus_config.publish.queryDrafts, { params: data, loading: true })
export const saveDrafts = (params, data) => http.post(
  api.bidsplus_config.publish.saveDrafts.replace('{configId}', params.configId || 'null'), { params: data, loading: true }
)
export const updateDrafts = (params, data) => http.post(
  api.bidsplus_config.publish.updateDrafts.replace('{configId}', params.configId).replace('{version}', params.version || 'null'), { params: data, loading: true }
)
export const batchStart = (data) => http.post(api.bidsplus_config.publish.batchStart, { params: data, loading: true })
export const removeDrafts = (data) => http.post(api.bidsplus_config.publish.removeDrafts, { params: data, loading: true })
export const submitDrafts = (data) => http.post(api.bidsplus_config.publish.submitDrafts, { params: data, loading: true })
export const setOnline = (data) => http.post(api.bidsplus_config.publish.setOnline, { params: data, loading: true })
export const setOffline = (data) => http.post(api.bidsplus_config.publish.setOffline, { params: data, loading: true })

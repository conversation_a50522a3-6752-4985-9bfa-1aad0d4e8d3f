<!--
 * @Description: 变量表达式提示项
 * @Autor: kenoxia
 * @LastEditTime: 2021-06-30 15:58:16
-->
<template>
  <div class="var-expression-tip">
    <el-popover
      placement="top-start"
      trigger="click"
      @show="() => icon = 'el-icon-arrow-up'"
      @hide="() => icon = 'el-icon-arrow-down'">
      <el-table
        :data="expressionList"
        border
        :span-method="spanMethod">
        <el-table-column
          prop="formItem"
          label="支持的表单项"
          width="200"
          key="formItem">
          <el-divider>数据发布</el-divider>
          <ul>
            <li>数据采集的SQL语句</li>
            <li>主题名称（kafka）</li>
            <li>输出数据的处理规则</li>
          </ul>
          <el-divider>数据订阅</el-divider>
          <ul>
            <li>数据订阅规则</li>
            <li>数据输出路径（filesystem）</li>
            <li>数据输出表名（jdbc）</li>
            <li>数据输出集合（mongo）</li>
            <li>文档索引名称（es）</li>
            <li>主题名称（kafka）</li>
            <li>前置后置事件</li>
          </ul>
        </el-table-column>
        <el-table-column v-for="item in expressionColumn" :key="item.prop" :prop="item.prop" :label="item.label" align="center" :width="item.width"/>
      </el-table>
      <div slot="reference" class="tip-text">变量表达式<i style="margin-left:5px" :class="icon"/></div>
    </el-popover>
  </div>
</template>
<script>
export default {
  name: 'var-expression-tip',
  data() {
    return {
      icon: 'el-icon-arrow-down',
      expressionColumn: [
        { prop: 'expression', label: '可用的表达式', width: '360' }, 
        { prop: 'explain', label: '说明', width: '260' }
      ],
      expressionList: [
        { expression: '{constant#BIDS_BATCH_NUMBER}', explain: '当前批次号' },
        { expression: '{constant#JOB_NAME}', explain: '当前任务名' },
        { expression: '{constant#INTERFACE_CODE}', explain: '该数据发布/订阅编码' },
        { expression: '{constant#INTERFACE_NAME}', explain: '该数据发布/订阅名称' },
        { expression: '{constant#INTERFACE_DESC}', explain: '该数据发布/订阅描述' },
        { expression: '{constant#CORPKEY}', explain: '当前租户' },
        { expression: '{curTimeStamp#yyyy-MM-dd HH:mm:ss}', explain: '对应格式的当前时间，格式可自定义' },
        { expression: '{cycleTimeStamp#yyyy-MM-dd HH:mm:ss}', explain: '对应格式的周期时间' },
        { expression: '{curTime(offsetSeconds)#yyyy-MM-dd HH:mm:ss}', explain: '当前时间的前/后|offsetSeconds|秒，即offsetSeconds为负数表示过去时间' },
        { expression: '{cycleTime(offsetSeconds)#yyyy-MM-dd HH:mm:ss}', explain: '周期时间的前/后|offsetSeconds|秒' }
      ]
    }
  },
  methods: {
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return { rowspan: 10, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
  .var-expression-tip {
    font-family: @font-family-yahei;
    color: @color-text-skyblue;
    .tip-text {
      cursor: pointer;
    }
  }
</style>

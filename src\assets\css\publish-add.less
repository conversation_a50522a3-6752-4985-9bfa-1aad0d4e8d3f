.publish-add {
  padding: 0 @page-padding-horizontal;
  max-width: @page-max-width;
  .page-header {
    display: flex;
    justify-content: space-between;
    height: @page-header;
    align-items: center;
    .header-left {
      .tips {
        font-size: @font-14;
        color: @color-text-gray;
      }
    }
  }
  .page-main {
    padding-bottom: @page-main-padding-vertical;
    min-height: 200px;
  }
}

.commit-confirm-dialog {
  .el-dialog__body {
    padding: 20px 40px;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
  label {
    color: #99a9bf;
  }
}

.show-data-dialog {
  .pagination {
    margin-top: 20px;
  }
  .el-dialog__body {
    padding: 20px 40px;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
}

.show-log-dialog {
  .label {
    line-height: 30px;
  }
  .toolbar {
    float: right;
    height: 36px;
    margin-bottom: 10px;
    margin-right: 10px;
  }
  .el-dialog__body {
    padding: 20px 20px;
  }
}
.el-tag {
  .el-loading-spinner {
    margin-top: -10px !important;
  }
}

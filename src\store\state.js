export default {
  userInfo: {
    staffId: '',
    engName: '',
    avatar: '',
    admin: false
  },
  dt_collectType: new Map([
    [1, '全量'], [2, '增量']
  ]),
  dt_jobStatus: new Map([
    [0, '草稿'], [1, '生效中'], [-1, '已下线']
  ]),
  filter_dt_jobStatus: new Map([
    [1, '生效中'], [-1, '已下线']
  ]),
  dt_scheduleUnit: new Map([
    ['Min', '分钟'], ['Hour', '小时'], ['Day', '天'], ['Week', '周'], ['Month', '月']
  ]),
  dt_jobState: {
    ACCEPTED: '已提交', RUNNING: '运行中', EVENT_BEFORE: '运行中', SUCCEEDED: '完成', EVENT_AFTER: '完成', FAILED: '失败', KILLED: '已停止', NOJOB: '无任务'
  },
  dt_scheduleState: {
    0: '已提交', 1: '运行中', 3: '完成', 2: '失败'
  },
  dt_executorState: {
    RUNNING: '运行中', FAILED: '异常', ACCEPTED: '等待中', SUCCEEDED: '已完成', KILLED: '已停止', NOJOB: '未启动'
  },
  dt_dsConnector: {
    MySQL: 'jdbc', 
    SQLServer: 'jdbc', 
    PostgreSQL: 'jdbc', 
    Derby: 'jdbc', 
    Hive: 'jdbc', 
    ESjdbc: 'jdbc', 
    Oracle: 'jdbc', 
    FrontRepo: 'jdbc', 
    ES6: 'elasticsearch-6', 
    ES7: 'elasticsearch-7', 
    HDFS: 'filesystem', 
    Kafka: 'kafka', 
    MongoDB: 'mongo',
    'http_write_json': 'http_write',
    'http_write_file': 'http_write',
    MySqlTenant: 'jdbc',
    clickhouse: 'jdbc',
    COS: 'COS',
    ApacheIgnite: 'jdbc',
    HTTP: 'HTTP',
    starrocks: 'starrocks'
  },
  dt_storageStrategy: {
    NOT_SAVE: '直连源库', SAVE: '暂存平台'
  },
  dt_system: {
    maxCores: 1,
    maxMemoryMB: 10240,
    maxParallelism: 10,
    minPollingIntervalSeconds: 5,
    // maxPollingIntervalSeconds: 60,
    maxPartitionNum: 10,
    minPollingOffset: 0
  },
  dataToBeSub: [], // 待订阅的数据，用于构建数据订阅
  currentPublishConfig: {},
  currentSubscribeConfig: {},
  defaultPublishConfig: {
    cache: {},
    interfaceDesc: '',
    jobType: 'FLINK',
    collectType: '',
    appKey: '',
    appName: '',
    interfaceName: '',
    interfaceCode: '',
    job: {
      sources: [{
        dataFormat: 'ROW',
        columns: [],
        properties: {
          // connector: '',
          // query: ''
          dsId: ''
        }
      }],
      transform: {
        functions: [],
        script: ''
      },
      sink: {
        columns: [],
        dataFormat: 'ROW',
        storageStrategy: '',
        enableEncryption: false,
        properties: {}
      }
    },
    owners: [],
    setting: {
      savepoint: {
        isAllowNonRestoredState: true
      },
      checkpoint: {
        type: '',
        interval: 60000
      },
      retryStrategy: {
        attempts: 1,
        delay: 1,
        enabled: false
      },
      checkStrategy: {
        enabled: false,
        minRows: 0,
        maxRows: 1000000
      },
      timeoutStrategy: {
        enabled: false,
        timeoutSeconds: 60
      },
      resources: {
        cores: 1,
        parallelism: 1,
        memoryMB: 1024
      },
      scheduleStrategy: {
        scheduleType: '',
        scheduleBeginTime: '',
        scheduleEndTime: '9999-12-31 12:00:00',
        scheduleUnit: '',
        schedulePeriod: '',
        scheduleAtCron: '',
        ifScheduleUnfinished: ''
      }
    }
  },
  defaultSubscribeConfig: {
    cache: {},
    interfaceDesc: '',
    jobType: 'FLINK',
    collectType: '',
    collectWay: '',
    appKey: '',
    appName: '',
    interfaceName: '',
    interfaceCode: '',
    job: {
      sources: [],
      transform: {
        functions: [],
        script: ''
      },
      sink: {
        columns: [],
        // tableName: '',
        dataFormat: 'ROW',
        overrideStrategy: '',
        enableEncryption: false,
        properties: {} // { dsId } jdbc: { table-name } es: { index, document-type }
      },
      beforeEvent: {
        eventPlugin: '',
        eventDesc: '',
        eventProperties: {}
      },
      afterEvent: {
        eventPlugin: '',
        eventDesc: '',
        eventProperties: {}
      }
    },
    owners: [],
    setting: {
      savepoint: {
        isAllowNonRestoredState: true
      },
      checkpoint: {
        type: '',
        interval: 60000
      },
      retryStrategy: {
        attempts: 1,
        delay: 1,
        enabled: false
      },
      checkStrategy: {
        enabled: false,
        minRows: 0,
        maxRows: 1000000
      },
      timeoutStrategy: {
        enabled: false,
        timeoutSeconds: 60
      },
      resources: {
        cores: 1,
        parallelism: 1,
        memoryMB: 1024
      },
      scheduleStrategy: {
        scheduleType: '',
        scheduleBeginTime: '',
        scheduleEndTime: '9999-12-31 12:00:00',
        scheduleUnit: '',
        schedulePeriod: '',
        scheduleAtCron: '',
        ifScheduleUnfinished: ''
      }
    }
  },
  dt_db_link_para: {
    MySQL: { rewriteBatchedStatements: true, useCursorFetch: true },
    PostgreSQL: { reWriteBatchedInserts: true }
  },
  subWhiteList: []
}

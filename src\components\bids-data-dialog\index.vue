<!--
 * @Description: 查看数据组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-02-05 11:20:20
-->
<template>
  <div class="bids-data-dialog">
    <el-dialog title="查看数据" :visible.sync="visible" :before-close="handleClose" width="800px" top="10vh">
      <el-form :inline="true" :model="initData" label-width="100px" class="custom-el-form">
        <el-form-item :label="label">
          <span>{{initData.jobName}}</span>
        </el-form-item>
        <div class="toolbar" v-if="initData.collectType === 2">
          <span>{{second + ' 秒后更新...'}}</span>
        </div>
      </el-form>
      <el-input
        id="scroll_text"
        type="textarea"
        readonly
        resize="none"
        v-model="dataText"
        rows="25">
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose()" size="medium" >关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { queryJobTableData } from 'services/common.service'

export default {
  name: 'bids-data-dialog',
  props: {
    label: String
  },
  data() {
    return {
      visible: false,
      logType: 'flink',
      dataText: '',
      initData: {},
      fullscreen: false,
      intervalID: '',
      second: 0
    }
  },
  watch: {
    second(val) {
      if (this.initData.collectType === 2 && val === 0) {
        this.fetchData('refresh')
      }
    }
  },
  methods: {
    show(initData) {
      this.initData = initData || {} // { jobName, collectType }
      this.visible = true
      this.fetchData()
      if (initData.collectType === 2) {
        this.intervalID = setInterval(() => {
          this.second > 0 && this.second--
        }, 1000)
      }
    },
    handleClose(done) {
      clearInterval(this.intervalID)
      this.$emit('close')
      done ? done() : this.visible = false
    },
    fetchData(mode) {
      const params = { jobName: this.initData.jobName }
      const postData = { limit: 30, tail: this.initData.collectType === 2 }
      this.dataText = 'Loading...'
      queryJobTableData(params, postData).then(res => {
        this.dataText = '仅显示至多30条数据\n\n' + (res.join('\n\n') || '无数据')
        this.initData.collectType === 2 && (this.second = 5)
        if (mode === 'refresh') {
          this.$nextTick(() => {
            const textarea = document.getElementById('scroll_text')
            textarea.scrollTop = textarea.scrollHeight
          })
        }
      }).catch(res => {
        this.dataText = res.message.toString()
        this.initData.collectType === 2 && (this.second = 5)
      })
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/bids-data-dialog.less";
</style>

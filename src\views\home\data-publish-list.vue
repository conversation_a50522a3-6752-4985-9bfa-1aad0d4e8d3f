<!--
 * @Description: 数据市场上的数据发布列表组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-05-12 11:20:16
-->
<template>
  <fragment>
    <div class="page-header">
      <div class="header-left">
        <div class="filter">
          <el-input placeholder="请搜索关键词" v-model="criteriaValue" size="medium" @input="handleInput">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
            <el-select v-model="criteriaKey" slot="prepend" placeholder="请选择" @change="handleChange">
              <el-option v-for="item in pubFilterOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-input>
        </div>
      </div>
    </div>
    <div class="page-main">
      <div class="table">
        <el-table
          ref="table"
          v-loading="loading"
          key="publishTable"
          :data="tableData"
          height="100%"
          :header-cell-style="{background:'#f5f7f9', height: '60px'}"
          style="width: 100%">
          <el-table-column
            type="index"
            :index="(pageIndex-1)*10+1"
            width="50">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableColumn" :key="index"
            :prop="item.prop"
            :label="item.label"
            :formatter="item.formatter"
            :sortable="item.sortable"
            :min-width="item.minWidth || item.width"
            :show-overflow-tooltip="true"
            align="center">
          </el-table-column>
          <el-table-column label="运行状态" width="80" align="center" v-if="userInfo.admin">
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="left" @show="handlePopoverShow(scope.row)" :open-delay="200">
                <bids-simple-table :data="exeStatusData" :table-column="statusTableColumn" :loading="popoverLoading"></bids-simple-table>
                <span slot="reference" class="tableCellIcon"><i slot="reference" class="el-icon-reading"></i></span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            :width="190">
            <template slot-scope="scope">
              <!-- 管理员和owners可以在数据市场直接操作数据发布 -->
              <template v-if="userInfo.admin || scope.row.isMine">
                <el-button @click="handleManageSetting(scope.row)" style="width:48px;" type="text" size="small">编辑</el-button>
                <el-button @click="handleManageJobs(scope.row)" type="text" size="small">管理实例</el-button>
                <el-button @click="handleManageSub(scope.row)" type="text" size="small">管理订阅</el-button>
              </template>
              <template v-else>
                <el-button @click="handleViewSetting(scope.row)" type="text" size="small">查看配置</el-button>
                <el-button @click="handleViewSub(scope.row)" type="text" size="small">查看订阅</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </div>
  </fragment>
</template>
<script>
import { Fragment } from 'vue-fragment'
import { pagination, filterInput, formatter } from 'mixins'
import { queryAllData } from 'services/publish.service'
import { mapState } from 'vuex'
import { BidsSimpleTable } from 'components'
import { DataType } from 'sdc-core'
import { queryJobShapshot } from 'services/common.service'

export default {
  name: 'data-publish-list',
  mixins: [pagination, filterInput, formatter], // 分页，搜索栏，格式化表格内容
  components: {
    Fragment,
    BidsSimpleTable
  },
  computed: {
    ...mapState(['userInfo']) // 登录用户信息
  },
  data() {
    return {
      loading: false,
      tableData: [], // 数据发布表格数据
      paginationType: 'remote', // 由服务器实现分页查询
      exeStatusData: [], // 运行状态表格数据
      popoverLoading: false, // 运行状态弹出窗加载效果
      tableColumn: [
        { prop: 'appName', label: '所属应用', minWidth: '80' },
        { prop: 'interfaceName', label: '发布名称', minWidth: '160' },
        { prop: 'interfaceCode', label: '发布编码', minWidth: '120' },
        { prop: 'interfaceDesc', label: '发布描述', minWidth: '120' },
        { prop: 'version', label: '当前版本', minWidth: '80' },
        { prop: 'collectType', label: '采集类型', minWidth: '80', formatter: this.formatterCollectType },
        { prop: 'schedulePeriodCron', label: '发布周期', minWidth: '110', sortable: true },
        { prop: 'storageStrategy', label: '落地策略', minWidth: '80', formatter: this.formatterStorageStrategy },
        { prop: 'creatorName', label: '创建人', minWidth: '80' },
        { prop: 'jobStatus', label: '状态', minWidth: '70', formatter: this.formatterJobStatus }
      ],
      statusTableColumn: [
        { prop: 'version', label: '版本', width: '100' },
        { prop: 'jobExecutorStatus', label: '最近任务运行状态', width: '200', formatter: this.formatterJobExeState }
      ]
    } 
  },
  methods: {
    fetchData() {
      // 获取数据发布列表
      const paginator = { pageSize: this.pageSize, pageIndex: this.pageIndex } // 分页查询的参数
      const criteria = [] // 搜索栏的参数
      if (this.criteriaValue && this.criteriaKey) {
        criteria.push({ criteriaKey: this.criteriaKey, criteriaValue: this.criteriaValue })
      }
      const postData = { paginator, criteria }
      this.loading = true
      queryAllData(postData).then(res => {
        res = res || { paginator: {} }
        this.tableData = res.content ? res.content.map(row => ({
          ...row,
          isMine: (row.jobOwners || []).some(item => item.staffId === this.userInfo.staffId), // 该数据发布是否属于登录用户
          schedulePeriodCron: this.formatterScheduleAtCron(row)
        })) : []
        this.recordCount = res.paginator.recordCount || 0
        this.$refs.table.doLayout()
        this.loading = false
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('获取数据发布失败！原因：' + res.message || '暂无')
        this.loading = false
      })
    },
    handleManageSetting(row) {
      this.$router.push({ path: '/publish/edit', query: { configId: row.configId } })
    },
    handleViewSetting(row) {
      this.$router.push({ path: '/publish/view', query: { configId: row.configId } })
    },
    handleManageJobs(row) {
      this.$router.push({ path: '/publish/jobs', query: { configId: row.configId } })
    },
    handleManageSub(row) {
      this.$router.push({ path: '/publish/subs', query: { configId: row.configId } })
    },
    handleViewSub(row) {
      this.$router.push({ path: '/publish/subs', query: { configId: row.configId } })
    },
    handlePopoverShow(row) {
      // 获取运行状态数据
      const params = { configId: row.configId }
      this.exeStatusData = []
      this.popoverLoading = true
      queryJobShapshot(params).then(res => {
        if (DataType.isObject(res)) res = [res]
        this.exeStatusData = res
        this.popoverLoading = false
      }).catch(res => {
        this.popoverLoading = false
        this.$message.error('获取运行状态失败！原因：' + res.message || '暂无')
      })
    }
  }
}
</script>

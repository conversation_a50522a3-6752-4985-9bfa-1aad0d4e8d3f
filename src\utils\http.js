/*
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-12-15 11:48:00
 */
import { DataHttp } from 'sdc-core'
import { app, env } from './index'

export const http = DataHttp.getInstance({
  // 当前应用上下文
  app,
  multi: true,
  ctx: true,
  // 当前环境配置
  env,

  // 当前请求实例(可使用DataFetch构建，也可配置详细参数，参考axios配置)
  axios: DataHttp.create({
    withCredentials: true,
    retry: 0
  }),

  // 数据映射(对返回数据字段映射)
  map: {
    result: 'data',
    message: 'message',
    success: (res) => {
      return res.success && (res.code === 0 || res.code === 200)
    }
  }

  // 当前加载器函数(覆盖默认加载器，loading当前请求是否启动加载器，app应用上下文)
  // spinner(loading, app) {},

  // 数据校验函数(对返回数据的权限校验，返回boolean)
  // check(res) {}

  // 数据处理函数(对返回数据的处理函数, 返回处理结果)
  // handle(res) {}

  // 全局错误处理函数(err是包含title,message的对象)
  // error(err, app) {}
})

export const httpFlink = DataHttp.getInstance({
  app,
  multi: true,
  env,
  axios: DataHttp.create({
    withCredentials: true,
    retry: 0
  }),
  map: { status: 'success', result: 'data' }
})

export const httpScheduler = DataHttp.getInstance({
  app,
  multi: true,
  env,
  axios: DataHttp.create({
    withCredentials: true,
    retry: 0
  }),
  map: { status: 'success', result: 'data' }
})

export const httpFrontRepo = DataHttp.getInstance({
  app,
  multi: true,
  env,
  axios: DataHttp.create({
    withCredentials: true,
    retry: 0
  }),
  map: { status: 'code', result: 'data', message: 'message', value: 0 }
})

export const httpJobservice = DataHttp.getInstance({
  app,
  multi: true,
  env,
  axios: DataHttp.create({
    withCredentials: true,
    retry: 0
  }),
  map: { status: 'success', result: 'data' }
})

{"name": "bidsplus-web", "version": "1.0.0", "title": "数据管道平台", "author": "kenoxia", "description": "基于SDC-WebUI框架的数据管道平台前端项目", "license": "MIT", "repository": {"type": "git", "url": "http://git.code.oa.com/hrplat-middletier/datacenter2/bidsplus-web.git"}, "scripts": {"serve": "vue-cli-service serve --mode loc", "build:dev": "vue-cli-service build --mode dev", "build:uat": "vue-cli-service build --mode uat", "build:prd": "vue-cli-service build --mode prd", "lint": "vue-cli-service lint"}, "dependencies": {"@babel/polyfill": "^7.8.7", "@tencent/sdc-core": "^1.0.7", "@tencent/sdc-theme": "1.0.2", "@tencent/sdc-vue": "^1.0.1", "@tencent/sdc-webui": "^1.0.6-beta.7", "camel-case": "^4.1.2", "clipboard": "^2.0.6", "core-js": "^3.4.3", "crypto-js": "^4.0.0", "echarts": "^5.1.2", "element-ui": "^2.13.0", "generate-schema": "^2.6.0", "resize-detector": "^0.3.0", "sortablejs": "^1.12.0", "vcrontab": "^0.3.5", "vue": "^2.6.10", "vue-codemirror": "^4.0.6", "vue-fragment": "^1.5.1", "vue-router": "^3.1.3", "vuex": "^3.1.2", "vxe-table": "^3.3.10", "whatwg-fetch": "^3.0.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.1.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.3", "babel-plugin-component": "^1.1.1", "eslint": "^5.16.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^5.0.0", "less": "^3.0.4", "less-loader": "^5.0.0", "postcss-import": "^12.0.1", "postcss-px-to-viewport": "^1.1.1", "postcss-url": "^8.0.0", "style-resources-loader": "1.3.3", "svg-sprite-loader": "^4.2.1", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-template-compiler": "^2.6.10"}}
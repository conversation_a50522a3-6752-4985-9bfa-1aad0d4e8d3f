<!--
 * @Description: 数据结构组件，用于定义输入/输出数据的结构
 * @Autor: kenoxia
 * @LastEditTime: 2021-08-09 16:46:11
-->
<template>
  <div class="bids-datastruct-form-part">
    <el-form :model="form" ref="form" size="small" :rules="moderateEditable ? rules : {}" :label-width="labelWidth" :disabled="!moderateEditable">
      <template v-if="type === 'sink'">
        <el-form-item label="是否加密数据" prop="enableEncryption">
          <el-switch v-model="form.enableEncryption" @change="(val) => {updateSinkProperty(val, 'enableEncryption')}" v-if="mildEditable"></el-switch>
          <span v-else>{{form.enableEncryption ? '是' : '否'}}</span>
        </el-form-item>
        <template v-if="form.enableEncryption">
          <!-- 新增密钥token字段 -->
          <el-form-item v-if="configData.appToken && ['RSA', 'AES'].includes(form.encryptionWay)">
            <template #label>
              <span>密钥token</span>
              <el-tooltip effect="dark" content="密钥token和密钥key配合使用可以从kms获取密钥明文" placement="top">
                <i class="el-icon-question"/>
              </el-tooltip>
            </template>
            <div>
              <el-input  v-if="mildEditable" style="width:420px;" :value="configData.appToken"></el-input>
              <span v-else style="display:inline-block;width:420px">{{configData.appToken}}</span>
              <!-- <el-button style="margin-left:20px;" :disabled="false" @click="handleCheckToken">查看</el-button> -->
            </div>
          </el-form-item>
          <el-form-item label="加密算法" prop="encryptionWay">
            <el-select v-model="form.encryptionWay" @change="handleEncryptionWayChange" v-if="mildEditable">
              <el-option v-for="item in encryptionWayOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <span v-else>{{form.encryptionWay}}</span>
            <!-- 调整位置 -->
            <el-button style="margin-left:20px;" @click="handleGenEncryptionKey" :loading="btnLoading" v-if="moderateEditable && ['RSA', 'AES'].includes(form.encryptionWay)">密钥生成</el-button>
            <el-button v-if="moderateEditable && ['AES'].includes(form.encryptionWay) && configData.appToken" @click="chooseEncryptionPublicKey">选择密钥key</el-button>
            <!-- 调整位置 -->
          </el-form-item>
          <el-form-item :label="form.encryptionWay === 'RSA' ? '公钥key' : '密钥key'" prop="encryptionPublicKey" v-if="['RSA', 'AES'].includes(form.encryptionWay)">
            <el-input :value="form.encryptionPublicKey" style="width:420px;" @change="(val) => {updateSinkProperty(val, 'encryptionPublicKey')}" v-if="mildEditable"></el-input>
            <span v-else>{{form.encryptionPublicKey}}</span>
            <!-- <el-button style="margin-left:20px;" @click="handleGenEncryptionKey" :loading="btnLoading" v-if="moderateEditable">密钥生成</el-button> -->
            <!-- <el-button v-if="moderateEditable && form.encryptionWay !== 'RSA'" @click="chooseEncryptionPublicKey">选择密钥key</el-button> -->
          </el-form-item>
          <el-form-item label="私钥key" prop="encryptionPrivateKey" v-if="form.encryptionWay === 'RSA' && mildEditable">
            <el-input v-model="form.encryptionPrivateKey" style="width:420px;" @change="(val) => {updateSinkProperty(val, 'encryptionPrivateKey')}"></el-input>
          </el-form-item>
        </template>
      </template>
      <el-form-item :label="label" prop="dataFormat">
        <el-radio-group v-model="form.dataFormat" @change="handleDataFormatChange" :disabled="true" v-if="mildEditable">
          <el-radio label="JSON" :disabled="configType === 'subscribe' && configData.collectWay === 'PULL'"></el-radio>
          <el-radio label="ROW"></el-radio>
        </el-radio-group>
        <span v-else>{{form.dataFormat}}</span>
        <div class="item-right"  v-if="moderateEditable">
          <transition mode="out-in">
            <span key="row" v-if="form.dataFormat === 'ROW'">
              <el-button size="small" type="text" @click="handleClearTable">清空</el-button>
              <template v-if="type === 'source' && connector === 'jdbc'">
                <el-button size="small" type="primary" @click="handleScanBySQL">解析SQL获取结构</el-button>
                <el-button size="small" @click="$refs.structTable.addRow()" type="primary">新增行</el-button>
              </template>
              <template v-else>
                <el-button size="small" @click="handleScanTable" v-if="type === 'source'">扫描表结构</el-button>
                <!-- <el-button size="small" @click="handleCopyTable" v-else>{{`拷贝${configType === 'publish' ? '输入' : '需订阅'}数据的表结构`}}</el-button> -->
                <el-button size="small" @click="handleParse" v-else>解析规则获取表结构</el-button>
                <el-button size="small" @click="handleUploadTable">上传表结构</el-button>
                <el-button size="small" @click="$refs.structTable.addRow()" type="primary">新增行</el-button>
              </template> 
            </span>
            <span key="json" v-else-if="form.dataFormat === 'JSON'">
              <el-button size="small" type="primary" @click="handleImportJson">导入JSON</el-button>
            </span>
          </transition>
        </div>
      </el-form-item>
    </el-form>
    <el-alert
      title="请手动定义数据类型"
      type="warning"
      v-if="form.dataFormat === 'ROW' && rowData.some(item => !item.type)"
      :description="'序列号：'+rowData.filter(item => !item.type).map(item => item.seq).join('、')">
    </el-alert>
    <div class="config-item" :class="[!structValid ? 'config-item__error' : '']">
      <transition mode="out-in">
        <div class="panel panel-border" key="json" v-if="form.dataFormat === 'JSON'">
          <el-tabs type="border-card" class="custom-tabs" tab-position="left">
            <el-tab-pane label="JSON">
              <bids-json-schema-editor v-model="jsonSchema" disabled-type :all-disabled="!moderateEditable"/>
            </el-tab-pane>
            <el-tab-pane label="RAW" style="padding-top:5px;">
              <pre style="color:#666;line-height:20px;">{{jsonRaw}}</pre>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="panel panel-border" key="row" v-else-if="form.dataFormat === 'ROW'">
          <bids-edit-table ref="structTable" v-model="rowData" :configData="configData" :disabled="!moderateEditable" :custom-save-check="customSaveCheck"
            :table-column="tableColumn" @change="handleTableChange" :type="type" :configType="configType" :options="type === 'source' && connector === 'jdbc' ? ['modify'] : ['drag', 'delete', 'modify']">
          </bids-edit-table>
        </div>
      </transition>
      <div class="error-message" v-if="!structValid && structErrorMsg">{{structErrorMsg}}</div>
    </div>
    <scan-table-dialog ref="scanTableDialog" :init-data="initData" @scan="handleScan"></scan-table-dialog>
    <upload-table-dialog ref="uploadTableDialog" :init-data="initData" @upload="handleUpload"></upload-table-dialog>
    <import-json-dialog ref="importJsonDialog" @confirm="handleJsonConfirm"></import-json-dialog>
    <encryption-key-dialog ref="encryptionKeyDialog" :encryptionWay="form.encryptionWay" @handleChoose="handleChoose"></encryption-key-dialog>
  </div>
</template>

<script>
import ScanTableDialog from './sacn-table-dialog'
import UploadTableDialog from './upload-table-dialog'
import BidsJsonSchemaEditor from 'components/bids-json-schema-editor'
import ImportJsonDialog from './import-json-dialog'
import EncryptionKeyDialog from './encryption-key-dialog.vue'
import BidsEditTable from 'components/bids-edit-table'
import { mapState, mapMutations } from 'vuex'
import { DataUtil, DataType } from 'sdc-core'
import { editable } from 'mixins'
import { scanBySQL, parseSinkTableStructure } from 'services/common.service'
// import { genEncryptionKey, scanBySQL, parseSinkTableStructure, getAppKmsToken } from 'services/common.service'

export default {
  name: 'bids-datastruct-form-part',
  mixins: [editable],
  components: {
    ScanTableDialog,
    UploadTableDialog,
    BidsEditTable,
    BidsJsonSchemaEditor,
    ImportJsonDialog,
    EncryptionKeyDialog
  },
  props: {
    pageStatus: String,
    configType: String,
    type: String, // source || sink
    labelWidth: {
      type: String,
      default: '140px'
    },
    initData: {
      /* { dsAppName, dsAppKey, dsId, dsName, dsType, dsProperties: { dsUrl, dsUserName }, ... } */
      type: Object,
      default: () => {}
    },
    partialUpdate: Boolean
  },
  computed: {
    ...mapState(['dataToBeSub', 'dt_dsConnector']),
    jsonRaw() {
      return { $schema: 'http://json-schema.org/draft-04/schema#', ...this.jsonSchema.root }
    },
    configData() {
      const state = this.$store.state
      return this.configType === 'subscribe' ? state.currentSubscribeConfig : state.currentPublishConfig
    },
    label() {
      return this.type === 'source' ? '输入数据的结构' : '输出数据的结构'
    },
    connector() {
      return this.dt_dsConnector[this.configData.job.sources[0].properties.dsType]
    }
  },
  watch: {
    'form.enableEncryption'(val) {
      if (val) {
        this.rowData = this.rowData.map(item => ({ enableEncryption: false, ...item }))
        this.handleTableChange(this.rowData)
        !this.tableColumn.some(item => item.prop === 'enableEncryption') && this.tableColumn.push({ prop: 'enableEncryption', label: '是否加密', minWidth: '80', edit: { type: 'checkbox' }, formatter: (row) => row.enableEncryption ? '是' : '否' })
      } else {
        // 清除输出数据列
        this.rowData = this.rowData.map(item => {
          const { enableEncryption, ...others } = item
          return others
        })
        this.handleTableChange(this.rowData)
        const index = this.tableColumn.findIndex(item => item.prop === 'enableEncryption')
        index !== -1 && this.tableColumn.splice(index, 1)
        // 清除sink字段值
        if (this.form.encryptionWay) {
          this.form.encryptionWay = undefined
          this.updateSinkProperty(this.form.encryptionWay, 'encryptionWay')
        }
        if (this.form.encryptionPublicKey) {
          this.form.encryptionPublicKey = undefined
          this.updateSinkProperty(this.form.encryptionPublicKey, 'encryptionPublicKey')
        }
        if (this.form.encryptionPrivateKey) {
          this.form.encryptionPrivateKey = undefined
          this.updateSinkProperty(this.form.encryptionPrivateKey, 'encryptionPrivateKey')
        }
      }
    },
    'configData.collectWay'(val) {
      if (val === 'PULL' && this.form.dataFormat === 'JSON') {
        this.form.dataFormat = 'ROW'
        this.handleDataFormatChange('ROW')
      }
    }
  },
  data() {
    const suggestions = (text, cb) => {
      const typeOptions = [
        { value: 'STRING' }, { value: 'BOOLEAN' }, { value: 'DECIMAL' }, 
        { value: 'TINYINT' }, { value: 'SMALLINT' }, { value: 'INTEGER' }, { value: 'INT' },
        { value: 'BIGINT' }, { value: 'FLOAT' }, { value: 'DOUBLE' },
        { value: 'DATE' }, { value: 'TIME' }, { value: 'TIMESTAMP' },
        { value: 'JSON' }, { value: 'JSONB' }
      ]
      var results = text ? typeOptions.filter(item => item.value.toLowerCase().indexOf(text.toLowerCase()) === 0) : typeOptions
      cb(results)
    }
    // const nullOptions = [{ value: true, label: '是' }, { value: false, label: '否' }]
    return {
      form: {},
      tableColumn: [
        { prop: 'name', label: '列名', minWidth: '120', edit: { type: 'input' }, notNull: true, noRepeat: true },
        // { prop: 'type', label: '数据类型', minWidth: '120', edit: { type: 'select', options: typeOptions }, notNull: true },
        { prop: 'type', label: '数据类型', minWidth: '120', edit: { type: 'autocomplete', suggestions: suggestions }, notNull: true },
        { prop: 'seq', label: '序列号', minWidth: '70', edit: { type: 'input', increment: true }, notNull: true },
        { prop: 'isPrimary', label: '是否主键', minWidth: '90', edit: { type: 'checkbox', onlyChecked: true }, formatter: (row) => row.isPrimary ? '是' : '否' },
        // { prop: 'desc', label: '描述', minWidth: '150', edit: { type: 'input' } },
        { prop: 'default', label: '默认值', minWidth: '80', edit: { type: 'input' } }
      ],
      jsonSchema: {
        root: {
          type: 'object'
        }
      },
      rowData: [],
      rules: {
        dataFormat: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
        enableEncryption: [{ required: true, message: '请选择是否加密数据', trigger: 'change' }],
        encryptionWay: [{ required: true, message: '请选择加密算法', trigger: 'change' }],
        encryptionPublicKey: [{ required: true, message: '请输入密钥key', trigger: 'change' }],
        encryptionPrivateKey: [{ required: true, message: '请输入私钥', trigger: 'change' }]
      },
      encryptionWayOptions: [
        { value: 'RSA', label: 'RSA' }, { value: 'AES', label: 'AES' }, { value: 'MD5', label: 'MD5' },
        { value: 'SHA128', label: 'SHA128' }, { value: 'SHA256', label: 'SHA256' }
      ],
      structValid: true,
      structErrorMsg: '',
      btnLoading: false
    }
  },
  created() {
    this.updatePageData()
    if (this.type === 'sink' && this.configType === 'subscribe') {
      this.tableColumn.push({ prop: 'resultType', label: '最终类型', minWidth: '100', edit: { type: null }, formatter: (row) => row.convert ? row.convert.resultType : '' })
      this.tableColumn.push({ prop: 'convert', label: '脱敏规则', minWidth: '100', edit: { type: null }, formatter: (row) => row.convert ? row.convert.label : '' })
    }
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    updatePageData() {
      const rootData = this.type === 'source' ? this.configData.job.sources[0] : this.configData.job.sink
      const { dataFormat, enableEncryption = false, encryptionWay, encryptionPublicKey, encryptionPrivateKey } = rootData
      this.form = { dataFormat, enableEncryption, encryptionWay, encryptionPublicKey, encryptionPrivateKey }
      this.rowData = DataUtil.clone(rootData.columns)
    },
    updateSinkProperty(val, name) {
      const payload = { type: this.configType, keyPath: ['job', 'sink', name], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleEncryptionWayChange(val) {
      this.updateSinkProperty(val, 'encryptionWay')
      if (this.form.encryptionPublicKey) {
        this.form.encryptionPublicKey = undefined
        this.updateSinkProperty(this.form.encryptionPublicKey, 'encryptionPublicKey')
      }
      if (this.form.encryptionPrivateKey) {
        this.form.encryptionPrivateKey = undefined
        this.updateSinkProperty(this.form.encryptionPrivateKey, 'encryptionPrivateKey')
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleGenEncryptionKey() {
      if (!this.form.encryptionWay) {
        this.$message.warning('请先选择加密算法！')
        return
      }
      // const params = { encryptionWay: this.form.encryptionWay }
      // this.btnLoading = true
      // genEncryptionKey(params).then(res => {
      //   this.form.encryptionPublicKey = res.encryptionPublicKey
      //   this.updateSinkProperty(this.form.encryptionPublicKey, 'encryptionPublicKey')
      //   if (res.encryptionPrivateKey) {
      //     this.form.encryptionPrivateKey = res.encryptionPrivateKey
      //     this.updateSinkProperty(this.form.encryptionPrivateKey, 'encryptionPrivateKey')
      //   }
      //   this.btnLoading = false
      // }).catch(res => {
      //   this.$message.error('操作失败！原因：' + res.message || '暂无')
      //   this.btnLoading = false
      // })
      this.$refs['encryptionKeyDialog'].visible = true
    },
    handleDataFormatChange(val) {
      this.structValid = true
      const keyPath = this.type === 'source' ? ['job', 'sources', 0, 'dataFormat'] : ['job', 'sink', 'dataFormat']
      const payload = { type: this.configType, keyPath, value: val }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleScanTable() {
      if (!this.initData.dsId) {
        this.$message.warning('请先选择一个数据源！')
        return
      }
      this.$refs.scanTableDialog.show()
    },
    handleScanBySQL() {
      const dsId = this.initData.dsId
      if (!dsId) {
        this.$message.warning('请先选择一个数据源！')
        return
      }
      const query = this.configData.job.sources[0].properties.query
      if (!query) {
        this.$message.warning('请先填写数据采集的SQL语句！')
        return
      }
      const params = { dsId }
      const postData = { query }
      scanBySQL(params, postData).then(res => {
        if (res.length === 0) {
          this.$message.info('未解析到数据字段！')
        } else {
          if (res.some(item => !item.type)) {
            this.$message.warning('扫描成功，但存在未能识别类型的字段，请手工定义！')
          } else {
            this.$message.success('扫描成功！')
          }
          this.rowData = res
          this.handleTableChange(res)
        }
      }).catch(res => {
        this.$message.error('解析失败！原因：' + res.message || '暂无')
      })
    },
    handleParse() {
      const params = {}
      const data = {}
      if (!this.configData.job.transform || !this.configData.job.transform.script) {
        this.handleCopyTable()
        return
      }
      if (this.configType === 'publish') {
        params.category = 'PUB'
        data.transform = this.configData.job.transform
        data.sources = this.configData.job.sources
        const { storageStrategy } = this.configData.job.sink
        if (!data.sources[0].tableName) {
          // 第一次build
          if (storageStrategy === 'SAVE') {
            data.sources[0].tableName = `${this.configData.appKey}_${this.configData.interfaceCode}_v1`
          } else {
            data.sources[0].tableName = `PUB_${this.configData.appKey}_${this.configData.interfaceCode}`
          }
        } else {
          // 兼容build过的老数据 无携带
          if (storageStrategy === 'SAVE') {
            if (this.pageStatus === 'build') {
              data.sources[0].tableName = `${this.configData.appKey}_${this.configData.interfaceCode}_v1`
            } else if (this.pageStatus === 'clone&build') {
              const latest_version = this.configData.latestVersion?.replace(/(\d+)/, ($1) => Number($1) + 1)
              const table_name = `${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}_${latest_version}`
              data.sources[0].tableName = table_name
            } else if (this.pageStatus === 'edit') {
              const version = this.configData.version
              const table_name = `${this.configData.appKey || '应用name'}_${this.configData.interfaceCode || '接口编码'}_${version}`
              data.sources[0].tableName = table_name
            }
          }
        }
        // 校验script的表名
        if (data.transform.script) {
          const tableName = data.sources[0].tableName
          if (data.transform.script.indexOf(tableName) === -1) {
            this.$message.error('未按要求填写表名，请使用右侧第二条提示中的表名')
            return
          }
        }
      } else {
        params.category = 'SUB'
        data.transform = this.configData.job.transform
        data.pubConfigs = this.dataToBeSub.map(item => ({ configId: item.configId, version: item.version }))
      }
      parseSinkTableStructure(params, data).then(res => {
        this.handleScan(res.columns || [])
      }).catch(res => {
        this.$message.error('解析失败！原因：' + res.message || '暂无')
      })
    },
    handleCopyTable() {
      let sourceColumns = []
      if (this.configType === 'publish') {
        sourceColumns = this.configData.job.sources[0].columns
      } else {
        const hash = {}
        this.dataToBeSub.forEach(config => {
          const columns = config.job.sink.columns || []
          columns.forEach(col => hash[col.name] ? '' : hash[col.name] = true && sourceColumns.push(col))
        })
      }
      this.handleScan(DataUtil.clone(sourceColumns))
    },
    handleUploadTable() {
      if (!this.initData.dsId) {
        this.$message.warning('请先选择一个数据源！')
        return
      }
      this.$refs.uploadTableDialog.show()
    },
    handleImportJson() {
      this.$refs.importJsonDialog.show()
    },
    handleJsonConfirm(data) {
      const { $schema, ...item } = data
      this.jsonSchema.root = item
    },
    handleTableChange(data) {
      const keyPath = this.type === 'source' ? ['job', 'sources', 0, 'columns'] : ['job', 'sink', 'columns']
      const payload = { type: this.configType, keyPath, value: data }
      this.UPDATE_CONFIG_ITEM(payload)
      if (!this.structValid) {
        const properties = this.configType === 'publish' ? this.configData.job.sources[0].properties : this.configData.sink.properties
        this.structValid = data.length > 0 && this.rowData.every(item => item.type) && this.rowData.every(item => item.name) && (properties.format !== 'raw' || this.rowData.length === 1)
      }
    },
    handleClearTable() {
      this.rowData = []
      this.handleTableChange([])
    },
    handleScan(data) {
      // 解析表结构的时候，需要合并原有的配置(除序列号外)
      if (this.rowData.length) {
        data.forEach(item => {
          this.rowData.forEach(row => {
            if (item.name === row.name) {
              item.type = row.type
              item.isPrimary = row.isPrimary
              item.default = row.default
              row.convert && (item.convert = row.convert)
            }
          })
        })
      }
      this.rowData = data
      this.handleTableChange(data)
    },
    handleUpload(data) {
      this.rowData = data
      this.handleTableChange(data)
    },
    customSaveCheck(index, row) {
      // 点击保存字段时自定义一些校验，满足返回true，不满足返回false
      const types = [/^STRING$/, /^BOOLEAN$/, /^DECIMAL(\((0|[1-9]\d*),(0|[1-9]\d*)\))?$/, /^TINYINT$/, /^SMALLINT$/, /^INTEGER$/, /^INT$/, /^BIGINT$/, /^FLOAT$/, /^DOUBLE$/, /^DATE$/, /^TIME$/, /^TIMESTAMP(\(\d+\))?$/, /^JSON$/, /^JSONB$/]
      if (!types.some(item => item.test(row.type.replace(/\s+/g, '')))) {
        this.$message.warning('不符合要求的数据类型！')
        return false
      }
      const dsTypeLimit = {
        MySQL: { pMin: 1, pMax: 65, sMin: 0, sMax: 30 },
        SQLServer: { pMin: 1, pMax: 38, sMIn: 0 },
        Oracle: { pMin: 1, pMax: 38, sMIn: 0 },
        PostgreSQL: { pMin: 1, pMax: 1000, sMin: 0 }
      }
      const dsType = this.configType === 'publish' ? this.configData.job.sources[0].properties.dsType : this.configData.job.sink.properties.dsType
      if (/^DECIMAL(\((0|[1-9]\d*),(0|[1-9]\d*)\))$/.test(row.type.replace(/\s+/g, '')) && dsTypeLimit[dsType]) {
        // 带精度和小数的DECIMAL
        const match = row.type.replace(/\s+/g, '').match(/(\d+),(\d+)/)
        const limit = dsTypeLimit[dsType]
        const precision = parseInt(match[1])
        const scala = parseInt(match[2])
        if (precision < limit.pMin || precision > limit.pMax) {
          this.$message.warning(`精度超过范围[${limit.pMin}, ${limit.pMax}]`)
          return false
        }
        if (scala < limit.sMin || scala > limit.sMax) {
          this.$message.warning(`小数位数超过范围[${limit.sMin}, ${limit.sMax || 'INF'}]`)
          return false
        }
        if (scala > precision) {
          this.$message.warning(`小数位数不能超过精度`)
          return false
        } 
      }
      return true
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      if (this.form.dataFormat === 'ROW') {
        const properties = this.configType === 'publish' ? this.configData.job.sources[0].properties : this.configData.sink.properties
        if (this.rowData.length === 0) {
          validRes = false
          this.structValid = false
          this.structErrorMsg = '请定义数据的结构'
        } else if (this.rowData.some(item => !item.type) || this.rowData.some(item => !item.name)) {
          validRes = false
          this.structValid = false
          this.structErrorMsg = '存在缺失列名/数据类型的字段'
        } else if (properties.format === 'raw' && this.rowData.length !== 1) {
          validRes = false
          this.structValid = false
          this.structErrorMsg = '只能定义1个数据类型为STRING的列'
        }
      }
      if (this.form.dataFormat === 'JSON') {
        const properties = this.jsonSchema.root.properties
        if (!properties || DataType.isEmptyObject(properties)) {
          validRes = false
          this.structValid = false
        }
      }
      return validRes
    },
    // 查看token按钮
    // handleCheckToken() {
    //   const params = { configId: this.configData.configId }
    //   getAppKmsToken(params).then(res => {
    //     this.$set(this.form, 'appToken', res)
    //   }).catch(err => {
    //     this.$message.error('查看失败！原因：' + err.message || '暂无')
    //   })
    // },
    chooseEncryptionPublicKey() {
      this.$refs['encryptionKeyDialog'].show(this.configData.configId)
    },
    handleChoose(encryptionPublicKey, encryptionPrivateKey) {
      this.form.encryptionPublicKey = encryptionPublicKey
      this.updateSinkProperty(this.form.encryptionPublicKey, 'encryptionPublicKey')
      if (encryptionPrivateKey) {
        this.form.encryptionPrivateKey = encryptionPrivateKey
        this.updateSinkProperty(this.form.encryptionPrivateKey, 'encryptionPrivateKey')
      }
    },
    subscribePartialUpdateValidate() {
      if (this.partialUpdate) {
        const valid = this.rowData.some(item => item.isPrimary)
        return valid
      }
      return true
    }
  }
}
</script>
<style lang="less">
  @import "~assets/css/bids-datastruct-form-part.less";
</style>

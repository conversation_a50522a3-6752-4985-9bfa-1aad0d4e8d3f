<!--
 * @Description: 基本信息组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-06-18 15:04:43
-->
<template>
  <div class="bids-base-info">
    <el-form :model="form" :label-width="labelWidth" size="small" ref="form" :rules="rules" :disabled="!mildEditable">
      <el-form-item :label="labelType+'所属应用'" prop="appKey">
        <el-select v-model="form.appKey" filterable placeholder="请选择" v-if="onlyOnceEditable"
          @change="handleAppChange" :appLoading="appLoading">
          <el-option v-for="item in appOptions" :key="item.appKey" :label="item.appLabel" :value="item.appKey"></el-option>
        </el-select>
        <span v-else>{{form.appName}}</span>
      </el-form-item>
      <el-form-item :label="labelType+'编码'" prop="interfaceCode">
        <el-input v-model="form.interfaceCode" @change="handleInterfaceCodeChange" v-if="onlyOnceEditable"></el-input>
        <span v-else>{{form.interfaceCode}}</span>
      </el-form-item>
      <el-form-item :label="labelType+'名称'" prop="interfaceName">
        <el-input v-model="form.interfaceName" @change="handleInterfaceNameChange" v-if="onlyOnceEditable"></el-input>
        <span v-else>{{form.interfaceName}}</span>
      </el-form-item>
      <el-form-item :label="labelType+'描述'" prop="interfaceDesc">
        <el-input resize="none" rows="4" type="textarea" v-model="form.interfaceDesc" @change="handleDescChange" v-if="mildEditable"></el-input>
        <span v-else>{{form.interfaceDesc}}</span>
      </el-form-item>
      <el-form-item label="负责/维护人" prop="ownersId">
        <sdc-staff-selector v-model="form.ownersId" multiple size="small" ref="staffSelector" @change="handleOwnersChange"
          :includeDimission="true" :showTotal="false" :props="{ staffName: 'staffName', staffID: 'staffId' }"
          :disabled="['read'].includes(pageStatus)" v-if="mildEditable"/>
        <span v-else>{{configData.owners.map(item => item.staffName).join('；')}}</span>
      </el-form-item>
      <el-form-item label="构造模式" prop="buildMode">
        <el-radio-group v-model="form.buildMode" :disabled="true" v-if="onlyOnceEditable">
          <el-radio label="simple">简单模式</el-radio>
          <el-radio label="professional">专家模式</el-radio>
        </el-radio-group>
        <span v-else>专家模式</span>
      </el-form-item>
    </el-form>
    <div class="description-panel" v-if="mildEditable">
      <div class="question">
        <i class="el-icon-question"></i>
        <span>所属应用的选择</span>
      </div>
      <div class="answer">
        <span>可选择HR技术中台-服务市场上的应用，后续也仅能新增/选择该应用下的数据源进行数据的发布/订阅，若需新增应用，请先到服务市场上</span>
        <el-link type="primary" href="http://ntsgw.oa.com/#/main/servicemarket/home-app" target="_blank">注册应用</el-link>，
        <span>若找不到对应应用，你可能需要去<el-link type="primary" href="http://ntsgw.oa.com/#/main/fly/home-page-app" target="_blank">申请</el-link>该应用的授权</span>
      </div>
      <div class="question">
        <i class="el-icon-question"></i>
        <span>构造模式的选择</span>
      </div>
      <div class="answer">
        <span>简单模式适用于直接按数据原有格式输出的发布/订阅，配置项较少；专家模式适用于需要按一定规则将数据进行加工后再输出的发布/订阅，配置项较多</span>
      </div>
    </div>
  </div>
</template>
<script>
import { queryApps, queryDataSources, checkCodeExist } from 'services/common.service'
import { mapMutations, mapState } from 'vuex'
import { editable } from 'mixins'
export default {
  name: 'bids-base-info',
  mixins: [editable],
  props: {
    pageStatus: String,
    data: {
      type: Object,
      default: () => {}
    },
    configType: {
      type: String,
      default: 'publish'
    },
    subEditable: { // 用于subscribe-edit的按钮权限
      type: Boolean,
      default: true
    }
  },
  data() {
    const validatorOwners = (rule, value, callback) => {
      if (this.form.ownersId.length) {
        callback()
      } else {
        callback(new Error('请选择负责人'))
      }
    }
    const checkInterfaceCode = (rule, value, callback) => {
      if (!this.form.appKey || !this.onlyOnceEditable) return callback()
      // 根据configId的存在判断是编辑
      if (this.configData.configId && this.coypObj.interfaceCode === value) return callback()
      const params = { appKey: this.form.appKey, interfaceCode: value }
      checkCodeExist(params).then(res => {
        if (res) {
          callback(new Error('该编码已存在'))
        } else {
          callback()
        }
      }).catch(res => {
        this.$message.error('校验编码重复性失败！原因：' + res.message)
        callback()
      })
    }
    return {
      form: {},
      appOptions: [],
      rules: {
        appKey: [{ required: true, message: '请选择所属应用', trigger: 'change' }],
        interfaceCode: [
          { required: true, message: '请输入编码' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_-]{0,}$/, message: '以字母开头，只能包含字符、数字、中划线和下划线' },
          { validator: checkInterfaceCode, trigger: 'blur' }
        ],
        interfaceName: [{ required: true, message: '请输入接口名称' }],
        interfaceDesc: [{ required: true, message: '请输入接口描述' }],
        ownersId: [
          { required: true, message: '请选择负责人' },
          { validator: validatorOwners, trigger: 'change' }
        ],
        buildMode: [{ required: true, message: '请选择构造模式' }]
      },
      appLoading: false,
      labelWidth: '140px',
      coypObj: {},
      showSelector: true
    }
  },
  computed: {
    ...mapState(['userInfo']),
    configData() {
      const state = this.$store.state
      return this.configType === 'publish' ? state.currentPublishConfig : state.currentSubscribeConfig
    },
    labelType() {
      return this.configType === 'publish' ? '数据发布' : '数据订阅'
    }
  },
  created() {
    this.onlyOnceEditable && this.fetchData()
    this.updatePageData()
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    fetchData() {
      this.appLoading = true
      queryApps().then(res => {
        this.appLoading = false
        this.appOptions = res.map(item => ({
          appKey: item.name,
          appName: item.label,
          appLabel: item.name + '（' + item.label + '）'
        }))
      }).catch(res => {
        this.appOptions = []
        this.appLoading = false
        this.$message.error('获取应用下拉列表失败！原因：' + res.message || '暂无')
      })
    },
    updatePageData() {
      Object.assign(this.coypObj, this.configData)
      const { appKey, appName, interfaceCode, interfaceName, interfaceDesc, owners } = this.configData
      if (appKey && appName && this.appOptions.length === 0) {
        this.appOptions = [{ appKey, appName, appLabel: appKey + '（' + appName + '）' }]
      }
      if (appKey && !this.configData.cache.dataSourceOptions && this.mildEditable) {
        const params = { appKey }
        queryDataSources(params).then(res => {
          // if (this.configType === 'publish') {
          //   res = res.filter(item => !['ES6', 'ES7'].includes(item.dsType)).sort(this.dsSort)
          // } else {
          //   res = res.filter(item => !['ESjdbc', 'Hive'].includes(item.dsType)).sort(this.dsSort)
          // }
          this.UPDATE_CONFIG_ITEM({ type: this.configType, keyPath: ['cache', 'dataSourceOptions'], value: res.sort(this.dsSort) })
        }).catch(res => {
          this.UPDATE_CONFIG_ITEM({ type: this.configType, keyPath: ['cache', 'dataSourceOptions'], value: null })
        })
      }
      this.form = { appKey, appName, interfaceCode, interfaceName, interfaceDesc, buildMode: 'professional' }
      this.mildEditable && this.$nextTick(() => {
        if (owners && owners.length > 0) {
          this.$refs.staffSelector.setSelected(owners.map(item => ({
            staffName: item.staffName,
            staffId: Number.parseInt(item.staffId)
          })))
        } else if (this.$route.query.mode === 'cloning' || (this.pageStatus === 'build' && !this.$route.query.configId)) {
          this.$refs.staffSelector.getPasteResult(`${this.userInfo.engName};`).then(res => {
            this.$refs.staffSelector.setSelected(res.map(item => ({ staffId: item.StaffID, staffName: item.StaffName })))
          })
        }
      })
    },
    handleAppChange(val) {
      this.form.appName = this.appOptions.find(item => item.appKey === val).appName
      this.UPDATE_CONFIG_ITEM({ type: this.configType, keyPath: ['appKey'], value: val })
      this.UPDATE_CONFIG_ITEM({ type: this.configType, keyPath: ['appName'], value: this.form.appName })
      const params = { appKey: val }
      this.mildEditable && queryDataSources(params).then(res => {
        if (this.configType === 'publish') {
          // res = res.filter(item => !['ES6', 'ES7'].includes(item.dsType)).sort(this.dsSort)
          res = res.sort(this.dsSort)
        } else {
          res = res.filter(item => !['ESjdbc', 'Hive'].includes(item.dsType)).sort(this.dsSort)
        }
        this.UPDATE_CONFIG_ITEM({ type: this.configType, keyPath: ['cache', 'dataSourceOptions'], value: res })
      }).catch(res => {
        this.UPDATE_CONFIG_ITEM({ type: this.configType, keyPath: ['cache', 'dataSourceOptions'], value: [] })
      })
    },
    handleInterfaceCodeChange(val) {
      const payload = { type: this.configType, keyPath: ['interfaceCode'], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleInterfaceNameChange(val) {
      const payload = { type: this.configType, keyPath: ['interfaceName'], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleDescChange(val) {
      const payload = { type: this.configType, keyPath: ['interfaceDesc'], value: val }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    handleOwnersChange(data) {
      const owners = data.map(item => ({
        staffId: item.staffId,
        staffName: item.staffName
      }))
      const payload = { type: this.configType, keyPath: ['owners'], value: owners }
      this.UPDATE_CONFIG_ITEM(payload)
    },
    dsSort(a, b) {
      const keyA = a.dsType.toUpperCase()
      const keyB = b.dsType.toUpperCase()
      if (keyA < keyB) {
        return -1
      }
      if (keyA > keyB) {
        return 1
      }
      return 0
    },
    formValidate() {
      let validRes = true
      this.$refs['form'].validate((valid) => {
        validRes = valid
      })
      return validRes
    }
  }
}
</script>
<style lang="less">
  @import "~assets/css/bids-base-info.less";
</style>

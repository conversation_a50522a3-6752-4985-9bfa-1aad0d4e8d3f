/*
 * @Description: 用于判断页面上组件内的表单项是否可编辑
 * @Autor: kenoxia
 * @LastEditTime: 2021-01-07 17:45:01
 */
export default {
  computed: {
    mildEditable() {
      return this.pageStatus !== 'read'
    },
    moderateEditable() {
      const configData = this.configType === 'publish' ? this.$store.state.currentPublishConfig : this.$store.state.currentSubscribeConfig
      return this.pageStatus === 'build' || this.pageStatus === 'clone&build' || (this.pageStatus === 'edit' && configData.jobStatus === -1)
    },
    strictEditable() {
      return this.pageStatus === 'build' || this.pageStatus === 'clone&build'
    },
    onlyOnceEditable() {
      return this.pageStatus === 'build'
    }
  }
}

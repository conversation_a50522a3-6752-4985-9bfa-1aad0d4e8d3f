<!--
 * @Description: 数据订阅草稿提交确认弹窗组件，展示提交草稿的基本信息
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-18 11:35:46
-->
<template>
  <div class="commit-confirm-dialog">
    <el-dialog title="确认本次数据订阅的详细信息" :visible.sync="visible" :before-close="handleClose" width="900px" top="6vh">
      <el-tabs v-model="currentTab" type="card" @tab-click="handleTabClick">
        <el-tab-pane v-for="(item, index) in initData" :key="index" :label="(index+1).toString()" :name="(index+1).toString()">
        </el-tab-pane>
      </el-tabs>
      <el-form :model="form" label-position="left" label-width="150px">
        <!-- <el-form-item label="配置ID_版本">
          <span>{{`${currentData.configId}_${currentData.version}`}}</span>
        </el-form-item> -->
        <el-form-item label="[所属应用]订阅名称">
          <span>{{`[${currentData.appName}] ${currentData.interfaceName}`}}</span>
        </el-form-item>
        <el-form-item label="数据获取方式">
          <span>{{currentData.collectWay}}</span>
        </el-form-item>
        <el-form-item label="输出方式">
          <span>{{currentData.collectType === 'PULL' ? '拉取' : '推送'}}</span>
        </el-form-item>
        <el-form-item label="数据输出去向">
          <span>{{currentData.collectWay === 'PULL' ? currentData.format : `(${currentData.dsType})${currentData.dsName}`}}</span>
        </el-form-item>
        <el-form-item label="开始时间">
          <span>{{currentData.scheduleBeginTime || '无'}}</span>
        </el-form-item>
        <el-form-item label="订阅周期">
          <span>{{currentData.scheduleUnit}}</span>
        </el-form-item>
        <el-form-item label="数据订阅Owners">
          <span>{{currentData.owners}}</span>
        </el-form-item>
        <el-form-item label="输出数据结构">
          <!-- <span>{{currentData.dataFormat}}</span> -->
        </el-form-item>
      </el-form>
      <el-table :data="currentData.columns" style="width: 100%" height="400" 
        :header-cell-style="{background:'#f5f7f9'}" v-if="currentData.dataFormat === 'ROW'">
        <el-table-column v-for="(item, index) in tableColumn" :key="index"
          :prop="item.prop"
          :label="item.label"
          :formatter="item.formatter"
          :min-width="item.minWidth"
          align="center">
        </el-table-column>
      </el-table>
      <el-input type="textarea" :rows="12" resize="none" readonly v-if="currentData.dataFormat === 'JSON'"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="medium">取消</el-button>
        <el-button size="medium" type="primary" @click="handleSubmit">确认提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { formatter } from 'mixins'

export default {
  name: 'commit-confirm-dialog',
  mixins: [formatter],
  props: {
    initData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState(['dt_collectType', 'dt_scheduleUnit'])
  },
  data() {
    return {
      visible: false,
      form: {},
      tableData: [],
      tableColumn: [
        { prop: 'name', label: '列名', minWidth: '100' },
        { prop: 'type', label: '数据类型', minWidth: '100' },
        { prop: 'seq', label: '序列号', minWidth: '50' },
        { prop: 'abstract', label: '摘要', minWidth: '140', formatter: this.formatterAbstract },
        { prop: 'desc', label: '描述', minWidth: '130' },
        { prop: 'default', label: '默认值', minWidth: '70' }
      ],
      currentTab: '',
      currentData: {}
    }
  },
  methods: {
    show() {
      this.fetchData()
      this.visible = true
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    handleSubmit() {
      this.$emit('confirm')
      this.visible = false
    },
    handleTabClick(tab) {
      this.currentData = this.transform(this.initData[parseInt(tab.index)])
    },
    fetchData() {
      this.currentData = this.transform(this.initData[0])
      this.currentTab = this.initData[0] ? '1' : ''
    },
    transform(data) {
      if (!data) {
        return {}
      }
      const { configId, version, appName, collectWay, interfaceName } = data
      const { dataFormat, columns, properties } = data.job.sink
      const { dsType, dsName, format } = properties
      const collectType = data.collectType ? this.dt_collectType.get(data.collectType) : ''
      const owners = data.owners ? data.owners.map(item => item.staffName).join('；') : ''
      let scheduleUnit = this.formatterScheduleAtCron(data)
      let scheduleBeginTime = data.setting.scheduleStrategy.scheduleBeginTime
      return { configId, version, appName, interfaceName, collectType, collectWay, format, dsType, dsName, owners, scheduleUnit, scheduleBeginTime, dataFormat, columns }
    }
  }
}
</script>

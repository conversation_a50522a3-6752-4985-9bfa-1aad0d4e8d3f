<!--
 * @Description: 订阅数据发布弹窗组件，展示待订阅数据发布的基本信息
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-15 18:11:27
-->
<template>
  <el-dialog title="请选择待订阅的数据" append-to-body :visible.sync="visible" :before-close="handleClose" width="900px" top="6vh">
    <div class="choose-pub-data-dialog">
      <el-form :model="baseInfo" inline size="small">
        <el-form-item label="所属应用">
          <span>{{baseInfo.appName}}</span>
        </el-form-item>
        <el-form-item label="接口名称">
          <span>{{baseInfo.interfaceName}}</span>
        </el-form-item>
        <el-form-item label="接口描述">
          <span>{{baseInfo.interfaceDesc}}</span>
        </el-form-item>
      </el-form>
      <div class="content">
        <el-tabs v-model="currentTab" class="version-tabs" type="card" @tab-click="handleTabClick">
          <el-tab-pane v-for="item in data" :key="item.version" :name="item.version">
            <span slot="label" class="verson-tab-label">
            <span class="label-text">{{item.version}}</span>
            <i style="color:#0ad0b6;" class="el-icon-success" v-if="item.jobStatus === 1"></i>
            <i style="color:#ACACAC;" class="el-icon-remove" v-else></i>
          </span>
          </el-tab-pane>
        </el-tabs>
        <div class="toolbar">
          <el-button size="mini" type="primary" @click="joinSubsribe" :disabled="currentData.jobStatus !== 1">加入订阅</el-button>
        </div>
        <el-form :model="form" label-position="left" label-width="140px" size="mini">
          <el-form-item label="当前状态">
            <span><el-tag v-if="transData.jobStatus" effect="dark" size="small" :type="currentData.jobStatus === 1 ? 'success' : 'info'">{{transData.jobStatus}}</el-tag></span>
          </el-form-item>
          <el-form-item label="数据来源">
            <span>{{transData.dsType ? `(${transData.dsType})${transData.dsName}` : ''}}</span>
          </el-form-item>
          <el-form-item label="采集类型">
            <span>{{transData.collectType}}</span>
          </el-form-item>
          <el-form-item label="数据发布周期">
            <span>{{transData.scheduleUnit}}</span>
          </el-form-item>
          <el-form-item label="负责/维护人">
            <span>{{transData.owners}}</span>
          </el-form-item>
          <el-form-item label="输出数据的结构">
          </el-form-item>
        </el-form>
        <el-table :data="transData.columns" style="width: 100%" height="300" 
          :header-cell-style="{background:'#f5f7f9'}" v-if="transData.dataFormat === 'ROW'">
          <el-table-column v-for="(item, index) in tableColumn" :key="index"
            :prop="item.prop"
            :label="item.label"
            :formatter="item.formatter"
            :min-width="item.minWidth"
            align="center">
          </el-table-column>
        </el-table>
        <el-input type="textarea" :rows="12" resize="none" readonly v-if="transData.dataFormat === 'JSON'"></el-input>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false" size="medium">关闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import { queryData } from 'services/publish.service'
import { formatter } from 'mixins'

export default {
  name: 'choose-pub-data-dialog',
  mixins: [formatter],
  computed: {
    ...mapState({
      configData: state => state.currentSubscribeConfig,
      dt_collectType: 'dt_collectType',
      dt_jobStatus: 'dt_jobStatus',
      dataToBeSub: 'dataToBeSub'
    })
  },
  data() {
    return {
      visible: false,
      baseInfo: {},
      form: {},
      tableColumn: [
        { prop: 'name', label: '列名', minWidth: '130' },
        { prop: 'type', label: '数据类型', minWidth: '100' },
        { prop: 'seq', label: '序列号', minWidth: '50' },
        { prop: 'abstract', label: '摘要', minWidth: '120', formatter: this.formatterAbstract },
        { prop: 'desc', label: '描述', minWidth: '50' }
        // { prop: 'default', label: '默认值', minWidth: '70' }
      ],
      currentTab: '',
      transData: {},
      currentData: {},
      data: []
    }
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM', 'UPDATE_DATA_TO_BE_SUB']),
    show(row) {
      this.currentTab = ''
      this.transData = {}
      this.data = []
      this.visible = true
      this.fetchData(row.configId)
      this.baseInfo = { appName: row.appName, interfaceName: row.interfaceName, interfaceDesc: row.interfaceDesc }
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    handleTabClick(tab) {
      this.currentData = this.data.find(item => item.version === tab.name)
      this.transData = this.transform(this.currentData)
    },
    fetchData(configId) {
      const params = { configId }
      queryData(params).then(res => {
        if (res.length > 0) {
          this.data = res
          this.currentData = res[0]
          this.transData = this.transform(res[0])
          this.currentTab = res[0].version
        }
      }).catch(res => {
        this.$message.error('获取该数据发布的版本详情失败！原因：' + res.message || '暂无')
      })
    },
    joinSubsribe() {
      const sources = this.configData.job.sources
      if (sources.some(item => item.properties.subConfigId === this.currentData.configId)) {
        this.$message.warning('该数据发布已有版本在待订阅列表中，请先移除！')
        return
      }
      const newSource = { properties: { subConfigId: this.currentData.configId, subConfigVersion: this.currentData.version } }
      const payload = { type: 'subscribe', keyPath: ['job', 'sources'], value: sources.concat([newSource]) }
      this.UPDATE_CONFIG_ITEM(payload)
      this.UPDATE_DATA_TO_BE_SUB(this.dataToBeSub.concat([this.currentData]))
      this.visible = false
      this.$emit('added')
    },
    transform(data) {
      if (!data) {
        return {}
      }
      const { dataFormat, columns } = data.job.sink
      const { dsType, dsName } = data.job.sources[0].properties
      const collectType = data.collectType ? this.dt_collectType.get(data.collectType) : ''
      const owners = data.owners ? data.owners.map(item => item.staffName).join('；') : ''
      const jobStatus = this.dt_jobStatus.get(data.jobStatus)
      let scheduleUnit = this.formatterScheduleAtCron(data)
      return { jobStatus, collectType, scheduleUnit, dsType, dsName, dataFormat, owners, columns }
    }
  }
}
</script>

<style lang="less">
.choose-pub-data-dialog {
  .toolbar {
    float: right;
    margin-right: 15px;
  }
  .el-tag {
    border-radius: 0px;
  }
  label {
    width: 70px;
    color: #99a9bf;
  }
  .el-form--inline {
    .el-form-item {
      margin-right: 50px;
    }
  }
  .content {
    margin: 0 10px;
    .el-form-item {
      margin-right: 100px; // 不遮挡按钮
    }
  }
}
</style>

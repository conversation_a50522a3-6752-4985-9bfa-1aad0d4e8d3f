<template>
  <div class="sync-result-dialog">
    <el-dialog title="同步结果" :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" width="1002px" top="6vh">
      <el-table :data="tableData" style="width: 100%" height="500" :header-cell-style="{ background: '#f5f7f9', height: '60px' }">
        <el-table-column type="index" :index="(pageIndex-1)*10+1" width="50" align="center"></el-table-column>
        <template v-if="type === 'publish'">
          <el-table-column label="发布名称" prop="name" min-width="188" show-overflow-tooltip></el-table-column>
          <el-table-column label="发布编码" prop="code" min-width="183" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属应用" prop="app" min-width="172" show-overflow-tooltip></el-table-column>
          <el-table-column label="发布版本" prop="version" min-width="134">
            <template> template </template>
          </el-table-column>
        </template>

        <template v-if="type === 'subscribe'">
          <el-table-column label="订阅名称" prop="name" min-width="188" show-overflow-tooltip></el-table-column>
          <el-table-column label="订阅编码" prop="code" min-width="183" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属应用" prop="app" min-width="172" show-overflow-tooltip></el-table-column>
          <el-table-column label="订阅版本" prop="version" min-width="134">
            <template> template </template>
          </el-table-column>
        </template>

        <el-table-column label="校验结果" prop="result" min-width="114"></el-table-column>
        <el-table-column label="详情" width="115">
          <template v-slot="{ row }">
            <el-button @click="handleView(row)" type="text" size="small">查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pagination } from 'mixins'

export default {
  name: 'sync-result-dialog',
  mixins: [pagination], // 分页
  props: {
    initData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      type: 'publish',
      visible: false,
      paginationType: 'remote', // 由服务器实现分页查询
      tableData: [
        { code: 123, status: 'success' },
        { code: 123, status: 'success' },
        { code: 123, status: 'success' },
        { code: 123, status: 'success' },
        { code: 123, status: 'success' },
        { code: 123, status: 'success' },
        { code: 123, status: 'success' },
        { code: 123, status: 'fail' },
        { code: 123, status: 'fail' },
        { code: 123, status: 'fail' },
        { code: 123, status: 'fail' },
        { code: 123, status: 'fail' },
        { code: 123, status: 'fail' },
        { code: 123, status: 'fail' },
        { code: 123, status: 'fail' }
      ]
    }
  },
  methods: {
    show() {
      this.fetchData()
      this.visible = true
    },
    handleClose(done) {
      this.$emit('close')
      done()
    },
    fetchData() {},
    handleView(row) {
      if (row.status === 'success') {
        this.$emit('view', row)
        return
      }
      this.$confirm('原因xxxxx', '同步失败', { type: 'error' }).then(() => {
        
      }).catch(() => {
        
      })
    }
  }
}
</script>

<style lang="less" scoped>
.sync-result-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }
  .pagination {
    margin-top: 20px;
  }
}
</style>

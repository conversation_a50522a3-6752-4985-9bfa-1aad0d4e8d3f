<!--
 * @Description: 选择待订阅数据发布组件
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-15 18:11:49
-->
<template>
  <div class="choose-pub-data">
    <div class="search-wrap">
      <div class="wrap-header">
        <div class="filter">
          <el-input placeholder="请搜索关键词" v-model="criteriaValue" size="medium" @input="handleInput">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
            <el-select v-model="criteriaKey" slot="prepend" placeholder="请选择" @change="handleChange">
              <el-option v-for="item in pubFilterOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-input>
        </div>
      </div>
      <div class="wrap-table">
        <el-table
          :data="tableData"
          v-loading="loading"
          height="100%"
          :header-cell-style="{background:'#f5f7f9', height: '60px'}"
          style="width: 100%">
          <el-table-column
            type="index"
            width="50">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in dataTableColumn" :key="index"
            :prop="item.prop"
            :label="item.label"
            :formatter="item.formatter"
            :min-width="item.minWidth || item.width"
            :show-overflow-tooltip="true"
            align="center">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            width="100">
            <template slot-scope="scope">
              <el-button @click="handleSubscribe(scope.row)" type="text" size="small">订阅</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordCount">
        </el-pagination>
      </div>
    </div>
    <choose-pub-data-dialog ref="choosePubDataDialog" @added="$emit('added')"></choose-pub-data-dialog>
  </div>
</template>

<script>
import { pagination, filterInput, formatter } from 'mixins'
import { queryAllData } from 'services/publish.service'
import { mapState, mapMutations } from 'vuex'
import ChoosePubDataDialog from './choose-pub-data-dialog'

export default {
  name: 'choose-pub-data',
  components: {
    ChoosePubDataDialog
  },
  mixins: [pagination, formatter, filterInput],
  computed: mapState({
    configData: state => state.currentSubscribeConfig
  }),
  data() {
    return {
      data: [],
      selectedData: [],
      loading: false,
      selectLoading: false,
      paginationType: 'remote',
      tableData: [],
      dataTableColumn: [
        { prop: 'appName', label: '所属应用', minWidth: '80' },
        { prop: 'interfaceName', label: '发布名称', minWidth: '160' },
        { prop: 'interfaceCode', label: '发布编码', minWidth: '100' },
        { prop: 'interfaceDesc', label: '发布描述', minWidth: '120' },
        // { prop: 'dataFormat', label: '数据类型', minWidth: '80' },
        // { prop: 'jobType', label: '来源库类型', minWidth: '95' },
        { prop: 'collectType', label: '采集类型', minWidth: '80', formatter: this.formatterCollectType }
        // { prop: 'collectWay', label: '接入方式', minWidth: '90' },
        // { prop: 'schedulePeriodCron', label: '发布周期', minWidth: '110', formatter: this.formatterScheduleAtCron }
        // { prop: 'jobStatus', label: '状态', minWidth: '70', formatter: this.formatterJobStatus }
      ]
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    ...mapMutations(['UPDATE_CONFIG_ITEM']),
    fetchData() {
      const paginator = { pageSize: this.pageSize, pageIndex: this.pageIndex }
      const criteria = []
      if (this.criteriaValue && this.criteriaKey) {
        criteria.push({ criteriaKey: this.criteriaKey, criteriaValue: this.criteriaValue })
      }
      const postData = { paginator, criteria }
      this.loading = true
      queryAllData(postData).then(res => {
        res = res || { paginator: {} }
        this.tableData = res.content || []
        this.recordCount = res.paginator.recordCount || 0
        this.loading = false
      }).catch(res => {
        this.tableData = []
        this.recordCount = 0
        this.$message.error('获取数据发布失败！原因：' + res.message || '暂无')
        this.loading = false
      })
    },
    handleSubscribe(row) {
      this.$refs['choosePubDataDialog'].show(row)
    },
    formValidate() {
      let validRes = true
      if (this.selectedData.length === 0) {
        validRes = false
      }
      return validRes
    }
  }
}
</script>

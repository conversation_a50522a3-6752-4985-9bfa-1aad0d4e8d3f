<!--
 * @Description: 新增数据发布页面
 * @Autor: kenoxia
 * @LastEditTime: 2021-03-24 14:23:09
-->
<template>
  <div class="publish-add">
    <div class="page-header">
      <div class="header-left">
        <el-page-header @back="goBack">
          <div slot="content">
            新增发布
            <span class="tips">可预先定义多条待发布的数据</span>
          </div>
        </el-page-header>
      </div>
      <div class="header-right">
        <el-button plain size="medium" @click="handleAdd()">构造数据</el-button>
        <el-button type="primary" size="medium" @click="handleCommit()">批量提交</el-button>
      </div>
    </div>
    <div class="page-main">
      <div class="table">
        <el-table
          :data="tableData"
          :header-cell-style="{background:'#f5f7f9', height: '60px'}"
          @selection-change="handleSelectionChange"
          style="width: 100%">
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableColumn" :key="index"
            :prop="item.prop"
            :label="item.label"
            :formatter="item.formatter"
            :min-width="item.minWidth || item.width"
            :show-overflow-tooltip="true"
            align="center">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            width="170">
            <template slot-scope="scope">
              <el-button @click="handleEdit(scope.row)" type="text" size="small" :loading="scope.row['btnLoading']">编辑</el-button>
              <el-button @click="handleDelete(scope.$index, scope.row)" type="text" size="small" class="button-text-danger" :loading="scope.row['btnLoading']">移除</el-button>
              <el-popover trigger="click" placement="bottom-start" @show="handlePopoverShow(scope.row)">
                <div style="width:440px;">
                  <el-tag type="warning" size="small" v-loading="tagLoading" element-loading-spinner="el-icon-loading">{{dt_jobState[scope.row.job.draftStatus] || '请刷新'}}</el-tag>
                  <el-divider direction="vertical"></el-divider>
                  <div style="display:inline-block;" v-loading="btnLoading">
                    <el-button size="mini" type="primary" round @click="handleRun(scope.row)" >启动</el-button>
                    <el-button size="mini" type="danger" round @click="handleKill(scope.row)">停止</el-button>
                    <el-button size="mini" round @click="handleRefresh(scope.row)">刷新</el-button>
                    <el-button size="mini" type="info" round @click="handleShowLog(scope.row)">查看日志</el-button>
                    <el-button size="mini" type="info" round @click="handleShowData(scope.row)">查看数据</el-button>
                  </div>
                </div>
                <el-button slot="reference" style="margin-left:10px;" type="text" size="small" class="button-text-other" :loading="scope.row['btnLoading']">测试</el-button>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <commit-confirm-dialog ref="commitConfirmDialog" :init-data="selectedData" @confirm="handleConfirm"></commit-confirm-dialog>
    <bids-log-dialog ref="logDialog" :label="dialogLabel"></bids-log-dialog>
    <bids-data-dialog ref="dataDialog" :label="dialogLabel"></bids-data-dialog>
    <bids-batch-result-dialog ref="batchResDialog"></bids-batch-result-dialog>
  </div>
</template>

<script>
import CommitConfirmDialog from './commit-confirm-dialog'
import BidsLogDialog from 'components/bids-log-dialog'
import BidsDataDialog from 'components/bids-data-dialog'
import BidsBatchResultDialog from 'components/bids-batch-result-dialog'
import { queryDrafts, removeDrafts, submitDrafts } from 'services/publish.service'
import { runJob, killJob, queryJobState } from 'services/common.service'
import { formatter } from 'mixins'
import { mapState } from 'vuex'

export default {
  name: 'publish-add',
  mixins: [formatter], // 格式化表格内容
  components: {
    CommitConfirmDialog, // 提交确认弹窗组件
    BidsLogDialog, // 查看日志弹窗组件
    BidsDataDialog, // 查看数据弹窗组件
    BidsBatchResultDialog // 批量操作结果列表组件
  },
  computed: {
    ...mapState(['dt_jobState']) // 任务状态字段
  },
  data() {
    return {
      dialogLabel: '', // 查看日志弹窗任务ID的label
      tableData: [], // 发布草稿表格数据
      btnLoading: false,
      tagLoading: false,
      selectedData: [], // 表格选中数据
      tableColumn: [
        { prop: 'appName', label: '所属应用', minWidth: '120' },
        { prop: 'interfaceName', label: '接口名称', minWidth: '120' },
        { prop: 'interfaceCode', label: '接口编码', minWidth: '100' },
        // { prop: 'interfaceDesc', label: '接口描述', minWidth: '120' },
        // { prop: 'dataFormat', label: '数据类型', minWidth: '80', formatter: this.formatterDataFormat },
        { prop: 'collectType', label: '采集类型', minWidth: '80', formatter: this.formatterCollectType },
        { prop: 'scheduleBeginTime', label: '开始时间', minWidth: '120', formatter: this.formatterScheduleBeginTime },
        { prop: 'scheduleUnit', label: '发布周期', minWidth: '110', formatter: this.formatterScheduleAtCron },
        { prop: 'storageStrategy', label: '落地策略', minWidth: '80', formatter: this.formatterStorageStrategy },
        { prop: 'jobStatus', label: '状态', minWidth: '70', formatter: this.formatterJobStatus }
      ]
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      // 获取发布草稿
      const postData = []
      queryDrafts(postData).then(res => {
        this.tableData = res.map(item => ({ btnLoading: false, ...item }))
      }).catch(res => {
        this.tableData = []
        this.$message.error('获取待发布的数据草稿失败！原因：' + res.message || '暂无')
      })
    },
    handleSelectionChange(val) {
      this.selectedData = val
    },
    async handleCommit() {
      if (this.tableData.length === 0) {
        this.$message.info('暂无需要提交的数据发布！')
        return
      }
      if (this.selectedData.length === 0) {
        this.$message.info('请先选择需要提交的数据发布！')
        return
      }
      // 刷新下待提交草稿的最新状态
      const jobNameData = this.selectedData.map(item => ({ jobName: item.jobName }))
      this.$sdc.loading()
      try {
        const res = await queryJobState(jobNameData)
        this.selectedData.forEach(data => {
          const item = res.find(item => item.jobName === data.jobName)
          data.job.draftStatus = item.state
          data.lastModifiedTime = item.lastModifiedTime
        })
      } catch (error) {}
      this.$sdc.loading.hide()
      // 筛选不满足提交要求的草稿，全量的草稿需执行成功（即也不处于运行中），增量的草稿需已停止
      let cantData = this.selectedData.filter(item => {
        if (item.collectType === 1 && ['SUCCEEDED', 'EVENT_AFTER'].includes(item.job.draftStatus)) {
          return false
        }
        if (item.collectType === 2 && ['KILLED'].includes(item.job.draftStatus)) {
          return false
        }
        return true
      })
      if (cantData.length) {
        this.$message.warning('提交的数据发布不满足要求，请重新选择！')
        cantData = cantData.map(item => ({
          interfaceCode: item.interfaceCode,
          interfaceDesc: item.interfaceDesc,
          message: `测试实例${item.collectType === 1 ? '未执行成功' : '执行失败或未停止'}（状态：${this.dt_jobState[item.job.draftStatus]}）`
        }))
        // 展示草稿不满足要求的原因
        this.$refs['batchResDialog'].show(cantData)
        return
      }
      this.$refs.commitConfirmDialog.show()
    },
    handleConfirm() {
      const postData = this.selectedData.map(item => ({
        configId: item.configId,
        version: item.version,
        lastModifiedTime: item.lastModifiedTime
      }))
      submitDrafts(postData).then(res => {
        const failedRes = res.filter(item => !item.success) // 筛选提交失败的草稿
        if (failedRes.length === 0) {
          this.$message.success('提交成功！')
          this.$router.push({ name: 'PublishList' })
        } else if (failedRes.length === this.selectedData.length) {
          this.$message.error('提交失败！')
        } else {
          this.$message.warning('部分提交成功！')
        }
        // 删去表格里提交成功的草稿
        this.tableData = this.tableData.filter(data => !this.selectedData.some(item => item.configId === data.configId) || failedRes.some(item => {
          if (item.configId === data.configId) {
            item.interfaceCode = data.interfaceCode
            item.interfaceDesc = data.interfaceDesc
            return true
          } else {
            return false
          }
        }))
        // 展示提交失败的草稿
        if (failedRes.length) {
          this.$refs['batchResDialog'].show(failedRes)
        }
      }).catch(res => {
        this.$message.error('提交失败！原因：' + res.message || '暂无')
      })
    },
    handleAdd() {
      this.$router.push({ name: 'PublishBuild' })
    },
    handleDelete(index, row) {
      this.$confirm('确认移除该行待发布的数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const postData = [{ configId: row.configId, version: row.version, lastModifiedTime: row.lastModifiedTime }]
        row.btnLoading = true
        if (row.job.draftStatus === 'RUNNNING') {
          this.$message.error('该数据发布有正在运行的测试实例，请先停止!')
          return
        }
        removeDrafts(postData).then(res => {
          row.btnLoading = false
          this.$message.success('操作成功！')
          this.tableData.splice(index, 1)
        }).catch(res => {
          row.btnLoading = false
          this.$message.error('操作失败！原因：' + res.message || '暂无')
        })
      }).catch(() => {})
    },
    handleEdit(row) {
      const { btnLoading, ...configData } = row
      this.$router.push({ name: 'PublishBuild', query: { configId: configData.configId } })
    },
    handleRun(row) {
      this.btnLoading = true
      const params = { jobName: row.jobName }
      runJob(params).then(res => {
        const { state, lastModifiedTime } = res
        row.job.draftStatus = state
        row.lastModifiedTime = lastModifiedTime
        this.$message.success('操作成功！')
        this.btnLoading = false
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        this.btnLoading = false
      })
    },
    handleKill(row) {
      this.btnLoading = true
      const params = { jobName: row.jobName }
      killJob(params).then(res => {
        const { state, lastModifiedTime } = res
        row.job.draftStatus = state
        row.lastModifiedTime = lastModifiedTime
        this.$message.success('操作成功！')
        this.btnLoading = false
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        this.btnLoading = false
      })
    },
    handleRefresh(row) {
      const data = [{ jobName: row.jobName }]
      this.tagLoading = true
      queryJobState(data).then(res => {
        const item = res[0]
        row.job.draftStatus = item.state
        row.lastModifiedTime = item.lastModifiedTime
        this.tagLoading = false
      }).catch(res => {
        this.$message.error('操作失败！原因：' + res.message || '暂无')
        this.tagLoading = false
      })
    },
    goBack() {
      this.$router.push({ name: 'PublishList' })
    },
    handlePopoverShow(row) {
      // 还在运行中的草稿，显示弹出窗时自动获取最新状态
      if (['RUNNING', 'ACCEPTED'].includes(row.job.draftStatus)) {
        this.handleRefresh(row)
      }
    },
    handleShowLog(row) {
      this.dialogLabel = '测试任务ID'
      this.$refs.logDialog.show({ jobName: row.jobName })
    },
    handleShowData(row) {
      this.dialogLabel = '测试任务ID'
      this.$refs.dataDialog.show(row)
    }
  }
}
</script>

<style lang="less">
  @import "~assets/css/publish-add.less";
</style>

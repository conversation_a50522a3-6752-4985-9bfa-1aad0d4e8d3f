.publish-list {
  padding: 0 @page-padding-horizontal;
  max-width: @page-max-width;
  .page-header {
    display: flex;
    justify-content: space-between;
    min-height: @page-header;
    align-items: center;
    border-bottom: 1px solid #dcdcdc;
    .header-left {
      .el-form {
        .el-form-item {
          margin: 10px 10px 10px 0;
        }
      }
    }
  }
  .page-main {
    padding: @page-main-padding-vertical 0;
    height: calc(100vh - 80px - @page-header);
    min-height: 200px;
    .table {
      height: calc(100% - 80px);
    }
    .pagination {
      margin-top: 24px;
    }
  }
  .el-input .el-select{
    width: 110px;
  }
}
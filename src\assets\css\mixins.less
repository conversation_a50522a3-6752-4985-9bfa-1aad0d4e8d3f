@import "./vars.less";

.codemirror-wrap-common {
  position: relative;
  i {
    position: absolute;
    right: 10px;
    top: 5px;
    z-index: 100;
    cursor: pointer;
    font-weight: bold;
    color: @color-theme;
    font-size: 14px;
  }
}

// 状态点+颜色
.bids-green-dot,
.bids-blue-dot,
.bids-red-dot {
  position: relative;
  padding-left: 10px;
  &:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #50ba5e;
    transform: translateY(-50%);
  }
}

.bids-blue-dot {
  &:before {
    background-color: #3464e0;
  }
}
.bids-red-dot {
  &:before {
    background-color: #f81d22;
  }
}

// sdc人员选择器或组织选择器弹窗层级覆盖，避免层级低于element，需要手动给组件设置modalClass
.bids-sdc-modal--fix {
  z-index: 3501;
}
<!--
 * @Description: 脱敏规则设置弹窗
 * @Autor: 
 * @LastEditTime: 
-->
<template>
  <div class="desensite-rule-dialog">
    <el-dialog :visible="showDialog" title="脱敏规则设置" width="600px" @close="handleClose">
      <el-form label-width="100px" label-position="left" :model="form" :rules="rules" ref="form" size="small">
        <el-form-item label="可选规则">
          <el-select v-model="form.optionValue" clearable @change="handleChange" style="width:220px;">
            <el-option v-for="item in computedOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="['hide', 'hideEmail'].includes(form.optionValue)">
          <el-form-item label="保留明文">
            前 <el-input-number :min=0 v-model="form.expression[0]" size="mini" style="width:100px;"></el-input-number> 位，
            后 <el-input-number :min=0 v-model="form.expression[1]" size="mini" style="width:100px;"></el-input-number> 位
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="form.expression[2]">保留后的明文替换为码值</el-checkbox>
          </el-form-item>
        </template>
        <el-form-item label="固定值" v-if="form.optionValue === 'replaceToFixed' ">
          <el-input placeholder="请输入" v-model="form.constant" style="width:220px;"></el-input>
        </el-form-item>
        <el-form-item label="取整位数" v-if="['roundUp', 'roundDown'].includes(form.optionValue)">
          后 <el-input-number :min=1 v-model="form.places" size="mini" style="width:100px;"></el-input-number> 位
        </el-form-item>
        <el-form-item label="随机值范围" v-if="['addRandom', 'toRandom'].includes(form.optionValue)">
          下限 <el-input-number :min=1 v-model="form.random[0]" size="mini" style="width:100px;"></el-input-number> ，
          上限 <el-input-number :min=1 v-model="form.random[1]" size="mini" style="width:100px;"></el-input-number>
        </el-form-item>
        <el-form-item label="随机天数范围" v-if="['dateAddRandomDays', 'timestampAddRandomDays'].includes(form.optionValue)">
          下限 <el-input-number :min=0 v-model="form.randomDays[0]" size="mini" style="width:100px;"></el-input-number> 天，
          上限 <el-input-number :min=0 v-model="form.randomDays[1]" size="mini" style="width:100px;"></el-input-number> 天
        </el-form-item>
        <el-form-item label="随机月数范围" v-if="['dateAddRandomMonths', 'timestampAddRandomMonths'].includes(form.optionValue)">
          下限 <el-input-number :min=0 v-model="form.randomMonths[0]" size="mini" style="width:100px;"></el-input-number> 月，
          上限 <el-input-number :min=0 v-model="form.randomMonths[1]" size="mini" style="width:100px;"></el-input-number> 月
        </el-form-item>
        <el-form-item label="归一粒度" v-if="form.optionValue === 'formatDate'">
          <el-radio-group v-model="form.particle[0]">
            <el-radio label="yyyy-01-01">月</el-radio>
            <el-radio label="yyyy-MM-01">日</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="随机枚举" v-if="form.optionValue === 'replaceIn'">
          <el-input placeholder="请输入枚举值(逗号分隔)" v-model="form.enum" style="width:220px;"></el-input>
        </el-form-item>
        <el-form-item label="最终类型" v-if="form.optionValue && form.optionValue !== 'dict_union_convert'">{{form.resultType}}</el-form-item>
        <template v-if="form.optionValue === 'dict_union_convert'">
          <el-form-item label="字典表" prop="dictTableName">
            <!-- <el-select v-model="form.dictTableName" filterable placeholder="请选择" @change="dictTableNameChange">
              <el-option v-for="opt in dictTableOptions" :key="opt" :label="opt" :value="opt"></el-option>
            </el-select> -->
            <el-input v-model="form.dictTableName" placeholder="请输入" @change="dictTableNameChange"></el-input>
          </el-form-item>
          <el-form-item label="过滤条件" prop="">
            <el-input v-model="form.filterSql" placeholder="只输入条件语句（不用写where，在后台封装where语句）"></el-input>
          </el-form-item>
          <el-form-item label="关联字段" prop="fields">
            <el-table :data="form.fields" style="width: 100%" :header-cell-style="{background:'#f5f7f9'}">
              <el-table-column prop="outputField" label="输出字段">
                <template v-slot="{ row, $index }">
                  <el-form-item :prop="`fields[${$index}].outputField`"  :rules="[{ required: true, message: '请选择输出字段', trigger: 'change' }]">
                    <el-select v-model="row.outputField" filterable clearable :disabled="$index === 0" placeholder="请选择" size="mini" style="width: 100%;">
                      <el-option v-for="opt in outputOptions" :key="opt" :label="opt" :value="opt" :disabled="isOptionDisabled(opt, $index)"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column width="35">
                <template>→</template>
              </el-table-column>
              <el-table-column prop="dictionaryField" label="字典字段">
                <template v-slot="{ row, $index }">
                  <el-form-item :prop="`fields[${$index}].dictionaryField`" :rules="[{ required: true, message: '请选择字典字段', trigger: 'change' }]">
                    <el-select v-model="row.dictionaryField" filterable clearable placeholder="请选择" :loading="loading" size="mini" @change="(val) => dictionaryFieldChange(val, row)" style="width: 100%;">
                      <!-- <el-option v-for="item in dictionaryOptions" :key="item.name" :label="item.name" :value="item.name" :disabled="isDictOptionDisabled(item.name, $index)"></el-option> -->
                      <el-option v-for="item in dictionaryOptions" :key="item.name" :label="item.name" :value="item.name"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template v-slot="{ row, $index }">
                  <el-button type='text' @click="handleAdd($index)">添加</el-button>
                  <el-button type='text' @click="handleDelete(row, $index)" :disabled="$index === 0">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <el-button style="margin-right: 235px;" v-show="form.optionValue === 'dict_union_convert'" @click="handleDelAll">删除</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { DataUtil } from 'sdc-core'
import { mapState } from 'vuex'
import { queryAllTables, queryTableColumns } from '@/services/common.service.js'

export default {
  name: 'desensite-rule-dialog',
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    currentRowData: {
      type: [String, Object],
      default: () => ({})
    },
    // 表格数据
    allData: {
      type: Array,
      default: () => []
    },
    // 订阅配置数据
    configData: {
      type: Object,
      default: () => ({})
    }
  },
  model: {
    prop: 'showDialog'
  },
  data() {
    return {
      rowSameTableNameData: [],
      datasource: {},
      // 字典表选项
      dictTableOptions: [],
      // 字典字段选项
      dictionaryOptions: [],
      rules: {
        dictTableName: [
          { required: true, message: '必选', trigger: 'change' }
        ],
        fields: [
          { required: true },
          { validator: this.validateFields, trigger: 'blur' }
        ]
      },
      form: {
        optionValue: '', // 规则
        expression: [1, 0, false], // 保留明文
        constant: '', // 固定值
        places: 1, // 取整位数
        random: [1, 10], // addRandom or toRandom随机值范围随机值范围
        particle: ['yyyy-01-01'], // 归一粒度
        resultType: '', // 最终类型
        enum: '', // 枚举值
        randomDays: [0, 30], // dateAddRandomDays, timestampAddRandomDays随机值范围
        randomMonths: [0, 12], // dateAddRandomMonths, timestampAddRandomMonths随机值范围
        dictTableName: '', // 字典表
        filterSql: '',
        fields: [ // 关联字段
          { outputField: '', dictionaryField: '', type: '', seq: '' }
        ]
      },
      options: [
        { label: '替换星号', value: 'hide', type: /^STRING$/ },
        { label: '替换星号(邮箱)', value: 'hideEmail', type: /^STRING$/ },
        { label: '哈希', value: 'hash', type: 'ALL' },
        { label: '替换为固定值', value: 'replaceToFixed', type: 'ALL' },
        { label: '向上取整', value: 'roundUp', type: /^(TINYINT|SMALLINT|BIGINT|INTEGER|INT|DECIMAL(\((0|[1-9]\d*),(0|[1-9]\d*)\))?|FLOAT|DOUBLE)$/ },
        { label: '向下取整', value: 'roundDown', type: /^(TINYINT|SMALLINT|BIGINT|INTEGER|INT|DECIMAL(\((0|[1-9]\d*),(0|[1-9]\d*)\))?|FLOAT|DOUBLE)$/ },
        { label: '加随机值', value: 'addRandom', type: /^(TINYINT|SMALLINT|BIGINT|INTEGER|INT|DECIMAL(\((0|[1-9]\d*),(0|[1-9]\d*)\))?|FLOAT|DOUBLE)$/ },
        { label: '日期向下取整', value: 'formatDate', type: /^(DATE|TIME|TIMESTAMP(\(\d+\))?)$/ },
        { label: '加密', value: 'FPE', type: /^(STRING|INT|BIGINT|INTEGER|TINYINT|SMALLINT)$/ },
        { label: '替换为null', value: 'replaceToNull', type: 'ALL' },
        { label: '随机替换', value: 'replaceIn', type: 'ALL' },
        { label: '随机数', value: 'toRandom', type: /^(TINYINT|SMALLINT|BIGINT|INTEGER|INT|DECIMAL(\((0|[1-9]\d*),(0|[1-9]\d*)\))?|FLOAT|DOUBLE)$/ },
        { label: '添加随机天数', value: 'dateAddRandomDays', type: /^DATE$/ },
        { label: '添加随机天数', value: 'timestampAddRandomDays', type: /^(TIME|TIMESTAMP(\(\d+\))?)$/ },
        { label: '添加随机月数', value: 'dateAddRandomMonths', type: /^DATE$/ },
        { label: '添加随机月数', value: 'timestampAddRandomMonths', type: /^(TIME|TIMESTAMP(\(\d+\))?)$/ },
        { label: '名字唯一化脱敏', value: 'nameHash', type: /^STRING$/ },
        { label: '字典值同频随机脱敏', value: 'dict_union_convert', type: 'ALL' }
      ],
      enumeration: {
        hide: 'expression',
        hideEmail: 'expression',
        replaceToFixed: 'constant',
        roundUp: 'places',
        roundDown: 'places',
        addRandom: 'random',
        formatDate: 'particle',
        replaceIn: 'enum',
        toRandom: 'random',
        dateAddRandomDays: 'randomDays',
        timestampAddRandomDays: 'randomDays',
        dateAddRandomMonths: 'randomMonths',
        timestampAddRandomMonths: 'randomMonths'
      },
      loading: false,
      allDelData: []
    }
  },
  watch: {
    showDialog: {
      async handler(value) {
        if (!value) return
        const { convert } = DataUtil.clone(this.currentRowData)
        if (!convert) return
        const { name, params, resultType } = convert
        this.form.optionValue = name
        this.form.resultType = resultType
        if (!params) return
        // 数据回显, params是数组
        if (['replaceToFixed', 'roundUp', 'roundDown'].includes(name)) {
          this.form[this.enumeration[name]] = params[0]
        } else if (name === 'replaceIn') {
          this.form[this.enumeration[name]] = params.join(',')
        } else if (name === 'dict_union_convert') {
          // params = [dictDatasoruceid,dicttablename,dicttcolumnname,seq,filterSql]
          this.form.dictTableName = convert.params[1]
          this.form.filterSql = convert.params[4]
          
          await this.getTables()
          this.dictTableNameChange(convert.params[1], false)
          this.rowSameTableNameData = DataUtil.clone(this.getSameDictTableNameData(convert.params[1]))
        } else {
          if (['hide', 'hideEmail'].includes(name)) { 
            const [,, checkedNum] = params
            params[2] = checkedNum === '1' // '1'是勾选true, '0'是未勾选false
          }
          this.form[this.enumeration[name]] = params
        }
      }
    }
  },
  computed: {
    ...mapState({
      publishConfig: state => state.dataToBeSub
    }),
    defaultPubConfig() {
      return this.publishConfig[0]
    },
    computedOptions() {
      return this.options.filter((item) => {
        return (
          item.type === 'ALL' || (this.currentRowData.type && item.type.test(this.currentRowData.type.replace(/\s+/g, '')))
        )
      })
    },
    outputOptions() {
      return this.allData.map(item => item.name)
    },
    // 已经设置了字典值同频随机脱敏的字段
    hasSetRuleFields() {
      // 当前记录的输出字段列表
      const currentOutputFields = this.rowSameTableNameData.map(item => item.outputField)
      
      return this.allData
        .filter(item => 
          item.convert?.name === 'dict_union_convert' && 
          (this.rowSameTableNameData.length === 0 || !currentOutputFields.includes(item.name))
        )
        .map(item => item.name)
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false)
      this.handleChange()
    },
    async handleConfirm() {
      const name = this.form.optionValue
      if (!name) {
        this.$emit('UpdateDesensite', null)
        this.handleClose()
        return
      }
      const valid = await this.validateForm()
      if (!valid) return
      const label = this.options.find((item) => item.value === name).label
      let params = []
      const _array = ['hide', 'hideEmail', 'addRandom', 'formatDate', 'toRandom', 'dateAddRandomDays', 'timestampAddRandomDays', 'dateAddRandomMonths', 'timestampAddRandomMonths']
      if (_array.includes(name)) {
        if (['hide', 'hideEmail'].includes(name)) { 
          const [,, checked] = this.form[this.enumeration[name]] // 传参1是勾选, 0是未勾选
          this.form[this.enumeration[name]][2] = checked ? 1 : 0
        }
        // 表单数据->number[]转为String[]
        params = this.form[this.enumeration[name]].map(String)
      } else if (['replaceToFixed', 'roundUp', 'roundDown'].includes(name)) {
        // 单个数字转为String[]
        params.push('' + this.form[this.enumeration[name]])
      } else if (name === 'replaceIn') {
        // 输入的字符串如'1,2,3' 转为数组['1','2','3']
        params = this.form.enum.split(',')
      } else {
        // 无输入
        params = null
      }
      const resultType = this.DetermineType(name)
      const convert = { name, label, params, resultType }

      if (name === 'dict_union_convert') {
        this.form.fields.forEach(field => {
          this.allData.forEach(item => {
            if (item.name === field.outputField) {
              Object.assign(convert, { resultType: field.type, params: [this.datasource.datasourceId, this.form.dictTableName, field.dictionaryField, field.seq, this.form.filterSql] })
              this.$set(item, 'convert', DataUtil.clone(convert))
            }
          })
        })
        const originField = this.rowSameTableNameData.map(item => item.outputField)
        const currentField = this.form.fields.map(item => item.outputField)
        const changeField = originField.filter(item => !currentField.includes(item))
        if (changeField.length) {
          changeField.forEach(field => {
            this.allData.forEach(item => {
              if (item.name === field && item.convert?.name === 'dict_union_convert') {
                this.$set(item, 'convert', null)
              }
            })
          })
        }
        this.$emit('emitData')
      } else {
        this.$emit('UpdateDesensite', convert)
      }
      this.handleClose()
    },
    handleChange(value) {
      const resultType = this.DetermineType(value)
      const expression = value === 'hideEmail' ? [1, 1, false] : [1, 0, false] // hideEmail or hide类型的默认值
      const random = value === 'toRandom' ? [1, 1000] : [1, 10] // toRandom or addRandom类型的默认值
      this.form = {
        optionValue: value, // 规则
        expression, // 保留明文
        constant: '', // 固定值
        places: 1, // 取整位数
        random,
        particle: ['yyyy-01-01'], // 归一粒度
        resultType, // 最终类型
        enum: '', // 枚举值
        randomDays: [0, 30], // dateAddRandomDays, timestampAddRandomDays随机值范围
        randomMonths: [0, 12], // dateAddRandomMonths, timestampAddRandomMonths随机值范围
        dictTableName: '',
        filterSql: '',
        fields: [
          { outputField: value === 'dict_union_convert' ? this.currentRowData.name : '', dictionaryField: '', type: '', seq: '' }
        ]
      }
      this.$refs.form.clearValidate()

      if (value === 'dict_union_convert') {
        this.getTables()
      }
    },
    // 决定返回的最终类型, 传入option的值
    DetermineType(name) {
      let resultType = this.currentRowData.type
      if (name === 'hash') {
        const _res = ['STRING', 'TINYINT', 'SMLLINT', 'INTEGER', 'BIGINT'].includes(resultType)
        if (!_res) resultType = 'INTEGER'
      } else if (['formatDate', 'timestampAddRandomDays', 'timestampAddRandomMonths'].includes(name)) {
        resultType = 'TIMESTAMP'
      } else if (['roundUp', 'roundDown'].includes(name)) {
        resultType = 'BIGINT'
      } else if (['hide', 'hideEmail'].includes(name)) {
        resultType = 'STRING'
      } else {}
      return resultType
    },
    async validateForm() {
      let valid = false
      this.$refs.form.validate((res) => {
        if (res) {
          valid = true
        }
      })
      return valid
    },
    validateFields(rule, value, callback) {
      let isValid = true
      this.form.fields.forEach(field => {
        if (!field.outputField || !field.dictionaryField) {
          isValid = false
        }
      })
      if (!isValid) {
        callback(new Error('所有字段都必须选择'))
      } else {
        callback()
      }
    },
    handleAdd(index) {
      const newRow = { outputField: '', dictionaryField: '', type: '', seq: '' }
      this.form.fields.splice(index + 1, 0, newRow)
      this.$nextTick(() => this.$refs.form.clearValidate())
    },
    handleDelete(row, index) {
      this.form.fields.splice(index, 1)
      if (row.outputField === '') {
        return
      }
      const allOutPutField = this.allDelData.map(item => item.outputField)
      if (allOutPutField.includes(row.outputField)) {
        return
      }
      this.allDelData.push(row)
    },
    handleDelAll() {
      this.$confirm('删除后不可恢复', '确定删除脱敏规则「字典值同频随机脱敏」', {
        confirmButtonText: '确认', cancelButtonText: '取消', type: 'warning'
      }).then(() => {
        this.form.fields.forEach(field => {
          this.allData.forEach(item => {
            if (item.name === field.outputField) {
              this.$set(item, 'convert', null)
            }
          })
        })
        this.$emit('emitData')
        this.$nextTick(() => this.handleClose())
      }).catch(() => { })
    },
    // 判断选项是否应该禁用
    isOptionDisabled(optionValue, currentSelectIndex) {
      // 当前选择器已选的值不禁用
      if (optionValue === this.form.fields[currentSelectIndex]?.outputField) {
        return false
      }
      
      // 已设置规则的值禁用
      if (this.hasSetRuleFields.includes(optionValue)) {
        return true
      }
      
      // 检查该值是否被其他选择器选中
      return this.form.fields.some((select, index) => {
        return index !== currentSelectIndex && select.outputField === optionValue
      })
    },
    isDictOptionDisabled(optionValue, currentSelectIndex) {
      // 当前选择器已选的值不禁用
      if (optionValue === this.form.fields[currentSelectIndex]?.dictionaryField) {
        return false
      }
      
      // 检查该值是否被其他选择器选中
      return this.form.fields.some((select, index) => {
        return index !== currentSelectIndex && select.dictionaryField === optionValue
      })
    },
    async getTables() {
      const res = await queryAllTables({ pubConfigId: this.defaultPubConfig.configId, pubConfigVersion: this.defaultPubConfig.version })
      this.dictTableOptions = res?.tables
      this.datasource = res
    },
    dictionaryFieldChange (value, row) {
      if (value) {
        const target = this.dictionaryOptions.find(item => item.name === value)
        row.type = target.type
        row.seq = target.seq
        return
      }
      row.type = ''
      row.seq = ''
    },
    getSameDictTableNameData (dictTableName) {
      const array = this.allData.reduce((prev, item) => {
        if (item.convert?.name === 'dict_union_convert' && item.convert.params[1] === dictTableName) {
          const obj = {
            outputField: item.name,
            dictionaryField: item.convert.params[2],
            type: item.convert.resultType,
            seq: item.convert.params[3]
          }
          prev.push(obj)
        }
        return prev
      }, [])
      return array
    },
    dictTableNameChange (dictTableName, changeFilterSql = true) {
      if (changeFilterSql) {
        this.form.filterSql = ''
      }
      const array = this.getSameDictTableNameData(dictTableName)
      if (array.length === 0) {
        array.push({ outputField: this.form.optionValue === 'dict_union_convert' ? this.currentRowData.name : '', dictionaryField: '', type: '', seq: '' })
      }
      this.form.fields = array

      this.loading = true
      queryTableColumns({ datasourceId: this.datasource.datasourceId, tablename: dictTableName }).then(res => {
        this.dictionaryOptions = res
      }).catch(() => { 
        this.dictionaryOptions = []
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .el-table {
  thead {
    th {
      padding: 0;
    }
  }
  tbody {
    td {
      padding: 5px 0;
    }
  }
  .el-form-item {
    margin: 0;
  }
}
</style>
